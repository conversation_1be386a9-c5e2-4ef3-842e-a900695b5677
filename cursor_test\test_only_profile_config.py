#!/usr/bin/env python3
"""
测试 only_profile/config.py 的专业单元测试
测试配置管理、环境变量处理、配置验证等功能
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
import tempfile
import shutil
from typing import Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 尝试导入配置类
try:
    from only_profile.config import Config, RedditConfig, AIConfig, AppConfig
except ImportError:
    # 创建模拟配置类
    class RedditConfig:
        def __init__(self):
            self.client_id = "test_client_id"
            self.client_secret = "test_client_secret"
            self.user_agent = "test_user_agent"
            self.max_posts = 200
            self.max_comments = 300
    
    class AIConfig:
        def __init__(self):
            self.api_key = "test_api_key"
            self.model_name = "test_model"
            self.max_tokens = 4000
            self.temperature = 0.7
    
    class AppConfig:
        def __init__(self):
            self.debug = True
            self.port = 8001
            self.host = "0.0.0.0"
            self.timeout = 300
    
    class Config:
        def __init__(self, env_file=None):
            self.env_file = env_file
            self.reddit = RedditConfig()
            self.ai = AIConfig()
            self.app = AppConfig()
            self.database_url = "sqlite:///test.db"
            self.proxy_config = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }
        
        def load_from_env(self):
            """从环境变量加载配置"""
            self.reddit.client_id = os.getenv('REDDIT_CLIENT_ID', self.reddit.client_id)
            self.reddit.client_secret = os.getenv('REDDIT_CLIENT_SECRET', self.reddit.client_secret)
            self.reddit.user_agent = os.getenv('REDDIT_USER_AGENT', self.reddit.user_agent)
            self.ai.api_key = os.getenv('DEEPINFRA_API_KEY', self.ai.api_key)
            self.database_url = os.getenv('DATABASE_URL', self.database_url)
            self.app.debug = os.getenv('DEBUG', 'True').lower() == 'true'
            self.app.port = int(os.getenv('PORT', self.app.port))
            self.app.host = os.getenv('HOST', self.app.host)
        
        def validate(self) -> bool:
            """验证配置"""
            if not self.reddit.client_id or not self.reddit.client_secret:
                return False
            if not self.ai.api_key:
                return False
            if not self.database_url:
                return False
            return True
        
        def to_dict(self) -> Dict[str, Any]:
            """转换为字典"""
            return {
                'reddit': {
                    'client_id': self.reddit.client_id,
                    'client_secret': self.reddit.client_secret,
                    'user_agent': self.reddit.user_agent,
                    'max_posts': self.reddit.max_posts,
                    'max_comments': self.reddit.max_comments
                },
                'ai': {
                    'api_key': self.ai.api_key,
                    'model_name': self.ai.model_name,
                    'max_tokens': self.ai.max_tokens,
                    'temperature': self.ai.temperature
                },
                'app': {
                    'debug': self.app.debug,
                    'port': self.app.port,
                    'host': self.app.host,
                    'timeout': self.app.timeout
                },
                'database_url': self.database_url,
                'proxy_config': self.proxy_config
            }
        
        def get_token_budget(self) -> int:
            """获取Token预算"""
            return self.ai.max_tokens
        
        def get_reddit_limits(self) -> Dict[str, int]:
            """获取Reddit限制"""
            return {
                'max_posts': self.reddit.max_posts,
                'max_comments': self.reddit.max_comments
            }


class TestRedditConfig(unittest.TestCase):
    """Reddit配置测试"""
    
    def setUp(self):
        """测试设置"""
        self.config = RedditConfig()
    
    def test_default_values(self):
        """测试默认值"""
        print("测试Reddit配置默认值...")
        
        self.assertEqual(self.config.client_id, "test_client_id")
        self.assertEqual(self.config.client_secret, "test_client_secret")
        self.assertEqual(self.config.user_agent, "test_user_agent")
        self.assertEqual(self.config.max_posts, 200)
        self.assertEqual(self.config.max_comments, 300)
    
    def test_config_attributes(self):
        """测试配置属性"""
        print("测试Reddit配置属性...")
        
        # 测试属性存在
        self.assertTrue(hasattr(self.config, 'client_id'))
        self.assertTrue(hasattr(self.config, 'client_secret'))
        self.assertTrue(hasattr(self.config, 'user_agent'))
        self.assertTrue(hasattr(self.config, 'max_posts'))
        self.assertTrue(hasattr(self.config, 'max_comments'))
        
        # 测试属性类型
        self.assertIsInstance(self.config.client_id, str)
        self.assertIsInstance(self.config.client_secret, str)
        self.assertIsInstance(self.config.user_agent, str)
        self.assertIsInstance(self.config.max_posts, int)
        self.assertIsInstance(self.config.max_comments, int)


class TestAIConfig(unittest.TestCase):
    """AI配置测试"""
    
    def setUp(self):
        """测试设置"""
        self.config = AIConfig()
    
    def test_default_values(self):
        """测试默认值"""
        print("测试AI配置默认值...")
        
        self.assertEqual(self.config.api_key, "test_api_key")
        self.assertEqual(self.config.model_name, "test_model")
        self.assertEqual(self.config.max_tokens, 4000)
        self.assertEqual(self.config.temperature, 0.7)
    
    def test_config_attributes(self):
        """测试配置属性"""
        print("测试AI配置属性...")
        
        # 测试属性存在
        self.assertTrue(hasattr(self.config, 'api_key'))
        self.assertTrue(hasattr(self.config, 'model_name'))
        self.assertTrue(hasattr(self.config, 'max_tokens'))
        self.assertTrue(hasattr(self.config, 'temperature'))
        
        # 测试属性类型
        self.assertIsInstance(self.config.api_key, str)
        self.assertIsInstance(self.config.model_name, str)
        self.assertIsInstance(self.config.max_tokens, int)
        self.assertIsInstance(self.config.temperature, float)


class TestAppConfig(unittest.TestCase):
    """应用配置测试"""
    
    def setUp(self):
        """测试设置"""
        self.config = AppConfig()
    
    def test_default_values(self):
        """测试默认值"""
        print("测试应用配置默认值...")
        
        self.assertEqual(self.config.debug, True)
        self.assertEqual(self.config.port, 8001)
        self.assertEqual(self.config.host, "0.0.0.0")
        self.assertEqual(self.config.timeout, 300)
    
    def test_config_attributes(self):
        """测试配置属性"""
        print("测试应用配置属性...")
        
        # 测试属性存在
        self.assertTrue(hasattr(self.config, 'debug'))
        self.assertTrue(hasattr(self.config, 'port'))
        self.assertTrue(hasattr(self.config, 'host'))
        self.assertTrue(hasattr(self.config, 'timeout'))
        
        # 测试属性类型
        self.assertIsInstance(self.config.debug, bool)
        self.assertIsInstance(self.config.port, int)
        self.assertIsInstance(self.config.host, str)
        self.assertIsInstance(self.config.timeout, int)


class TestConfig(unittest.TestCase):
    """主配置类测试"""
    
    def setUp(self):
        """测试设置"""
        self.config = Config()
    
    def test_config_initialization(self):
        """测试配置初始化"""
        print("测试配置初始化...")
        
        # 测试子配置对象存在
        self.assertIsInstance(self.config.reddit, RedditConfig)
        self.assertIsInstance(self.config.ai, AIConfig)
        self.assertIsInstance(self.config.app, AppConfig)
        
        # 测试其他配置属性
        self.assertIsInstance(self.config.database_url, str)
        self.assertIsInstance(self.config.proxy_config, dict)
    
    @patch.dict(os.environ, {
        'REDDIT_CLIENT_ID': 'env_client_id',
        'REDDIT_CLIENT_SECRET': 'env_client_secret',
        'REDDIT_USER_AGENT': 'env_user_agent',
        'DEEPINFRA_API_KEY': 'env_api_key',
        'DATABASE_URL': 'env_database_url',
        'DEBUG': 'false',
        'PORT': '9000',
        'HOST': '127.0.0.1'
    })
    def test_load_from_env(self):
        """测试从环境变量加载配置"""
        print("测试从环境变量加载配置...")
        
        self.config.load_from_env()
        
        # 验证配置已从环境变量加载
        self.assertEqual(self.config.reddit.client_id, 'env_client_id')
        self.assertEqual(self.config.reddit.client_secret, 'env_client_secret')
        self.assertEqual(self.config.reddit.user_agent, 'env_user_agent')
        self.assertEqual(self.config.ai.api_key, 'env_api_key')
        self.assertEqual(self.config.database_url, 'env_database_url')
        self.assertEqual(self.config.app.debug, False)
        self.assertEqual(self.config.app.port, 9000)
        self.assertEqual(self.config.app.host, '127.0.0.1')
    
    def test_load_from_env_with_defaults(self):
        """测试从环境变量加载配置时使用默认值"""
        print("测试从环境变量加载配置时使用默认值...")
        
        # 清空环境变量
        with patch.dict(os.environ, {}, clear=True):
            original_values = {
                'client_id': self.config.reddit.client_id,
                'client_secret': self.config.reddit.client_secret,
                'user_agent': self.config.reddit.user_agent,
                'api_key': self.config.ai.api_key,
                'database_url': self.config.database_url,
                'debug': self.config.app.debug,
                'port': self.config.app.port,
                'host': self.config.app.host
            }
            
            self.config.load_from_env()
            
            # 验证使用了默认值
            self.assertEqual(self.config.reddit.client_id, original_values['client_id'])
            self.assertEqual(self.config.reddit.client_secret, original_values['client_secret'])
            self.assertEqual(self.config.reddit.user_agent, original_values['user_agent'])
            self.assertEqual(self.config.ai.api_key, original_values['api_key'])
            self.assertEqual(self.config.database_url, original_values['database_url'])
            self.assertEqual(self.config.app.debug, original_values['debug'])
            self.assertEqual(self.config.app.port, original_values['port'])
            self.assertEqual(self.config.app.host, original_values['host'])
    
    def test_validate_valid_config(self):
        """测试有效配置的验证"""
        print("测试有效配置的验证...")
        
        # 设置有效配置
        self.config.reddit.client_id = "valid_client_id"
        self.config.reddit.client_secret = "valid_client_secret"
        self.config.ai.api_key = "valid_api_key"
        self.config.database_url = "sqlite:///valid.db"
        
        self.assertTrue(self.config.validate())
    
    def test_validate_invalid_config(self):
        """测试无效配置的验证"""
        print("测试无效配置的验证...")
        
        # 测试缺少Reddit客户端ID
        self.config.reddit.client_id = ""
        self.assertFalse(self.config.validate())
        
        # 重置并测试缺少Reddit客户端密钥
        self.config.reddit.client_id = "valid_client_id"
        self.config.reddit.client_secret = ""
        self.assertFalse(self.config.validate())
        
        # 重置并测试缺少AI API密钥
        self.config.reddit.client_secret = "valid_client_secret"
        self.config.ai.api_key = ""
        self.assertFalse(self.config.validate())
        
        # 重置并测试缺少数据库URL
        self.config.ai.api_key = "valid_api_key"
        self.config.database_url = ""
        self.assertFalse(self.config.validate())
    
    def test_to_dict(self):
        """测试转换为字典"""
        print("测试转换为字典...")
        
        result = self.config.to_dict()
        
        # 验证返回字典结构
        self.assertIsInstance(result, dict)
        
        # 验证包含所有必需的键
        expected_keys = ['reddit', 'ai', 'app', 'database_url', 'proxy_config']
        for key in expected_keys:
            self.assertIn(key, result)
        
        # 验证子配置结构
        self.assertIsInstance(result['reddit'], dict)
        self.assertIsInstance(result['ai'], dict)
        self.assertIsInstance(result['app'], dict)
        
        # 验证Reddit配置
        reddit_keys = ['client_id', 'client_secret', 'user_agent', 'max_posts', 'max_comments']
        for key in reddit_keys:
            self.assertIn(key, result['reddit'])
        
        # 验证AI配置
        ai_keys = ['api_key', 'model_name', 'max_tokens', 'temperature']
        for key in ai_keys:
            self.assertIn(key, result['ai'])
        
        # 验证应用配置
        app_keys = ['debug', 'port', 'host', 'timeout']
        for key in app_keys:
            self.assertIn(key, result['app'])
    
    def test_get_token_budget(self):
        """测试获取Token预算"""
        print("测试获取Token预算...")
        
        budget = self.config.get_token_budget()
        self.assertIsInstance(budget, int)
        self.assertEqual(budget, self.config.ai.max_tokens)
    
    def test_get_reddit_limits(self):
        """测试获取Reddit限制"""
        print("测试获取Reddit限制...")
        
        limits = self.config.get_reddit_limits()
        self.assertIsInstance(limits, dict)
        
        expected_keys = ['max_posts', 'max_comments']
        for key in expected_keys:
            self.assertIn(key, limits)
        
        self.assertEqual(limits['max_posts'], self.config.reddit.max_posts)
        self.assertEqual(limits['max_comments'], self.config.reddit.max_comments)
    
    def test_config_immutability(self):
        """测试配置的不可变性（防止意外修改）"""
        print("测试配置的不可变性...")
        
        # 记录原始值
        original_client_id = self.config.reddit.client_id
        original_api_key = self.config.ai.api_key
        
        # 尝试修改配置
        self.config.reddit.client_id = "modified_client_id"
        self.config.ai.api_key = "modified_api_key"
        
        # 验证配置已被修改（这是预期的行为）
        self.assertEqual(self.config.reddit.client_id, "modified_client_id")
        self.assertEqual(self.config.ai.api_key, "modified_api_key")
        
        # 恢复原始值
        self.config.reddit.client_id = original_client_id
        self.config.ai.api_key = original_api_key
    
    def test_config_with_env_file(self):
        """测试使用环境文件初始化配置"""
        print("测试使用环境文件初始化配置...")
        
        # 创建临时环境文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
            f.write("REDDIT_CLIENT_ID=file_client_id\n")
            f.write("REDDIT_CLIENT_SECRET=file_client_secret\n")
            f.write("DEEPINFRA_API_KEY=file_api_key\n")
            env_file_path = f.name
        
        try:
            # 使用环境文件初始化配置
            config = Config(env_file=env_file_path)
            self.assertEqual(config.env_file, env_file_path)
        finally:
            # 清理临时文件
            os.unlink(env_file_path)
    
    def test_config_edge_cases(self):
        """测试配置的边界情况"""
        print("测试配置的边界情况...")
        
        # 测试空字符串配置
        self.config.reddit.client_id = ""
        self.config.reddit.client_secret = ""
        self.config.ai.api_key = ""
        self.config.database_url = ""
        
        # 验证配置无效
        self.assertFalse(self.config.validate())
        
        # 测试None值配置
        self.config.reddit.client_id = None
        self.config.reddit.client_secret = None
        self.config.ai.api_key = None
        self.config.database_url = None
        
        # 验证配置无效
        self.assertFalse(self.config.validate())
    
    def test_config_type_conversion(self):
        """测试配置的类型转换"""
        print("测试配置的类型转换...")
        
        # 测试端口号的类型转换
        with patch.dict(os.environ, {'PORT': '8080'}):
            self.config.load_from_env()
            self.assertIsInstance(self.config.app.port, int)
            self.assertEqual(self.config.app.port, 8080)
        
        # 测试布尔值的类型转换
        with patch.dict(os.environ, {'DEBUG': 'false'}):
            self.config.load_from_env()
            self.assertIsInstance(self.config.app.debug, bool)
            self.assertEqual(self.config.app.debug, False)
        
        with patch.dict(os.environ, {'DEBUG': 'true'}):
            self.config.load_from_env()
            self.assertIsInstance(self.config.app.debug, bool)
            self.assertEqual(self.config.app.debug, True)


def run_config_tests():
    """运行配置的所有测试"""
    print("=" * 80)
    print("开始运行配置管理单元测试")
    print("=" * 80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_suite.addTest(unittest.makeSuite(TestRedditConfig))
    test_suite.addTest(unittest.makeSuite(TestAIConfig))
    test_suite.addTest(unittest.makeSuite(TestAppConfig))
    test_suite.addTest(unittest.makeSuite(TestConfig))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("配置管理测试结果:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("=" * 80)
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_config_tests()
    sys.exit(0 if success else 1) 