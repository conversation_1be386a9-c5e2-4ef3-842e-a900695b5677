"""快速时间线优化测试"""

import asyncio
import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.pipeline import RedditResonancePipeline

async def main():
    print("🧪 快速时间线优化测试")
    
    pipeline = RedditResonancePipeline()
    
    # 创建测试数据
    test_data = {
        'posts': [{
            'title': '创业困惑',
            'text': '最近在思考创业方向，很迷茫',
            'timestamp': datetime(2023, 1, 15),
            'score': 10
        }],
        'comments': [{
            'text': '后来经过深入思考，我觉得应该先积累更多经验',
            'timestamp': datetime(2023, 6, 20),
            'score': 15
        }]
    }
    
    # 测试格式化
    formatted = pipeline._format_contents_with_timeline(test_data)
    print(f"格式化内容: {len(formatted)} 条")
    for content in formatted:
        print(f"  - {content}")
    
    # 测试质量选择
    selected = pipeline._select_quality_temporal_contents(formatted)
    print(f"\n选择内容: {len(selected)} 条")
    
    # 测试图谱构建
    try:
        if pipeline.graph_builder.ai_service:
            graph = await pipeline.graph_builder.build_user_graph(
                contents=selected,
                user_context="时间线测试用户"
            )
            print(f"\n图谱结果: {len(graph.nodes)} 节点, {len(graph.edges)} 边")
            
            # 显示所有边的证据
            for i, edge in enumerate(graph.edges):
                print(f"  边 {i+1}: {edge.relation_type.value}")
                print(f"    证据: {edge.evidence}")
        else:
            print("AI服务不可用")
    except Exception as e:
        print(f"构建失败: {e}")
    
    print("🎉 测试完成")

if __name__ == "__main__":
    asyncio.run(main()) 