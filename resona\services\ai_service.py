"""
AI 服务 - 使用 Lang<PERSON>hain 进行结构化输出和错误处理
"""
import logging
from typing import List, Dict, Any, Optional
import numpy as np
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain.output_parsers import OutputFixingParser
from langchain_openai import ChatOpenAI
from langchain_core.runnables import RunnablePassthrough
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential
import re

from ..config import settings
from ..models import QueryProfile, RedditUser, RedditPost, UserQuery, MatchedUser
from ..models.ai_response_models import (
    QueryAnalysis, ResonanceSummary, PrivateMessage, 
    TranslationResult, RankingResult, EmotionType, ExpressionStyle,
    SemanticAnalysisResult, GraphAnalysisResult, ClarifyingQuestionsResult
)
from ..prompts import (
    SEMANTIC_ANALYSIS_PROMPT_TEMPLATE,
    GRAPH_ANALYSIS_PROMPT_TEMPLATE,
    CLARIFYING_QUESTIONS_PROMPT_TEMPLATE
)

logger = logging.getLogger(__name__)

# 设置 httpx 日志级别，过滤掉 200 OK 等正常请求日志
logging.getLogger("httpx").setLevel(logging.WARNING)

class AIService:
    """AI 服务类 - 使用 LangChain 进行可靠的 LLM 调用和 JSON 解析"""
    
    def __init__(self):
        """初始化 AI 服务"""
        self.api_key = settings.deepinfra_api_key
        self.base_url = settings.deepinfra_base_url
        
        # 获取当前模型配置
        model_config = settings.MODEL_CONFIGS.get(settings.llm_model, settings.MODEL_CONFIGS["default"])
        
        # 构建 LLM 初始化参数
        llm_kwargs = {
            "model": settings.llm_model,
            "api_key": self.api_key,
            "base_url": self.base_url,
            "timeout": 60.0,
            "max_retries": 3
        }
        
        # 根据模型配置设置参数
        if model_config.get("supports_json_mode", False):
            # 启用 JSON 模式以消除 <think> 标签问题
            llm_kwargs.setdefault("model_kwargs", {})["response_format"] = {"type": "json_object"}
            logger.info(f"为模型 {settings.llm_model} 启用 JSON 模式 (model_kwargs)")
        
        # 使用模型推荐的温度参数
        llm_kwargs["temperature"] = model_config.get("recommended_temperature", 0.3)
        
        # 使用模型推荐的最大输出token数
        llm_kwargs["max_tokens"] = model_config.get("max_output_tokens", 1500)
        
        # 初始化 LangChain LLM
        self.llm = ChatOpenAI(**llm_kwargs)
        
        logger.info(f"AI 服务初始化完成:")
        logger.info(f"  - 模型: {settings.llm_model}")
        logger.info(f"  - 描述: {model_config.get('description', '未知模型')}")
        logger.info(f"  - JSON 模式: {'启用' if model_config.get('supports_json_mode', False) else '禁用'}")
        logger.info(f"  - 温度: {llm_kwargs['temperature']}")
        logger.info(f"  - 最大输出: {llm_kwargs['max_tokens']}")
        
        # 设置embedding API客户端（保持原有方式，因为LangChain的embedding可能有兼容性问题）
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(
                connect=10.0,
                read=60.0,
                write=10.0,
                pool=5.0
            )
        )
        
        # 初始化输出解析器
        self.query_parser = PydanticOutputParser(pydantic_object=QueryAnalysis)
        self.resonance_parser = PydanticOutputParser(pydantic_object=ResonanceSummary)
        self.message_parser = PydanticOutputParser(pydantic_object=PrivateMessage)
        self.translation_parser = PydanticOutputParser(pydantic_object=TranslationResult)
        self.ranking_parser = PydanticOutputParser(pydantic_object=RankingResult)
        
        # 创建查询分析链
        self._setup_query_analysis_chain()
        
        # 创建其他处理链
        self._setup_other_chains()
        
        # 新增：创建扩展的处理链
        self._setup_extended_chains()
    
    def _setup_query_analysis_chain(self):
        """设置查询分析链"""
        query_prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个专业的心理分析助手，擅长理解用户的情绪和需求。请严格按照指定的JSON格式返回分析结果。"),
            ("human", """分析以下用户的困扰描述，提取关键信息。

用户输入："{query_text}"

请分析并返回以下信息：
- main_topics: 主要话题，如"职业选择"、"人际关系"等（英文单词或短语）
- emotions: 情绪标签，只能从以下选项中选择：{emotions}
- expression_style: 表达风格，只能从以下选项中选择：{styles}
- intent_summary: 一句话总结用户的核心困扰和需求

{format_instructions}""")
        ])
        
        self.query_analysis_chain = (
            {
                "query_text": RunnablePassthrough(),
                "emotions": lambda _: ", ".join([e.value for e in EmotionType]),
                "styles": lambda _: ", ".join([s.value for s in ExpressionStyle]),
                "format_instructions": lambda _: self.query_parser.get_format_instructions()
            }
            | query_prompt
            | self.llm
            | self.query_parser
        )
    
    def _setup_other_chains(self):
        """设置其他处理链"""
        # 共鸣摘要链
        resonance_prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个善于发现人与人之间共鸣点的助手。"),
            ("human", """基于以下信息，生成一段共鸣摘要，说明为什么推荐这个用户：

用户困扰：{query_text}
用户主要情绪：{emotions}

推荐用户：{username}
该用户的相关帖子：
{posts_text}

请用1-2句话说明这个用户为什么会与查询用户产生共鸣，重点突出相似的经历或感受。

{format_instructions}""")
        ])
        
        self.resonance_chain = (
            resonance_prompt
            | self.llm
            | self.resonance_parser
        )
        
        # 私信生成链
        message_prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个帮助人们建立真诚联系的助手。"),
            ("human", """基于以下信息，生成一条真诚的私信开场白：

我的困扰：{query_text}
对方用户：{username}
共鸣点：{resonance_summary}

要求：
1. 语气真诚、自然，不要太正式
2. 简短提及自己的困扰
3. 说明为什么觉得对方可能理解
4. 表达想要交流的愿望
5. 控制在50-100字

{format_instructions}""")
        ])
        
        self.message_chain = (
            message_prompt
            | self.llm
            | self.message_parser
        )
        
        # 翻译链
        translation_prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个专业的中英翻译助手。"),
            ("human", """请将以下中文文本翻译成英文，保持原意和语气：

{text}

{format_instructions}""")
        ])
        
        self.translation_chain = (
            translation_prompt
            | self.llm
            | self.translation_parser
        )
    
    def _setup_extended_chains(self):
        """设置扩展的处理链 - 用于语义分析、图谱构建等"""
        # ============== 语义分析链 ==============
        semantic_parser = PydanticOutputParser(pydantic_object=SemanticAnalysisResult)
        semantic_prompt = ChatPromptTemplate.from_template(
            template=SEMANTIC_ANALYSIS_PROMPT_TEMPLATE
        )
        self.semantic_analysis_chain = (
            {
                "query_text": RunnablePassthrough(),
                "format_instructions": lambda _: semantic_parser.get_format_instructions()
            }
            | semantic_prompt
            | self.llm
            | semantic_parser
        )

        # ============== 图谱分析链 ==============
        graph_parser = PydanticOutputParser(pydantic_object=GraphAnalysisResult)
        graph_prompt = ChatPromptTemplate.from_template(
            template=GRAPH_ANALYSIS_PROMPT_TEMPLATE
        )
        self.graph_analysis_chain = (
            {
                "content": lambda x: x.get("content", ""),
                "context_info": lambda x: f"上下文信息：{x.get('context', '')}" if x.get('context') else "",
                "format_instructions": lambda _: graph_parser.get_format_instructions()
            }
            | graph_prompt
            | self.llm
            | graph_parser
        )

        # ============== 追问问题生成链 ==============
        questions_parser = PydanticOutputParser(pydantic_object=ClarifyingQuestionsResult)
        questions_prompt = ChatPromptTemplate.from_template(
            template=CLARIFYING_QUESTIONS_PROMPT_TEMPLATE
        )
        self.clarifying_questions_chain = (
            {
                "num_questions": lambda x: x.get("num_questions", 3),
                "original_query": lambda x: x.get("original_query", ""),
                "completeness_score": lambda x: x.get("completeness_score", 0.0),
                "node_counts": lambda x: str(x.get("node_counts", {})),
                "key_beliefs": lambda x: str(x.get("key_beliefs", [])),
                "emotional_patterns": lambda x: str(x.get("emotional_patterns", [])),
                "missing_dimensions": lambda x: str(x.get("missing_dimensions", [])),
                "format_instructions": lambda _: questions_parser.get_format_instructions()
            }
            | questions_prompt
            | self.llm
            | questions_parser
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def get_embedding(self, text: str) -> List[float]:
        """
        获取文本的 embedding 向量（保持原有实现）
        
        Args:
            text: 输入文本
            
        Returns:
            embedding 向量
        """
        try:
            response = await self.client.post(
                f"{self.base_url}/embeddings",
                headers=self.headers,
                json={
                    "model": settings.embedding_model,
                    "input": text,
                    "dimensions": settings.embedding_dimension
                }
            )
            response.raise_for_status()
            
            data = response.json()
            return data["data"][0]["embedding"]
            
        except Exception as e:
            logger.error(f"获取 embedding 时出错: {e}")
            raise
    
    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        批量获取文本的 embedding 向量（新接口名，兼容reddit_service调用）
        
        Args:
            texts: 文本列表
            
        Returns:
            embedding 向量列表
        """
        return await self.batch_get_embeddings(texts)
    
    async def batch_get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        批量获取文本的 embedding 向量（保持原有实现）
        
        Args:
            texts: 文本列表
            
        Returns:
            embedding 向量列表
        """
        if len(texts) > settings.embedding_batch_size * 2:
            logger.debug(f"文本数量({len(texts)})较多，分批处理embedding请求...")
            all_embeddings = []
            batch_size = settings.embedding_batch_size
            
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i+batch_size]
                logger.debug(f"处理第{i//batch_size + 1}批，{len(batch)}个文本")
                try:
                    batch_embeddings = await self._get_embeddings_single_batch(batch)
                    all_embeddings.extend(batch_embeddings)
                except Exception as e:
                    logger.error(f"第{i//batch_size + 1}批embedding请求失败: {e}")
                    zero_embeddings = [[0.0] * settings.embedding_dimension] * len(batch)
                    all_embeddings.extend(zero_embeddings)
            
            return all_embeddings
        else:
            return await self._get_embeddings_single_batch(texts)
    
    async def _get_embeddings_single_batch(self, texts: List[str]) -> List[List[float]]:
        """单批次获取embedding（保持原有实现）"""
        try:
            logger.debug(f"发送embedding请求，{len(texts)}个文本")
            response = await self.client.post(
                f"{self.base_url}/embeddings",
                headers=self.headers,
                json={
                    "model": settings.embedding_model,
                    "input": texts,
                    "dimensions": settings.embedding_dimension
                }
            )
            response.raise_for_status()
            
            data = response.json()
            return [item["embedding"] for item in data["data"]]
            
        except Exception as e:
            logger.error(f"批量获取 embedding 时出错: {e}")
            logger.error(f"请求的文本数量: {len(texts)}")
            logger.error(f"第一个文本预览: {texts[0][:100] if texts else '无文本'}...")
            raise
    
    async def analyze_query(self, query_text: str) -> QueryProfile:
        """
        使用 LangChain 分析用户查询，提取画像
        
        Args:
            query_text: 用户输入的困扰描述
            
        Returns:
            查询画像
        """
        try:
            logger.info(f"开始分析查询: {query_text[:100]}...")
            
            # 使用 LangChain 链进行分析
            analysis: QueryAnalysis = await self.query_analysis_chain.ainvoke(query_text)
            
            logger.info(f"LangChain 分析结果: {analysis}")
            
            # 获取 embedding
            embedding = await self.get_embedding(query_text)
            
            # 转换为原有格式
            from ..models import EmotionType as OriginalEmotionType, ExpressionStyle as OriginalExpressionStyle
            
            return QueryProfile(
                original_text=query_text,
                main_topics=analysis.main_topics,
                emotions=[OriginalEmotionType(e.value) for e in analysis.emotions],
                expression_style=OriginalExpressionStyle(analysis.expression_style.value),
                intent_summary=analysis.intent_summary,
                embedding_vector=embedding
            )
            
        except Exception as e:
            logger.error(f"分析查询时出错: {e}")
            # 返回默认值
            from ..models import EmotionType as OriginalEmotionType, ExpressionStyle as OriginalExpressionStyle
            return QueryProfile(
                original_text=query_text,
                main_topics=["general"],
                emotions=[OriginalEmotionType.CONFUSION],
                expression_style=OriginalExpressionStyle.SEEKING,
                intent_summary="用户正在寻求帮助和理解",
                embedding_vector=await self.get_embedding(query_text)
            )
    
    async def generate_user_embedding(self, user: RedditUser) -> List[float]:
        """
        为 Reddit 用户生成综合 embedding（保持原有实现）
        
        Args:
            user: Reddit 用户对象
            
        Returns:
            用户的综合 embedding 向量
        """
        logger.debug(f"开始为用户 {user.username} 生成综合embedding...")
        
        texts = []
        
        # 添加最近的帖子
        post_count = 0
        for post in user.posts[:20]:
            texts.append(post.text)
            post_count += 1
        logger.debug(f"  - 收集了{post_count}个帖子")
        
        # 添加最近的评论
        comment_count = 0
        for comment in user.comments[:20]:
            texts.append(comment.text)
            comment_count += 1
        logger.debug(f"  - 收集了{comment_count}条评论")
        
        if not texts:
            logger.warning(f"用户 {user.username} 没有任何内容，返回零向量")
            return [0.0] * settings.embedding_dimension
        
        logger.debug(f"  - 正在为{len(texts)}段文本生成embeddings...")
        embeddings = await self.batch_get_embeddings(texts)
        
        # 计算加权平均
        weights = np.array([1.0 / (i + 1) for i in range(len(embeddings))])
        weights = weights / weights.sum()
        
        weighted_embedding = np.average(embeddings, axis=0, weights=weights)
        logger.debug(f"  - 用户 {user.username} 的综合embedding生成完成")
        
        return weighted_embedding.tolist()
    
    async def generate_resonance_summary(self, query: QueryProfile, user: RedditUser, matched_posts: List[RedditPost]) -> str:
        """
        使用 LangChain 生成共鸣摘要
        
        Args:
            query: 用户查询画像
            user: 匹配的 Reddit 用户
            matched_posts: 最相关的帖子
            
        Returns:
            共鸣摘要文本
        """
        try:
            posts_text = "\n\n".join([f"帖子 {i+1}：{post.text[:200]}..." for i, post in enumerate(matched_posts[:3])])
            
            result: ResonanceSummary = await self.resonance_chain.ainvoke({
                "query_text": query.original_text,
                "emotions": ', '.join([e.value for e in query.emotions]),
                "username": user.username,
                "posts_text": posts_text,
                "format_instructions": self.resonance_parser.get_format_instructions()
            })
            
            return result.summary
            
        except Exception as e:
            logger.error(f"生成共鸣摘要时出错: {e}")
            return "这位用户有着相似的经历和感受，可能会理解你的困扰。"
    
    async def generate_private_message(self, query: QueryProfile, user: RedditUser, resonance_summary: str) -> str:
        """
        使用 LangChain 生成私信建议
        
        Args:
            query: 用户查询画像
            user: 匹配的 Reddit 用户
            resonance_summary: 共鸣摘要
            
        Returns:
            私信内容
        """
        try:
            result: PrivateMessage = await self.message_chain.ainvoke({
                "query_text": query.original_text,
                "username": user.username,
                "resonance_summary": resonance_summary,
                "format_instructions": self.message_parser.get_format_instructions()
            })
            
            return result.message
            
        except Exception as e:
            logger.error(f"生成私信时出错: {e}")
            return "Hi，看到你之前的帖子，感觉我们有相似的经历。不知道能否聊聊？"
    
    async def translate_to_english(self, text: str) -> str:
        """
        使用 LangChain 翻译文本到英文
        
        Args:
            text: 中文文本
            
        Returns:
            英文翻译
        """
        try:
            result: TranslationResult = await self.translation_chain.ainvoke({
                "text": text,
                "format_instructions": self.translation_parser.get_format_instructions()
            })
            
            return result.translated_text
            
        except Exception as e:
            logger.error(f"翻译时出错: {e}")
            return text  # 翻译失败时返回原文
    
    async def get_completion(self, prompt: str, temperature: float = 0.3, max_tokens: int = 1500, system_prompt: str = "你是一个功能强大的AI助手，严格遵循用户的指令。") -> str:
        """
        使用 LangChain 获取通用文本完成
        
        Args:
            prompt: 用户提示
            temperature: 温度参数
            max_tokens: 最大token数
            system_prompt: 系统提示
            
        Returns:
            生成的文本
        """
        try:
            # 获取当前模型配置
            model_config = settings.MODEL_CONFIGS.get(settings.llm_model, settings.MODEL_CONFIGS["default"])
            
            # 构建临时 LLM 实例参数，使用与主 LLM 相同的配置
            temp_llm_kwargs = {
                "model": settings.llm_model,
                "api_key": self.api_key,
                "base_url": self.base_url,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "timeout": 60.0,
                "max_retries": 3
            }
            
            # 如果模型支持 JSON 模式，也为临时实例启用
            if model_config.get("supports_json_mode", False):
                temp_llm_kwargs.setdefault("model_kwargs", {})["response_format"] = {"type": "json_object"}
                logger.debug(f"为临时LLM实例启用JSON模式 (model_kwargs)")
            
            # 创建临时 LLM 实例
            temp_llm = ChatOpenAI(**temp_llm_kwargs)
            
            # 直接构建消息列表，避免使用模板
            from langchain_core.messages import SystemMessage, HumanMessage
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=prompt)
            ]
            
            # 直接调用LLM，不使用ChatPromptTemplate
            result = await temp_llm.ainvoke(messages)
            return result.content
            
        except Exception as e:
            logger.error(f"获取完成时出错: {e}")
            return "抱歉，处理请求时出现了问题。"
    
    async def get_completion_stream(self, prompt: str, temperature: float = 0.3, max_tokens: int = 1500, 
                                   system_prompt: str = "你是一个功能强大的AI助手，严格遵循用户的指令。",
                                   timeout: float = 30.0) -> str:
        """
        使用流式返回获取文本完成，支持更精确的超时控制
        
        Args:
            prompt: 用户提示
            temperature: 温度参数
            max_tokens: 最大token数
            system_prompt: 系统提示
            timeout: 超时时间（秒）
            
        Returns:
            生成的文本
        """
        try:
            # 获取当前模型配置
            model_config = settings.MODEL_CONFIGS.get(settings.llm_model, settings.MODEL_CONFIGS["default"])
            
            # 构建临时 LLM 实例参数
            temp_llm_kwargs = {
                "model": settings.llm_model,
                "api_key": self.api_key,
                "base_url": self.base_url,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "timeout": timeout,
                "max_retries": 1  # 流式模式下减少重试次数
            }
            
            # 如果模型支持 JSON 模式，也为临时实例启用
            if model_config.get("supports_json_mode", False):
                temp_llm_kwargs.setdefault("model_kwargs", {})["response_format"] = {"type": "json_object"}
                logger.debug(f"为流式LLM实例启用JSON模式")
            
            # 创建临时 LLM 实例
            temp_llm = ChatOpenAI(**temp_llm_kwargs)
            
            # 构建消息列表
            from langchain_core.messages import SystemMessage, HumanMessage
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=prompt)
            ]
            
            # 使用流式返回，带超时控制
            import asyncio
            from datetime import datetime
            
            start_time = datetime.now()
            response_chunks = []
            last_chunk_time = start_time
            
            try:
                async for chunk in temp_llm.astream(messages):
                    current_time = datetime.now()
                    
                    # 检查整体超时
                    if (current_time - start_time).total_seconds() > timeout:
                        logger.warning(f"流式返回整体超时 ({timeout}秒)")
                        break
                    
                    # 检查单块超时（如果某个chunk卡住超过10秒）
                    if (current_time - last_chunk_time).total_seconds() > 10:
                        logger.warning(f"流式返回单块超时 (10秒)")
                        break
                    
                    if hasattr(chunk, 'content') and chunk.content:
                        # 实时打印流式返回内容
                        chunk_content = chunk.content
                        response_chunks.append(chunk_content)
                        last_chunk_time = current_time
                        
                        # 像ChatGPT那样逐字显示返回内容
                        print(chunk_content, end='', flush=True)
                        
                        # 计算总长度用于提前结束判断
                        total_length = len(''.join(response_chunks))
                        
                        # 如果已经收集到足够的内容，可以提前结束
                        if total_length > max_tokens * 3:  # 粗略估算
                            logger.debug("已收集足够内容，提前结束流式返回")
                            break
                        
                    else:
                        # 打印空chunk的调试信息
                        logger.debug(f"收到空chunk: {chunk}")
                
                # 合并所有chunk
                full_response = ''.join(response_chunks)
                total_time = (datetime.now() - start_time).total_seconds()
                print(f"\n\n[完成] {len(full_response)}字符 / {total_time:.2f}秒")
                return full_response
                
            except asyncio.TimeoutError:
                logger.warning(f"流式返回超时 ({timeout}秒)")
                # 返回已收集的内容
                partial_response = ''.join(response_chunks)
                if partial_response:
                    total_time = (datetime.now() - start_time).total_seconds()
                    print(f"\n\n[超时] 部分响应: {len(partial_response)}字符")
                    return partial_response
                else:
                    raise
                    
        except Exception as e:
            logger.error(f"流式返回出错: {e}")
            raise
    
    # 保持原有的兼容性方法
    async def filter_relevant_posts(self, query_text: str, posts: List[RedditPost], 
                                   threshold: float = 0.75) -> List[RedditPost]:
        """基于相似度过滤高相关性帖子（保持原有实现）"""
        if not posts:
            return []
            
        try:
            logger.info(f"开始过滤帖子相关性，候选帖子数: {len(posts)}")
            logger.info(f"相似度阈值: {threshold}")
            
            query_embedding = await self.get_embedding(query_text)
            post_texts = [post.text for post in posts]
            post_embeddings = await self.batch_get_embeddings(post_texts)
            
            relevant_posts = []
            for i, post in enumerate(posts):
                try:
                    similarity = self._cosine_similarity(query_embedding, post_embeddings[i])
                    
                    if similarity >= threshold:
                        logger.debug(f"帖子 {post.id} 相似度: {similarity:.3f} (保留)")
                        relevant_posts.append((post, similarity))
                    else:
                        logger.debug(f"帖子 {post.id} 相似度: {similarity:.3f} (过滤)")
                        
                except Exception as e:
                    logger.error(f"处理帖子 {post.id} 时出错: {e}")
                    continue
            
            relevant_posts.sort(key=lambda x: x[1], reverse=True)
            result = [post for post, similarity in relevant_posts]
            
            logger.info(f"过滤完成，保留 {len(result)} 个高相关性帖子")
            return result
            
        except Exception as e:
            logger.error(f"过滤帖子相关性时出错: {e}")
            return posts
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算两个向量的余弦相似度（保持原有实现）"""
        import numpy as np
        
        v1 = np.array(vec1)
        v2 = np.array(vec2)
        
        dot_product = np.dot(v1, v2)
        norms = np.linalg.norm(v1) * np.linalg.norm(v2)
        
        if norms == 0:
            return 0.0
        
        return dot_product / norms
    
    def _extract_json_from_response(self, response: str) -> str:
        """从AI响应中提取JSON内容，增强处理thinking类模型的<think>标签"""
        import re
        import json
        
        logger.debug(f"开始提取JSON，原始响应长度: {len(response)} 字符")
        
        # 记录完整响应用于调试（但限制长度避免日志过长）
        if len(response) < 2000:
            logger.debug(f"完整响应内容: {response}")
        else:
            logger.debug(f"响应太长，前1000字符: {response[:1000]}")
            logger.debug(f"响应末尾1000字符: {response[-1000:]}")
        
        # 检查是否是空响应或者异常短的响应
        if len(response.strip()) < 10:
            logger.error(f"响应内容过短，可能是网络错误或API限制: '{response}'")
            raise ValueError("响应内容为空或过短")
        
        # 策略1：专门处理thinking模型格式 <think>...</think>{JSON}
        think_match = re.search(r'<think>.*?</think>\s*(\{.*?\})', response, re.DOTALL)
        if think_match:
            json_candidate = think_match.group(1).strip()
            logger.debug(f"找到thinking模式JSON，长度: {len(json_candidate)}")
            if self._validate_json(json_candidate):
                logger.debug("thinking模式JSON验证成功")
                return json_candidate
        
        # 策略2：寻找</think>之后的内容
        think_end = response.find('</think>')
        if think_end != -1:
            after_think = response[think_end + 8:].strip()
            logger.debug(f"找到</think>标签，提取之后内容长度: {len(after_think)}")
            
            # 查找第一个完整的JSON对象
            json_candidate = self._find_complete_json_object(after_think)
            if json_candidate and self._validate_json(json_candidate):
                logger.debug("从</think>之后成功提取JSON")
                return json_candidate
        
        # 策略3：移除所有think标签后提取
        response_cleaned = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL | re.IGNORECASE)
        response_cleaned = response_cleaned.strip()
        
        if response_cleaned:
            logger.debug(f"清理think标签后长度: {len(response_cleaned)}")
            json_candidate = self._find_complete_json_object(response_cleaned)
            if json_candidate and self._validate_json(json_candidate):
                logger.debug("从清理后响应成功提取JSON")
                return json_candidate
        
        # 策略4：正则表达式匹配JSON代码块
        patterns = [
            r'```json\s*(.*?)\s*```',
            r'```\s*(.*?)\s*```',
            r'(\{(?:[^{}]|{[^}]*})*\})'  # 匹配嵌套JSON对象
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.DOTALL)
            for match in matches:
                match = match.strip()
                if match.startswith('{') and match.endswith('}') and self._validate_json(match):
                    logger.debug(f"通过正则表达式成功提取JSON")
                    return match
        
        # 策略5：智能修复常见JSON错误
        json_candidate = self._attempt_json_repair(response)
        if json_candidate:
            logger.debug("通过智能修复成功提取JSON")
            return json_candidate
        
        # 策略6：降级 - 尝试提取部分JSON
        partial_json = self._extract_partial_json(response)
        if partial_json:
            # 改进警告信息，提供更多上下文
            logger.warning(f"使用部分JSON提取作为降级方案 - 响应长度: {len(response)}, think标签: {'<think>' in response}")
            logger.debug(f"降级JSON: {partial_json}")
            return partial_json
        
        # 所有策略都失败，记录详细错误信息
        logger.error("所有JSON提取策略都失败")
        logger.error(f"响应长度: {len(response)}")
        logger.error(f"包含花括号: {'{' in response}")
        logger.error(f"左括号数量: {response.count('{')}")
        logger.error(f"右括号数量: {response.count('}')}")
        
        # 提供更多调试信息
        if '<think>' in response:
            think_count = response.count('<think>')
            think_end_count = response.count('</think>')
            logger.error(f"Think标签统计: <think>={think_count}, </think>={think_end_count}")
        
        # 记录响应示例用于调试
        if len(response) > 0:
            logger.error(f"响应开头: {response[:200]}")
            logger.error(f"响应结尾: {response[-200:]}")
        
        # 如果有results_manager实例，保存调试信息
        try:
            # 检查是否有全局的详细结果管理器实例  
            from ..utils.detailed_results_manager import get_global_manager
            manager = get_global_manager()
            if manager and manager.get_active_session_id():
                manager.save_ai_response_debug(
                    response_type="json_extraction_failure",
                    original_response=response,
                    error=f"所有JSON提取策略都失败，响应长度: {len(response)}"
                )
        except Exception as debug_save_error:
            logger.debug(f"保存调试信息失败: {debug_save_error}")
        
        raise ValueError(f"无法从响应中提取有效的JSON内容")
    
    def _attempt_json_repair(self, response: str) -> Optional[str]:
        """尝试修复常见的JSON格式错误"""
        import json
        
        # 查找可能的JSON片段
        json_candidate = self._find_complete_json_object(response)
        if not json_candidate:
            return None
        
        # 尝试修复常见问题
        repairs = [
            # 移除末尾的逗号
            lambda s: re.sub(r',\s*}', '}', s),
            # 移除末尾的逗号（数组）
            lambda s: re.sub(r',\s*]', ']', s),
            # 修复单引号为双引号
            lambda s: re.sub(r"'([^']*)':", r'"\1":', s),
            # 修复没有引号的键名
            lambda s: re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', s)
        ]
        
        for repair_func in repairs:
            try:
                repaired = repair_func(json_candidate)
                if self._validate_json(repaired):
                    logger.debug("JSON修复成功")
                    return repaired
            except Exception:
                continue
        
        return None
    
    def _extract_partial_json(self, response: str) -> Optional[str]:
        """提取部分JSON作为降级方案"""
        import json
        
        # 尝试构建最小可用的JSON
        try:
            # 如果响应中有可识别的字段，尝试构建基础JSON
            basic_fields = {
                "search_keywords": ["help", "advice"],
                "topics": ["general"],
                "emotional_state": {"confusion": 0.7},
                "confidence": 0.3
            }
            
            # 尝试从响应中提取一些关键词
            if "创业" in response or "startup" in response:
                basic_fields["search_keywords"] = ["entrepreneurship", "startup", "business"]
                basic_fields["topics"] = ["创业", "商业"]
            
            return json.dumps(basic_fields, ensure_ascii=False)
            
        except Exception:
            return None
    
    def _find_complete_json_object(self, text: str) -> str:
        """寻找完整的JSON对象"""
        start_idx = text.find('{')
        if start_idx == -1:
            return ""
        
        brace_count = 0
        for i, char in enumerate(text[start_idx:], start_idx):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    return text[start_idx:i+1]
        
        return ""
    
    def _validate_json(self, json_str: str) -> bool:
        """验证JSON字符串是否有效"""
        try:
            import json
            json.loads(json_str)
            return True
        except json.JSONDecodeError:
            return False
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()
        
    async def test_connection(self) -> bool:
        """测试 API 连接"""
        try:
            await self.get_embedding("test")
            return True
        except Exception as e:
            logger.error(f"API 连接测试失败: {e}")
            return False
    
    def get_semantic_analysis_chain(self):
        """获取语义分析链"""
        return self.semantic_analysis_chain
    
    def get_graph_analysis_chain(self):
        """获取图谱分析链"""
        return self.graph_analysis_chain
    
    def get_clarifying_questions_chain(self):
        """获取追问问题生成链"""
        return self.clarifying_questions_chain 