"""
Twitter 数据获取服务 - 纯真实数据版本
使用snscrape实现Twitter/X平台的数据抓取，不包含模拟模式
"""
import asyncio
import logging
import math
import re
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Any, Tuple
import importlib.machinery
from pathlib import Path

# --- Python 3.12 兼容补丁 ---------------------------------------------
# snscrape 在 <2024 版本中使用了 importlib.machinery.FileFinder.find_module
# 该方法在 Python 3.12 中已被移除，导致 AttributeError。
# 这里为 FileFinder 动态添加兼容方法，使旧版本库能够正常工作。
if not hasattr(importlib.machinery.FileFinder, 'find_module'):
    def _filefinder_find_module(self, fullname):
        """Python3.12 兼容实现，代理到 find_spec"""
        spec = self.find_spec(fullname)
        if spec is None:
            return None
        return spec.loader
    importlib.machinery.FileFinder.find_module = _filefinder_find_module
# ----------------------------------------------------------------------

# 导入snscrape
try:
    import snscrape.modules.twitter as sntwitter
    SNSCRAPE_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("✅ snscrape导入成功")
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.error(f"❌ snscrape导入失败: {e}")
    logger.error("请安装snscrape: pip install snscrape")
    raise ImportError("snscrape is required for TwitterService") from e

from tenacity import retry, stop_after_attempt, wait_exponential
from .base_social_service import BaseSocialService
from ..config import settings

class TwitterService(BaseSocialService):
    """Twitter 数据获取服务类 - 纯真实数据版本"""
    
    def __init__(self):
        """初始化 Twitter 客户端"""
        if not SNSCRAPE_AVAILABLE:
            raise RuntimeError("snscrape不可用，无法初始化TwitterService")
        
        logger.info("🐦 初始化 Twitter 服务（真实数据模式）")
        
        # 自动检测和修复Twitter API schema ID
        self._auto_fix_twitter_api()
        
        self.platform_name = "Twitter"
        
        # 代理配置
        self.proxy_settings = self._setup_proxy()
        if self.proxy_settings:
            logger.info(f"🌐 配置代理: {self.proxy_settings}")
        else:
            logger.warning("⚠️  未配置代理，可能无法访问Twitter（中国大陆环境）")
        
        # 情绪和经历关键词用于内容过滤
        self.emotion_keywords = [
            '觉得', '认为', '感觉', '希望', '担心', '困惑', '迷茫', '焦虑', 
            '开心', '失望', '纠结', '犹豫', '后悔', '满足', '愤怒', '兴奋'
        ]
        
        self.experience_keywords = [
            '经历', '遇到', '发生', '经过', '体验', '参加', '做了', '试过', 
            '学会', '明白', '发现', '决定', '选择', '改变', '成长', '反思'
        ]
    
    def _auto_fix_twitter_api(self):
        """自动检测和修复Twitter API schema ID"""
        try:
            from ..utils.twitter_schema_updater import TwitterSchemaUpdater
            
            logger.info("检测Twitter API schema ID状态...")
            
            updater = TwitterSchemaUpdater()
            current_id = updater.get_current_schema_id()
            
            if current_id:
                logger.info(f"当前schema ID: {current_id}")
                
                # 测试当前ID是否可用
                if self._test_schema_id_basic(current_id):
                    logger.info("当前schema ID正常，无需修复")
                    return True
                else:
                    logger.warning("当前schema ID失效，尝试自动修复...")
                    
                    # 尝试自动更新
                    if updater.update_to_latest_schema():
                        logger.info("Twitter API schema ID自动修复成功")
                        return True
                    else:
                        logger.error("Twitter API自动修复失败")
                        return False
            else:
                logger.error("无法获取当前schema ID")
                return False
            
        except ImportError:
            logger.warning("Twitter schema updater模块不可用")
            return False
        except Exception as e:
            logger.error(f"Twitter API自动修复异常: {e}")
            return False
    
    def _test_schema_id_basic(self, schema_id: str) -> bool:
        """基础schema ID测试"""
        try:
            import requests
            
            test_url = f"https://twitter.com/i/api/graphql/{schema_id}/SearchTimeline"
            
            # 使用HEAD请求测试
            response = requests.head(
                test_url,
                proxies=self.proxy_settings or {},
                timeout=5,
                params={'variables': '{"rawQuery":"test","count":1}'}
            )
            
            # 200/401/429表示端点存在，404表示过期
            return response.status_code in [200, 401, 429]
            
        except:
            return False
    
    def _setup_proxy(self) -> Optional[Dict[str, str]]:
        """设置代理配置"""
        import os
        
        # 方式1: 从环境变量读取
        http_proxy = os.getenv('HTTP_PROXY') or os.getenv('http_proxy')
        https_proxy = os.getenv('HTTPS_PROXY') or os.getenv('https_proxy')
        
        if http_proxy or https_proxy:
            proxy_config = {}
            if http_proxy:
                proxy_config['http'] = http_proxy
            if https_proxy:
                proxy_config['https'] = https_proxy
            return proxy_config
        
        # 方式2: 常用Clash代理端口自动检测
        clash_ports = [7890, 7891, 1080, 8080]
        for port in clash_ports:
            proxy_url = f"http://127.0.0.1:{port}"
            if self._test_proxy(proxy_url):
                logger.info(f"🎯 自动检测到可用代理: {proxy_url}")
                return {
                    'http': proxy_url,
                    'https': proxy_url
                }
        
        return None
    
    def _test_proxy(self, proxy_url: str) -> bool:
        """测试代理是否可用"""
        try:
            import requests
            proxies = {'http': proxy_url, 'https': proxy_url}
            response = requests.get(
                'http://httpbin.org/ip', 
                proxies=proxies, 
                timeout=5
            )
            return response.status_code == 200
        except:
            return False
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def search_posts_by_keywords_enhanced(self, keywords: List[str], 
                                               limit: int = 100,
                                               time_filter: str = "all") -> List[Dict[str, Any]]:
        """
        增强的Twitter搜索：直接使用用户Prompt搜索Top 30，然后进行过滤
        
        Args:
            keywords: 搜索关键词列表（将拼接为搜索查询）
            limit: 搜索帖子数量限制
            time_filter: 时间过滤器（all/month/week/day）
            
        Returns:
            过滤后的高质量推文列表
        """
        logger.info(f"🔍 开始Twitter真实数据搜索，关键词: {keywords}")
        
        # 第一阶段：直接搜索 Top 30
        logger.info("第一阶段：Twitter搜索框式搜索（Top 30）")
        
        # 构建搜索查询（保持语义完整性）
        if len(keywords) == 1 and len(keywords[0]) > 50:
            # 如果是单个长关键词（可能是用户原始Prompt），直接使用
            search_query = keywords[0]
        else:
            # 多个关键词，用空格连接
            search_query = ' '.join(keywords)
        
        # 限制搜索查询长度，避免超过Twitter搜索限制
        if len(search_query) > 240:
            search_query = search_query[:240] + "..."
            logger.info(f"搜索查询过长，截断为: {search_query}")
        
        # 添加时间过滤器到查询
        time_filter_query = self._build_time_filter_query(search_query, time_filter)
        
        # 执行搜索
        raw_tweets = await self._search_tweets_raw(time_filter_query, limit=min(30, limit))
        
        logger.info(f"✅ Twitter搜索获得 {len(raw_tweets)} 条原始推文")
        
        if not raw_tweets:
            logger.warning("⚠️  Twitter搜索未返回任何结果")
            return []
        
        # 第二阶段：质量过滤和去重
        logger.info("第二阶段：质量过滤和用户去重")
        
        filtered_tweets = await self._filter_and_deduplicate_tweets(
            raw_tweets, 
            min_length=80,  # 至少80字符
            max_per_user=2  # 每个用户最多2条推文
        )
        
        logger.info(f"✅ 过滤后保留 {len(filtered_tweets)} 条高质量推文")
        
        # 转换为标准格式
        standardized_posts = self._convert_tweets_to_standard_format(filtered_tweets)
        
        return standardized_posts[:limit]
    
    def _build_time_filter_query(self, base_query: str, time_filter: str) -> str:
        """构建带时间过滤的搜索查询"""
        if time_filter == "all":
            return base_query
        
        # 计算时间范围
        now = datetime.now()
        if time_filter == "day":
            since_date = (now - timedelta(days=1)).strftime("%Y-%m-%d")
        elif time_filter == "week":
            since_date = (now - timedelta(weeks=1)).strftime("%Y-%m-%d")
        elif time_filter == "month":
            since_date = (now - timedelta(days=30)).strftime("%Y-%m-%d")
        else:
            return base_query
        
        return f"{base_query} since:{since_date}"
    
    async def _search_tweets_raw(self, query: str, limit: int = 30) -> List[Any]:
        """执行原始Twitter搜索"""
        logger.info(f"🔍 执行snscrape搜索: {query}")
        
        tweets = []
        scraper = sntwitter.TwitterSearchScraper(query)
        
        # 配置代理
        if self.proxy_settings:
            self._configure_scraper_proxy(scraper)
        
        try:
            for i, tweet in enumerate(scraper.get_items()):
                if i >= limit:
                    break
                
                # 只要原生推文，排除转推
                if hasattr(tweet, 'retweetedTweet') and tweet.retweetedTweet:
                    continue
                
                tweets.append(tweet)
            
            logger.info(f"📊 snscrape 获取到 {len(tweets)} 条原生推文")
            return tweets
        except Exception as e:
            logger.error(f"❌ snscrape搜索失败: {e}")
            if "网络" in str(e) or "timeout" in str(e).lower() or "connection" in str(e).lower():
                logger.error("💡 建议检查网络连接和代理设置")
                logger.error("💡 请确保Clash或其他代理软件正在运行")
            return []
    
    def _configure_scraper_proxy(self, scraper):
        """为snscrape scraper配置代理"""
        try:
            import requests
            
            # 创建带代理的session
            session = requests.Session()
            session.proxies.update(self.proxy_settings)
            
            # 设置超时和重试
            session.timeout = 30
            
            # 为scraper设置session
            if hasattr(scraper, '_session'):
                scraper._session = session
            else:
                # 如果scraper没有_session属性，尝试其他方式
                logger.debug("⚠️  无法直接设置scraper session，使用全局代理")
                
        except Exception as e:
            logger.warning(f"⚠️  代理配置失败: {e}")
    
    async def _filter_and_deduplicate_tweets(self, tweets: List[Any], 
                                           min_length: int = 80,
                                           max_per_user: int = 2) -> List[Any]:
        """过滤和去重推文"""
        filtered_tweets = []
        user_tweet_count = {}  # 记录每个用户的推文数量
        
        for tweet in tweets:
            try:
                # 基本质量检查
                if not self._is_quality_tweet(tweet, min_length):
                    continue
                
                # 用户去重检查
                username = tweet.user.username
                if user_tweet_count.get(username, 0) >= max_per_user:
                    continue
                
                # 通过所有检查，添加到结果
                filtered_tweets.append(tweet)
                user_tweet_count[username] = user_tweet_count.get(username, 0) + 1
                
            except Exception as e:
                logger.debug(f"处理推文时出错: {e}")
                continue
        
        # 按互动量排序（点赞+转发+回复）
        filtered_tweets.sort(
            key=lambda t: (t.likeCount or 0) + (t.retweetCount or 0) + (t.replyCount or 0),
            reverse=True
        )
        
        return filtered_tweets
    
    def _is_quality_tweet(self, tweet: Any, min_length: int = 80) -> bool:
        """检查推文是否为高质量内容"""
        content = tweet.rawContent or tweet.content or ""
        
        # 长度检查
        if len(content) < min_length:
            return False
        
        # 排除链接为主的推文
        if len(re.findall(r'http[s]?://\S+', content)) > 2:
            return False
        
        # 排除过多@和#的推文
        if len(re.findall(r'[@#]\w+', content)) > 5:
            return False
        
        # 检查是否包含情绪或经历关键词
        has_emotion = any(keyword in content for keyword in self.emotion_keywords)
        has_experience = any(keyword in content for keyword in self.experience_keywords)
        
        if has_emotion or has_experience:
            return True
        
        # 如果没有明显的情绪/经历词，但内容较长且有一定互动，也认为是质量内容
        if len(content) > 120 and (tweet.likeCount or 0) > 5:
            return True
        
        return False
    
    def _convert_tweets_to_standard_format(self, tweets: List[Any]) -> List[Dict[str, Any]]:
        """将Twitter推文转换为标准格式"""
        standardized_posts = []
        
        for tweet in tweets:
            try:
                # 计算质量分数
                quality_score = self._calculate_tweet_quality_score(tweet)
                
                post = {
                    'id': str(tweet.id),
                    'text': tweet.rawContent or tweet.content or "",
                    'score': (tweet.likeCount or 0) + (tweet.retweetCount or 0),  # 组合分数
                    'created_utc': tweet.date.timestamp() if tweet.date else datetime.now().timestamp(),
                    'subreddit': f"@{tweet.user.username}",  # 用用户名代替subreddit
                    'author': tweet.user.username,
                    'num_comments': tweet.replyCount or 0,  # 兼容Reddit格式
                    'quality_score': quality_score,
                    'platform': 'twitter',
                    'url': tweet.url or "",
                    'like_count': tweet.likeCount or 0,
                    'retweet_count': tweet.retweetCount or 0,
                    'reply_count': tweet.replyCount or 0,
                    'user_info': {
                        'username': tweet.user.username,
                        'display_name': tweet.user.displayname or "",
                        'followers': tweet.user.followersCount or 0,
                        'verified': tweet.user.verified or False
                    }
                }
                
                standardized_posts.append(post)
                
            except Exception as e:
                logger.debug(f"转换推文格式时出错: {e}")
                continue
        
        return standardized_posts
    
    def _calculate_tweet_quality_score(self, tweet: Any) -> float:
        """计算推文质量分数"""
        content = tweet.rawContent or tweet.content or ""
        like_count = tweet.likeCount or 0
        retweet_count = tweet.retweetCount or 0
        reply_count = tweet.replyCount or 0
        
        # 基础分数组成
        engagement_score = like_count * 0.5 + retweet_count * 0.3 + reply_count * 0.2
        length_score = min(len(content) / 200, 1.0)  # 长度归一化
        
        # 情绪和经历加分
        emotion_bonus = 0.2 if any(keyword in content for keyword in self.emotion_keywords) else 0
        experience_bonus = 0.3 if any(keyword in content for keyword in self.experience_keywords) else 0
        
        # 用户质量加分
        user_bonus = 0.1 if (tweet.user.verified or (tweet.user.followersCount or 0) > 1000) else 0
        
        total_score = (
            engagement_score * 0.4 +
            length_score * 0.3 +
            emotion_bonus +
            experience_bonus +
            user_bonus
        )
        
        return max(0.0, min(total_score, 10.0))  # 限制在0-10范围
    
    async def rerank_posts_by_embedding(self, posts: List[Dict[str, Any]], 
                                       user_query: str, 
                                       top_k: int = 30) -> List[Dict[str, Any]]:
        """使用嵌入向量重新排序推文"""
        logger.info(f"🔄 开始embedding重排序 {len(posts)} 条推文")
        
        if len(posts) <= top_k:
            logger.info("推文数量不足，跳过embedding重排序")
            return posts
        
        try:
            # 导入AI服务
            from ..services.ai_service import AIService
            ai_service = AIService()
            
            # 为每条推文计算与用户查询的相似度
            reranked_posts = []
            
            for post in posts:
                try:
                    # 计算语义相似度
                    similarity = await ai_service.calculate_semantic_similarity(
                        text1=user_query,
                        text2=post['text']
                    )
                    
                    # 组合分数：原质量分数 + 语义相似度
                    post['embedding_similarity'] = similarity
                    post['combined_score'] = (
                        post.get('quality_score', 0) * 0.3 +
                        similarity * 0.7
                    )
                    
                    reranked_posts.append(post)
                    
                except Exception as e:
                    logger.debug(f"计算相似度失败: {e}")
                    # 保留原分数
                    post['embedding_similarity'] = 0.0
                    post['combined_score'] = post.get('quality_score', 0)
                    reranked_posts.append(post)
            
            # 按组合分数排序
            reranked_posts.sort(key=lambda x: x['combined_score'], reverse=True)
            
            result = reranked_posts[:top_k]
            logger.info(f"✅ Embedding重排序完成，返回前 {len(result)} 条推文")
            
            return result
            
        except Exception as e:
            logger.error(f"Embedding重排序失败: {e}")
            return posts[:top_k]
    
    async def llm_final_rank_posts(self, posts: List[Dict[str, Any]], 
                                  user_query: str, 
                                  top_k: int = 10) -> List[Dict[str, Any]]:
        """LLM最终精排推文"""
        logger.info(f"🤖 开始LLM精排 {len(posts)} 条推文")
        
        if len(posts) <= top_k:
            logger.info("推文数量不足，跳过LLM精排")
            return posts
        
        try:
            # 导入AI服务
            from ..services.ai_service import AIService
            ai_service = AIService()
            
            # 构建LLM评分prompt
            posts_for_ranking = []
            for i, post in enumerate(posts[:min(20, len(posts))]):  # 最多处理20条
                posts_for_ranking.append({
                    'index': i,
                    'text': post['text'][:300],  # 限制长度
                    'author': post.get('author', ''),
                    'engagement': f"👍{post.get('like_count', 0)} 🔁{post.get('retweet_count', 0)} 💬{post.get('reply_count', 0)}"
                })
            
            # LLM评分
            ranking_prompt = f"""
请为以下Twitter推文评分，评分标准是与用户查询的相关性和内容质量。
用户查询：{user_query}

推文列表：
""" + "\n".join([f"{i+1}. @{p['author']}: {p['text']} [{p['engagement']}]" 
                for i, p in enumerate(posts_for_ranking)])

            ranking_prompt += """

请为每条推文打分（0-10分），考虑：
1. 与用户困惑的相关性
2. 内容的真实性和深度
3. 是否包含个人经历或感悟
4. 是否能给用户提供启发

返回格式：JSON数组，每个元素包含index和score。
"""
            
            # 获取LLM评分
            llm_response = await ai_service.generate_response(ranking_prompt)
            
            # 解析LLM评分结果
            scores = self._parse_llm_scores(llm_response, len(posts_for_ranking))
            
            # 应用LLM分数
            for i, post in enumerate(posts[:len(scores)]):
                llm_score = scores[i]
                post['llm_score'] = llm_score
                post['final_score'] = (
                    post.get('combined_score', post.get('quality_score', 0)) * 0.5 +
                    llm_score * 0.5
                )
            
            # 按最终分数排序
            posts.sort(key=lambda x: x.get('final_score', 0), reverse=True)
            
            result = posts[:top_k]
            logger.info(f"✅ LLM精排完成，返回前 {len(result)} 条推文")
            
            return result
            
        except Exception as e:
            logger.error(f"LLM精排失败: {e}")
            return posts[:top_k]
    
    def _parse_llm_scores(self, llm_response: str, expected_count: int) -> List[float]:
        """解析LLM评分结果"""
        try:
            import json
            import re
            
            # 尝试提取JSON
            json_match = re.search(r'\[.*\]', llm_response, re.DOTALL)
            if json_match:
                scores_data = json.loads(json_match.group())
                scores = [item.get('score', 5.0) for item in scores_data]
                return scores
            
            # 备选解析：寻找数字分数
            numbers = re.findall(r'(\d+(?:\.\d+)?)', llm_response)
            scores = [float(num) for num in numbers[:expected_count]]
            
            # 确保有足够的分数
            while len(scores) < expected_count:
                scores.append(5.0)
            
            return scores[:expected_count]
            
        except Exception as e:
            logger.error(f"解析LLM评分失败: {e}")
            return [5.0] * expected_count
    
    async def extract_quality_commenters_detailed(self, posts: List[Any], 
                                                min_score: int = 0, 
                                                min_length: int = 100,
                                                max_commenters: int = 50) -> Dict[str, Any]:
        """
        从推文中提取优质推主（作者）
        注意：Twitter的"评论者"概念对应推文作者
        """
        logger.info(f"👥 开始从 {len(posts)} 条推文中提取优质推主...")
        
        all_authors = {}
        selected_authors = []
        
        extraction_stats = {
            "total_posts_processed": len(posts),
            "total_authors_found": 0,
            "qualified_authors": 0,
            "posts_with_errors": 0
        }
        
        # 处理每条推文，提取作者信息
        for post in posts:
            try:
                author = post.get('author', '')
                if not author:
                    extraction_stats["posts_with_errors"] += 1
                    continue
                
                # 计算作者质量分数
                author_quality = self._calculate_author_quality(post)
                
                if author not in all_authors:
                    all_authors[author] = {
                        'username': author,
                        'posts': [],
                        'total_score': 0,
                        'total_length': 0,
                        'quality_scores': [],
                        'platforms': set(),
                        'user_info': post.get('user_info', {})
                    }
                    extraction_stats["total_authors_found"] += 1
                
                # 添加这条推文到作者记录
                all_authors[author]['posts'].append(post)
                all_authors[author]['total_score'] += post.get('score', 0)
                all_authors[author]['total_length'] += len(post.get('text', ''))
                all_authors[author]['quality_scores'].append(author_quality['overall_score'])
                all_authors[author]['platforms'].add('twitter')
                
            except Exception as e:
                logger.debug(f"处理推文作者时出错: {e}")
                extraction_stats["posts_with_errors"] += 1
                continue
        
        # 选择高质量作者
        for username, author_data in all_authors.items():
            try:
                # 基础统计
                post_count = len(author_data['posts'])
                avg_score = author_data['total_score'] / post_count if post_count > 0 else 0
                avg_length = author_data['total_length'] / post_count if post_count > 0 else 0
                avg_quality = sum(author_data['quality_scores']) / len(author_data['quality_scores']) if author_data['quality_scores'] else 0
                
                # 质量检查
                if (avg_score >= min_score and 
                    avg_length >= min_length and 
                    avg_quality >= 3.0):  # 质量分数阈值
                    
                    selection_reasons = self._generate_selection_reasons(author_data)
                    
                    selected_authors.append({
                        'username': username,
                        'post_count': post_count,
                        'avg_score': avg_score,
                        'avg_length': avg_length,
                        'avg_quality': avg_quality,
                        'platforms': list(author_data['platforms']),
                        'selection_reasons': selection_reasons,
                        'user_info': author_data['user_info'],
                        'selection_confidence': min(avg_quality / 10.0, 1.0)
                    })
                    
                    extraction_stats["qualified_authors"] += 1
                    
            except Exception as e:
                logger.debug(f"评估作者质量时出错: {e}")
                continue
        
        # 排序并限制数量
        selected_authors.sort(key=lambda x: x['avg_quality'], reverse=True)
        selected_authors = selected_authors[:max_commenters]
        
        # 为选中的作者添加排名
        for i, author in enumerate(selected_authors):
            author['rank'] = i + 1
        
        logger.info(f"✅ 提取完成：{len(selected_authors)} 位优质推主")
        
        return {
            "selected_commenters": selected_authors,
            "all_commenters": all_authors,
            "extraction_stats": extraction_stats,
            "selection_criteria": {
                "min_score": min_score,
                "min_length": min_length,
                "min_quality": 3.0,
                "max_commenters": max_commenters
            }
        }
    
    def _calculate_author_quality(self, post: Dict[str, Any]) -> Dict[str, float]:
        """计算作者质量分数"""
        text_length = len(post.get('text', ''))
        like_count = post.get('like_count', 0)
        retweet_count = post.get('retweet_count', 0)
        reply_count = post.get('reply_count', 0)
        
        # 用户信息质量
        user_info = post.get('user_info', {})
        followers = user_info.get('followers', 0)
        verified = user_info.get('verified', False)
        
        scores = {
            'content_length': min(text_length / 200, 1.0),
            'engagement': (like_count * 0.5 + retweet_count * 0.3 + reply_count * 0.2) / 10,
            'user_credibility': (1.0 if verified else 0) + min(followers / 10000, 0.5),
            'overall_score': 0
        }
        
        scores['overall_score'] = (
            scores['content_length'] * 3 +
            scores['engagement'] * 2 +
            scores['user_credibility'] * 5
        )
        
        return scores
    
    def _generate_selection_reasons(self, author_data: Dict[str, Any]) -> List[str]:
        """生成选择作者的理由"""
        reasons = []
        
        post_count = len(author_data['posts'])
        avg_length = author_data['total_length'] / post_count if post_count > 0 else 0
        user_info = author_data.get('user_info', {})
        
        if post_count > 1:
            reasons.append(f"多次发布相关内容({post_count}条)")
        
        if avg_length > 150:
            reasons.append("内容详实深入")
        
        if user_info.get('verified'):
            reasons.append("认证用户")
        
        if user_info.get('followers', 0) > 1000:
            reasons.append(f"影响力用户({user_info['followers']}关注者)")
        
        # 检查内容质量
        for post in author_data['posts']:
            content = post.get('text', '')
            if any(keyword in content for keyword in self.emotion_keywords):
                reasons.append("包含情感表达")
                break
        
        for post in author_data['posts']:
            content = post.get('text', '')
            if any(keyword in content for keyword in self.experience_keywords):
                reasons.append("分享个人经历")
                break
        
        return reasons[:5]  # 最多5个理由
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def get_user_comprehensive_history(self, username: str, limit: int = 200, 
                                           filter_top_level_comments: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取Twitter用户的综合历史数据
        使用时间分位采样策略（方案1）
        """
        logger.info(f"📊 开始获取用户 @{username} 的综合历史数据...")
        
        try:
            # 第一步：获取用户时间线
            all_tweets = await self._get_user_timeline_raw(username, limit=1000)
            
            if not all_tweets:
                logger.warning(f"⚠️  用户 @{username} 没有获取到任何推文")
                return None
            
            logger.info(f"📊 获取到用户 @{username} 的 {len(all_tweets)} 条推文")
            
            # 第二步：时间分位采样
            sampled_tweets = self._quantile_time_sampling(all_tweets, target_count=30)
            
            logger.info(f"⏰ 时间分位采样后保留 {len(sampled_tweets)} 条代表性推文")
            
            # 第三步：转换为标准格式
            formatted_contents = []
            for tweet in sampled_tweets:
                try:
                    formatted_content = {
                        'text': tweet.rawContent or tweet.content or "",
                        'created_utc': tweet.date.timestamp() if tweet.date else datetime.now().timestamp(),
                        'score': (tweet.likeCount or 0) + (tweet.retweetCount or 0),
                        'like_count': tweet.likeCount or 0,
                        'retweet_count': tweet.retweetCount or 0,
                        'reply_count': tweet.replyCount or 0,
                        'url': tweet.url or "",
                        'platform': 'twitter',
                        'sampling_score': self._calculate_tweet_sampling_score(tweet)
                    }
                    formatted_contents.append(formatted_content)
                except Exception as e:
                    logger.debug(f"格式化推文时出错: {e}")
                    continue
            
            # 第四步：计算统计信息
            content_stats = self._calculate_content_stats(formatted_contents)
            
            # 第五步：按采样分数排序
            formatted_contents.sort(key=lambda x: x['sampling_score'], reverse=True)
            
            result = {
                'username': username,
                'platform': 'twitter',
                'total_content_fetched': len(all_tweets),
                'sampled_content_count': len(formatted_contents),
                'formatted_contents': formatted_contents,
                'content_stats': content_stats,
                'sampling_method': '时间分位采样',
                'fetch_timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✅ 用户 @{username} 历史数据获取完成")
            return result
            
        except Exception as e:
            logger.error(f"❌ 获取用户 @{username} 历史数据失败: {e}")
            return None
    
    async def _get_user_timeline_raw(self, username: str, limit: int = 1000) -> List[Any]:
        """获取用户时间线的原始推文数据"""
        logger.info(f"🔍 获取用户 @{username} 的时间线数据...")
        
        tweets = []
        scraper = sntwitter.TwitterProfileScraper(username)
        
        # 配置代理
        if self.proxy_settings:
            self._configure_scraper_proxy(scraper)
        
        try:
            for i, tweet in enumerate(scraper.get_items()):
                if i >= limit:
                    break
                
                # 只要原生推文，排除转推
                if hasattr(tweet, 'retweetedTweet') and tweet.retweetedTweet:
                    continue
                
                tweets.append(tweet)
            
            logger.info(f"📊 获取到用户 @{username} 的 {len(tweets)} 条原生推文")
            return tweets
        except Exception as e:
            logger.error(f"❌ 获取用户时间线失败: {e}")
            if "网络" in str(e) or "timeout" in str(e).lower() or "connection" in str(e).lower():
                logger.error("💡 建议检查网络连接和代理设置")
            return []
    
    def _quantile_time_sampling(self, tweets: List[Any], target_count: int = 30) -> List[Any]:
        """
        时间分位采样策略（方案1）
        按照相对时间分位对推文进行采样，确保时间分布的代表性
        """
        if len(tweets) <= target_count:
            return tweets
        
        # 按时间排序（最新到最旧）
        sorted_tweets = sorted(tweets, key=lambda t: t.date if t.date else datetime.min, reverse=True)
        
        # 分为4个时间分位
        total_count = len(sorted_tweets)
        quantile_size = total_count // 4
        
        sampled_tweets = []
        
        for i in range(4):
            start_idx = i * quantile_size
            end_idx = (i + 1) * quantile_size if i < 3 else total_count
            
            quantile_tweets = sorted_tweets[start_idx:end_idx]
            
            # 每个分位采样 target_count/4 条（约7-8条）
            quantile_target = target_count // 4
            if i == 3:  # 最后一个分位可能需要调整
                quantile_target = target_count - len(sampled_tweets)
            
            if len(quantile_tweets) <= quantile_target:
                sampled_tweets.extend(quantile_tweets)
            else:
                # 按质量分数采样
                scored_tweets = []
                for tweet in quantile_tweets:
                    score = self._calculate_tweet_sampling_score(tweet)
                    scored_tweets.append((tweet, score))
                
                # 按分数排序并选择前N条
                scored_tweets.sort(key=lambda x: x[1], reverse=True)
                sampled_tweets.extend([tweet for tweet, _ in scored_tweets[:quantile_target]])
        
        logger.info(f"⏰ 时间分位采样：总计 {total_count} 条 → 采样 {len(sampled_tweets)} 条")
        
        # 按时间重新排序（保持时间顺序）
        sampled_tweets.sort(key=lambda t: t.date if t.date else datetime.min, reverse=True)
        
        return sampled_tweets
    
    def _calculate_tweet_sampling_score(self, tweet: Any) -> float:
        """计算推文的采样分数（用于分位内排序）"""
        try:
            content = tweet.rawContent or tweet.content or ""
            like_count = tweet.likeCount or 0
            retweet_count = tweet.retweetCount or 0
            reply_count = tweet.replyCount or 0
            
            # 基础质量分数
            engagement_score = like_count * 0.5 + retweet_count * 0.3 + reply_count * 0.2
            length_score = min(len(content) / 200, 1.0)
            
            # 内容质量加分
            emotion_bonus = 0.3 if any(keyword in content for keyword in self.emotion_keywords) else 0
            experience_bonus = 0.5 if any(keyword in content for keyword in self.experience_keywords) else 0
            
            # 时间相关性（较新的内容略微加分）
            days_ago = (datetime.now() - tweet.date).days if tweet.date else 365
            recency_bonus = max(0, (365 - days_ago) / 365 * 0.2)
            
            total_score = (
                engagement_score * 0.3 +
                length_score * 0.2 +
                emotion_bonus +
                experience_bonus +
                recency_bonus
            )
            
            return max(0.0, min(total_score, 10.0))
            
        except Exception as e:
            logger.debug(f"计算采样分数时出错: {e}")
            return 1.0
    
    def _calculate_content_stats(self, all_content: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算内容统计信息"""
        if not all_content:
            return {}
        
        total_length = sum(len(content['text']) for content in all_content)
        total_likes = sum(content.get('like_count', 0) for content in all_content)
        total_retweets = sum(content.get('retweet_count', 0) for content in all_content)
        
        return {
            'total_posts': len(all_content),
            'avg_length': total_length / len(all_content),
            'total_engagement': total_likes + total_retweets,
            'avg_likes': total_likes / len(all_content),
            'avg_retweets': total_retweets / len(all_content)
        }
    
    async def close(self):
        """关闭Twitter服务"""
        logger.info("📴 Twitter服务已关闭")
    
    async def test_connection(self) -> bool:
        """测试Twitter连接"""
        try:
            # 首先测试代理连接
            if self.proxy_settings:
                logger.info(f"🌐 测试代理连接: {self.proxy_settings}")
                proxy_test = self._test_proxy(list(self.proxy_settings.values())[0])
                if not proxy_test:
                    logger.warning("⚠️  代理连接测试失败，但仍尝试Twitter连接")
            
            # 执行简单搜索测试
            test_tweets = await self._search_tweets_raw("hello", limit=1)
            if test_tweets:
                logger.info("✅ Twitter连接测试成功")
                return True
            else:
                logger.warning("⚠️  Twitter连接测试失败：无法获取数据")
                self._provide_connection_help()
                return False
        except Exception as e:
            logger.error(f"❌ Twitter连接测试失败: {e}")
            self._provide_connection_help()
            return False
    
    def _provide_connection_help(self):
        """提供连接故障排除帮助"""
        logger.error("💡 Twitter连接故障排除建议:")
        logger.error("1. 确保网络连接正常")
        logger.error("2. 检查代理设置:")
        logger.error("   - Clash通常使用端口7890")
        logger.error("   - V2Ray通常使用端口1080")
        logger.error("   - 设置环境变量: set HTTP_PROXY=http://127.0.0.1:7890")
        logger.error("3. 确保代理软件正在运行且允许局域网连接")
        logger.error("4. 尝试在浏览器中访问Twitter确认代理可用")
        
        if not self.proxy_settings:
            logger.error("⚠️  当前未检测到代理配置")
            logger.error("💡 请启动Clash或设置环境变量后重试") 