#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细结果保存管理器 v2.0
增强版，支持内存缓冲、异步写入和丰富的指标处理
"""

import asyncio
import gzip
import json
import logging
import os
import shutil
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, date
from enum import Enum
from pathlib import Path
from queue import Queue, Empty
from threading import Thread, RLock
from typing import Any, Dict, List, Optional, NamedTuple

# 尝试导入可选依赖
try:
    import portalocker
    HAS_PORTALOCKER = True
except ImportError:
    HAS_PORTALOCKER = False
    logging.warning("portalocker not available, file locking disabled")

# 导入配置
try:
    from ..config import settings
except ImportError:
    # 创建简单的设置类作为后备
    class SimpleSettings:
        detailed_results_dir = "cursor_test/detailed_results"
        detailed_results_flush_interval = 30
        detailed_results_flush_size = 100
        detailed_results_max_debug_entries = 1000
        detailed_results_enable_file_lock = False
        detailed_results_compression = False
    
    settings = SimpleSettings()

logger = logging.getLogger(__name__)

# ============================================================================
# JSON 序列化辅助函数
# ============================================================================

def json_serial_default(o: Any) -> str:
    """处理 datetime 等非原生 JSON 支持的类型"""
    if isinstance(o, (datetime, date)):
        return o.isoformat()
    raise TypeError(f"Object of type {o.__class__.__name__} is not JSON serializable")

# ============================================================================
# 错误类定义
# ============================================================================

class DetailedResultsError(Exception):
    """详细结果管理器基础异常"""
    pass

class SessionNotFoundError(DetailedResultsError):
    """会话未找到异常"""
    pass

class SessionAlreadyExistsError(DetailedResultsError):
    """会话已存在异常"""
    pass

class InvalidSessionStateError(DetailedResultsError):
    """无效会话状态异常"""
    pass

class FileLockError(DetailedResultsError):
    """文件锁异常"""
    pass

class MetricHandlerError(DetailedResultsError):
    """指标处理器异常"""
    pass

# ============================================================================
# 数据结构定义
# ============================================================================

class SessionStatus(Enum):
    """会话状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    ABORTED = "aborted"
    ERROR = "error"

class OperationResult(NamedTuple):
    """操作结果"""
    success: bool
    message: str = ""
    data: Any = None

@dataclass
class SessionInfo:
    """会话信息"""
    session_id: str
    start_time: str
    end_time: Optional[str] = None
    status: SessionStatus = SessionStatus.PENDING
    user_prompt: str = ""
    total_execution_time: Optional[float] = None
    version: str = "2.0"

@dataclass
class SubtaskResult:
    """子任务结果"""
    task_name: str
    timestamp: str
    execution_time_seconds: Optional[float] = None
    status: str = "success"
    data: Dict[str, Any] = field(default_factory=dict)
    metrics: Dict[str, Any] = field(default_factory=dict)
    error_info: Optional[Dict[str, Any]] = None

@dataclass
class DebugEntry:
    """调试信息条目"""
    timestamp: str
    category: str
    data: Dict[str, Any]

@dataclass
class SessionResults:
    """完整会话结果"""
    session_info: SessionInfo
    subtasks: Dict[str, SubtaskResult]
    performance_metrics: Dict[str, Any]
    error_logs: List[Dict[str, Any]]
    debug_info: Dict[str, List[DebugEntry]]
    statistics: Optional[Dict[str, Any]] = None
    final_results: Optional[Dict[str, Any]] = None

# ============================================================================
# 指标处理器系统
# ============================================================================

class MetricHandler(ABC):
    """指标处理器抽象基类"""
    
    @abstractmethod
    def can_handle(self, task_name: str, data: Dict[str, Any]) -> bool:
        """判断是否可以处理指定的任务数据"""
        pass
    
    @abstractmethod
    def calculate_metrics(self, task_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """计算指标"""
        pass

class MetricHandlerRegistry:
    """指标处理器注册表"""
    
    def __init__(self):
        self._handlers: List[MetricHandler] = []
    
    def register(self, handler: MetricHandler) -> None:
        """注册处理器"""
        if handler not in self._handlers:
            self._handlers.append(handler)
            logger.debug(f"注册指标处理器: {handler.__class__.__name__}")
    
    def unregister(self, handler: MetricHandler) -> None:
        """取消注册处理器"""
        if handler in self._handlers:
            self._handlers.remove(handler)
            logger.debug(f"取消注册指标处理器: {handler.__class__.__name__}")
    
    def calculate_metrics(self, task_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """计算所有适用的指标"""
        combined_metrics = {}
        
        for handler in self._handlers:
            try:
                if handler.can_handle(task_name, data):
                    metrics = handler.calculate_metrics(task_name, data)
                    if metrics:
                        # 使用处理器类名作为命名空间
                        handler_name = handler.__class__.__name__.replace('MetricHandler', '').lower()
                        combined_metrics[handler_name] = metrics
            except Exception as e:
                logger.error(f"指标处理器 {handler.__class__.__name__} 出错: {e}")
                combined_metrics[f"{handler.__class__.__name__}_error"] = str(e)
        
        # 添加基础指标
        basic_metrics = {
            "data_size_bytes": len(str(data).encode('utf-8')),
            "data_keys_count": len(data) if isinstance(data, dict) else 0,
            "calculation_timestamp": datetime.now().isoformat()
        }
        combined_metrics['basic'] = basic_metrics
        
        return combined_metrics

# ============================================================================
# 具体指标处理器
# ============================================================================

class SemanticAnalysisMetricHandler(MetricHandler):
    """语义分析指标处理器"""
    
    def can_handle(self, task_name: str, data: Dict[str, Any]) -> bool:
        return 'semantic_analysis' in task_name.lower() or 'embeddings' in data
    
    def calculate_metrics(self, task_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        metrics = {}
        if 'embeddings' in data:
            embeddings = data['embeddings']
            if isinstance(embeddings, list):
                metrics['embedding_count'] = len(embeddings)
                if embeddings and isinstance(embeddings[0], list):
                    metrics['embedding_dimension'] = len(embeddings[0])
        return metrics

class RedditPostsMetricHandler(MetricHandler):
    """Reddit帖子指标处理器"""
    
    def can_handle(self, task_name: str, data: Dict[str, Any]) -> bool:
        return 'reddit' in task_name.lower() or 'posts' in data or 'submissions' in data
    
    def calculate_metrics(self, task_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        metrics = {}
        
        posts_data = data.get('posts') or data.get('submissions') or data.get('reddit_posts', [])
        if isinstance(posts_data, list):
            metrics['posts_count'] = len(posts_data)
            if posts_data:
                # 分析文本长度分布
                text_lengths = [len(str(post.get('title', '') + post.get('text', ''))) for post in posts_data if isinstance(post, dict)]
                if text_lengths:
                    metrics['avg_text_length'] = sum(text_lengths) / len(text_lengths)
                    metrics['max_text_length'] = max(text_lengths)
                    metrics['min_text_length'] = min(text_lengths)
        
        return metrics

class UserCandidatesMetricHandler(MetricHandler):
    """用户候选指标处理器"""
    
    def can_handle(self, task_name: str, data: Dict[str, Any]) -> bool:
        return 'candidates' in task_name.lower() or 'user_candidates' in data or 'matched_users' in data
    
    def calculate_metrics(self, task_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        metrics = {}
        
        candidates = data.get('user_candidates') or data.get('matched_users') or data.get('candidates', [])
        if isinstance(candidates, list):
            metrics['candidates_count'] = len(candidates)
        
        return metrics

# ============================================================================
# 缓冲写入器
# ============================================================================

class BufferedWriter:
    """缓冲写入器，实现异步批量写入"""
    
    def __init__(self, flush_interval: int = 30, flush_size: int = 100, 
                 enable_compression: bool = False):
        self.flush_interval = flush_interval
        self.flush_size = flush_size
        self.enable_compression = enable_compression
        
        self._buffer_queue = Queue()
        self._worker_thread = None
        self._stop_flag = False
        
        logger.info(f"缓冲写入器初始化: 间隔{flush_interval}s, 大小阈值{flush_size}")
    
    def start(self):
        """启动后台写入线程"""
        if self._worker_thread is None or not self._worker_thread.is_alive():
            self._stop_flag = False
            self._worker_thread = Thread(target=self._flush_worker, daemon=True)
            self._worker_thread.start()
            logger.info("缓冲写入线程已启动")
    
    def stop(self):
        """停止写入线程并刷新剩余数据"""
        self._stop_flag = True
        if self._worker_thread and self._worker_thread.is_alive():
            self._worker_thread.join(timeout=5)
            logger.info("缓冲写入线程已停止")
        
        # 刷新剩余数据
        self._flush_remaining()
    
    def add_to_buffer(self, file_path: str, data: Dict[str, Any]) -> None:
        """添加数据到缓冲区"""
        timestamp = datetime.now().isoformat()
        self._buffer_queue.put((file_path, data, timestamp))
    
    def _flush_worker(self):
        """后台刷新工作线程"""
        import time
        
        last_flush_time = time.time()
        
        while not self._stop_flag:
            try:
                current_time = time.time()
                
                # 检查时间间隔或队列大小
                if (current_time - last_flush_time >= self.flush_interval or 
                    self._buffer_queue.qsize() >= self.flush_size):
                    
                    self._flush_batch()
                    last_flush_time = current_time
                
                time.sleep(1)  # 避免过度轮询
                
            except Exception as e:
                logger.error(f"缓冲写入器工作线程出错: {e}")
    
    def _flush_batch(self):
        """批量刷新缓冲区数据"""
        if self._buffer_queue.empty():
            return
        
        # 收集待写入数据，按文件路径分组
        pending_writes = {}
        
        # 从队列中获取数据
        while not self._buffer_queue.empty():
            try:
                file_path, data, timestamp = self._buffer_queue.get_nowait()
                pending_writes[file_path] = data  # 保留最新数据
            except Empty:
                break
        
        # 执行写入
        for file_path, data in pending_writes.items():
            try:
                self._write_file(file_path, data)
                logger.debug(f"成功写入: {file_path}")
            except Exception as e:
                logger.error(f"写入失败 {file_path}: {e}")
    
    def _flush_remaining(self):
        """刷新所有剩余数据"""
        remaining = {}
        while not self._buffer_queue.empty():
            try:
                file_path, data, timestamp = self._buffer_queue.get_nowait()
                remaining[file_path] = data  # 保留最新数据
            except Empty:
                pass
        
        for file_path, data in remaining.items():
            try:
                self._write_file(file_path, data)
            except Exception as e:
                logger.error(f"最终刷新失败 {file_path}: {e}")
    
    def _write_file(self, file_path: str, data: Dict[str, Any]):
        """写入文件（带锁保护）"""
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 序列化数据
        json_data = json.dumps(data, ensure_ascii=False, indent=2, default=json_serial_default)
        
        if settings.detailed_results_enable_file_lock and HAS_PORTALOCKER:
            # 使用文件锁
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    portalocker.lock(f, portalocker.LOCK_EX)
                    f.write(json_data)
                    portalocker.unlock(f)
            except Exception as e:
                raise FileLockError(f"文件锁写入失败: {e}") from e
        else:
            # 普通写入
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(json_data)
        
        # 可选压缩
        if self.enable_compression:
            compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            # 删除原文件
            file_path.unlink()

# ============================================================================
# 主要管理器类
# ============================================================================

class DetailedResultsManager:
    """
    详细结果保存管理器 v2.0
    
    简化版实现，解决了缩进和语法错误
    """
    
    def __init__(self, results_dir: Optional[str] = None):
        """初始化详细结果管理器"""
        # 配置参数
        self.results_dir = Path(results_dir or settings.detailed_results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # 会话管理
        self.session_id = None
        self.file_path = None
        self.results = {}
        self.session_info = {}
        
        # 同步缓冲
        self.buffer = []
        self.last_flush_time = datetime.now()
        
        logger.info(f"详细结果管理器初始化完成，保存目录: {self.results_dir}")
    
    def _initialize_results(self):
        """初始化一个新的结果字典"""
        self.results = {
            "session_info": self.session_info,
            "subtask_results": {},
            "performance_metrics": {"subtask_timings": {}},
            "error_logs": [],
            "final_results": None,
        }

    def _load_existing_results(self):
        """如果结果文件已存在，则加载它"""
        if self.file_path and os.path.exists(self.file_path):
            try:
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    self.results = json.load(f)
                # 确保关键键存在
                self.results.setdefault('session_info', {})
                self.results.setdefault('subtask_results', {})
                self.results.setdefault('performance_metrics', {"subtask_timings": {}})
                self.results.setdefault('error_logs', [])
                logger.info(f"从 {self.file_path} 加载了现有结果")
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"加载现有结果文件失败: {e}，将创建新文件。")
                self._initialize_results()
        else:
            self._initialize_results()

    def start_session(self, user_prompt: str, session_id: Optional[str] = None) -> str:
        """开始一个新的执行会话"""
        if session_id:
            self.session_id = session_id
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            random_hash = uuid.uuid4().hex[:8]
            self.session_id = f"detailed_results_{timestamp}_{random_hash}"

        self.file_path = os.path.join(self.results_dir, f"{self.session_id}.json")
        
        self.session_info = {
            "session_id": self.session_id,
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "status": "running",
            "user_prompt": user_prompt,
            "total_execution_time": None,
            "version": "2.0"
        }
        self._initialize_results()
        logger.info(f"开始新的执行会话: {self.session_id}")
        return self.session_id

    def continue_session(self, session_id: str):
        """继续一个已存在的会话"""
        self.session_id = session_id
        self.file_path = os.path.join(self.results_dir, f"{session_id}.json")
        self._load_existing_results()
        self.session_info = self.results.get('session_info', {})
        self.session_info['status'] = 'running (continued)'
        self.results['session_info'] = self.session_info
        logger.info(f"继续会话: {session_id}，结果将写入 {self.file_path}")

    def save_subtask_result(self, task_name: str, task_data: Dict[str, Any], execution_time: float = 0.0, 
                            error_info: Optional[Dict[str, Any]] = None, force_flush: bool = False):
        """保存子任务的详细结果"""
        task_result = {
            "task_name": task_name,
            "timestamp": datetime.now().isoformat(),
            "execution_time_seconds": round(execution_time, 4),
            "status": "success" if error_info is None else "failed",
            "data": task_data,
            "metrics": {
                "data_size": len(str(task_data)),
                "data_type": str(type(task_data).__name__),
                "timestamp": datetime.now().isoformat()
            },
            "error_info": error_info
        }
        
        # 将结果添加到缓冲区
        self.buffer.append(task_result)
        
        logger.info(f"子任务结果已保存: {task_name} (执行时间: {execution_time:.2f}s)")
        
        # 检查是否应该触发写入
        if force_flush or len(self.buffer) >= 5 or (datetime.now() - self.last_flush_time).total_seconds() > 10:
            self.flush_buffer()
    
    def flush_buffer(self):
        """将缓冲区的数据写入文件"""
        if not self.buffer:
            return
            
        current_buffer = self.buffer[:]
        self.buffer.clear()
        self.last_flush_time = datetime.now()

        try:
            # 合并新旧结果
            for result in current_buffer:
                # 使用带时间戳的唯一键
                task_key = f"{result['task_name']}_{result['timestamp']}"
                self.results['subtask_results'][task_key] = result
                self.results['performance_metrics']['subtask_timings'][result['task_name']] = result['execution_time_seconds']
                if result['error_info']:
                    self.results['error_logs'].append({
                        "task_name": result['task_name'],
                        "timestamp": result['timestamp'],
                        "error": result['error_info']
                    })
            
            # 写入文件
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2, default=json_serial_default)
            
            logger.info(f"缓冲数据已成功写入 {self.file_path}")
            
        except TypeError as e:
            logger.error(f"保存会话失败: {e}")
            # 尝试保存简化版本
            simplified_results = self._simplify_for_error(self.results)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(simplified_results, f, ensure_ascii=False, indent=2)
            logger.warning("⚠️ 已保存简化版结果")
        except Exception as e:
            logger.error(f"批量写入失败 {self.file_path}: {e}")
            # 如果写入失败，将数据放回缓冲区以便下次重试
            self.buffer.extend(current_buffer)
    
    def complete_session(self, final_results: Dict[str, Any]) -> str:
        """完成会话并保存最终结果"""
        self.results['final_results'] = final_results
        self.session_info['end_time'] = datetime.now().isoformat()
        self.session_info['status'] = 'completed' if final_results.get('success', False) else 'failed'
        
        start_time = datetime.fromisoformat(self.session_info['start_time'])
        end_time = datetime.fromisoformat(self.session_info['end_time'])
        self.session_info['total_execution_time'] = (end_time - start_time).total_seconds()
        
        self.results['session_info'] = self.session_info
        
        # 确保所有缓冲数据都已写入
        self.flush_buffer()
        
        # 最终保存
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2, default=json_serial_default)
            logger.info(f"✅ 详细结果已保存到: {self.file_path}")
        except TypeError as e:
            logger.error(f"最终保存时序列化失败: {e}")
            # 尝试保存不含图谱的简化版本
            simplified_results = self._simplify_for_error(self.results)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(simplified_results, f, ensure_ascii=False, indent=2)
            logger.warning("⚠️ 已保存简化版结果")
        
        return self.file_path

    def _simplify_for_error(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """在序列化失败时，简化数据结构"""
        if isinstance(data, dict):
            return {k: self._simplify_for_error(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._simplify_for_error(v) for v in data]
        elif not isinstance(data, (str, int, float, bool, type(None))):
            return f"<unserializable:{type(data).__name__}>"
        return data

    def close(self):
        """关闭管理器，确保所有数据都已写入"""
        self.flush_buffer()

# ============================================================================
# 全局实例
# ============================================================================

# 创建全局实例
_global_manager: Optional[DetailedResultsManager] = None

def get_global_manager() -> DetailedResultsManager:
    """获取全局详细结果管理器实例"""
    global _global_manager
    if _global_manager is None:
        _global_manager = DetailedResultsManager()
    return _global_manager

def cleanup_global_manager():
    """清理全局管理器"""
    global _global_manager
    if _global_manager is not None:
        _global_manager.close()
        _global_manager = None 