"""
集成服务 - 整合所有核心模块提供完整的AI画像匹配流程
"""
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ..core.semantic_analyzer import SemanticAnalyzer
from ..core.graph_builder import GraphBuilder
from ..core.resonance_matcher import ResonanceMatcher
from ..services.reddit_service import RedditService
from ..services.graph_service import GraphService
from ..services.ai_service import AIService
from ..models.user_models import UserProfile, ParsedQuery
from ..models.graph_models import UserGraph
from ..models.matching_models import MatchingResult, MatchingContext, RankedCandidate

logger = logging.getLogger(__name__)


class IntegrationService:
    """
    集成服务 - CogBridges AI画像匹配系统的核心入口
    
    提供完整的端到端功能：
    1. 用户输入处理和画像构建
    2. Reddit用户数据获取和分析
    3. 图谱构建和相似度匹配
    4. 个性化推荐生成
    """
    
    def __init__(self):
        """初始化集成服务"""
        logger.info("初始化CogBridges集成服务...")
        
        # 初始化核心组件
        self.semantic_analyzer = SemanticAnalyzer()
        self.graph_builder = GraphBuilder()
        self.user_profiler = None  # 延迟初始化以避免循环导入
        self.resonance_matcher = ResonanceMatcher()
        
        # 初始化外部服务
        self.reddit_service = RedditService()
        self.graph_service = GraphService()
        self.ai_service = AIService()
        
        # 系统状态
        self.is_initialized = False
        self.system_stats = {
            'total_sessions': 0,
            'successful_matches': 0,
            'processed_users': 0,
            'avg_processing_time': 0.0
        }
        
        logger.info("CogBridges集成服务初始化完成")
    
    def _ensure_user_profiler_initialized(self):
        """确保UserProfiler已初始化（延迟初始化以避免循环导入）"""
        if self.user_profiler is None:
            from ..core.user_profiler import UserProfiler
            self.user_profiler = UserProfiler(
                semantic_analyzer=self.semantic_analyzer,
                graph_builder=self.graph_builder,
                ai_service=self.ai_service
            )
    
    async def initialize(self) -> bool:
        """初始化系统组件"""
        try:
            logger.info("正在初始化系统组件...")
            
            # 测试各服务连接
            reddit_ok = await self.reddit_service.test_connection()
            ai_ok = await self.ai_service.test_connection()
            
            if not reddit_ok:
                logger.warning("Reddit API连接失败，某些功能可能受限")
            
            if not ai_ok:
                logger.warning("AI服务连接失败，将使用降级模式")
            
            self.is_initialized = True
            logger.info("系统组件初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"系统初始化失败：{e}")
            return False
    
    async def process_user_query(self, user_input: str, 
                               user_contents: Optional[List[str]] = None,
                               matching_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理完整的用户查询流程
        
        Args:
            user_input: 用户输入
            user_contents: 用户额外内容
            matching_options: 匹配选项配置
            
        Returns:
            完整的匹配结果
        """
        start_time = datetime.now()
        session_id = f"session_{int(start_time.timestamp())}"
        
        try:
            logger.info(f"开始处理用户查询会话：{session_id}")
            logger.info(f"用户输入：{user_input[:100]}...")
            
            # 步骤1：构建用户画像
            logger.info("步骤1：构建用户画像")
            user_profile = await self._build_user_profile(user_input, user_contents)
            
            if not user_profile:
                return self._create_error_response("用户画像构建失败")
            
            # 步骤2：获取候选用户
            logger.info("步骤2：获取候选用户")
            candidate_profiles = await self._get_candidate_profiles(user_profile, matching_options)
            
            if not candidate_profiles:
                return self._create_error_response("未找到合适的候选用户")
            
            # 步骤3：执行共鸣匹配
            logger.info("步骤3：执行共鸣匹配")
            matching_result = await self._perform_resonance_matching(
                user_profile, candidate_profiles, matching_options
            )
            
            # 步骤4：生成推荐摘要
            logger.info("步骤4：生成推荐摘要")
            recommendations = await self._generate_recommendations(
                user_profile, matching_result
            )
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 更新统计信息
            self._update_system_stats(processing_time, len(matching_result.ranked_candidates))
            
            # 构建最终结果
            final_result = {
                'session_id': session_id,
                'success': True,
                'user_profile_summary': user_profile.to_summary(),
                'matching_result': matching_result,
                'recommendations': recommendations,
                'processing_time': processing_time,
                'system_stats': self.system_stats.copy()
            }
            
            logger.info(f"查询处理完成，会话：{session_id}，耗时：{processing_time:.2f}秒")
            return final_result
            
        except Exception as e:
            logger.error(f"处理用户查询失败：{e}")
            return self._create_error_response(f"系统处理失败：{str(e)}")
    
    async def _build_user_profile(self, user_input: str, 
                                user_contents: Optional[List[str]] = None) -> Optional[UserProfile]:
        """构建用户画像"""
        try:
            # 确保UserProfiler已初始化
            self._ensure_user_profiler_initialized()
            
            # 使用UserProfiler构建完整画像
            user_profile = await self.user_profiler.build_comprehensive_profile(
                user_input=user_input,
                user_contents=user_contents or []
            )
            
            # 存储用户图谱到图谱服务
            if user_profile and user_profile.graph:
                self.graph_service.store_user_graph(
                    user_profile.user_id, user_profile.graph
                )
            
            return user_profile
            
        except Exception as e:
            logger.error(f"构建用户画像失败：{e}")
            return None
    
    async def _get_candidate_profiles(self, user_profile: UserProfile,
                                    matching_options: Optional[Dict[str, Any]] = None) -> List[UserProfile]:
        """获取候选用户画像"""
        try:
            options = matching_options or {}
            
            # 方式1：基于语义搜索Reddit用户
            candidate_profiles = []
            
            # 提取搜索关键词
            parsed_query = await self.semantic_analyzer.parse_user_input(user_profile.original_query)
            search_keywords = parsed_query.get_search_keywords()
            
            if search_keywords:
                # 搜索相关帖子和优质评论者
                posts = await self.reddit_service.search_posts_by_keywords_enhanced(
                    search_keywords, limit=50
                )
                
                commenters = await self.reddit_service.extract_quality_commenters(
                    [{'id': p['id']} for p in posts], max_commenters=30
                )
                
                # 获取候选用户的详细数据并构建画像
                candidate_profiles = await self._build_candidate_profiles(commenters[:20])
            
            # 方式2：从图谱服务查找相似图谱
            if user_profile.graph:
                similar_graphs = self.graph_service.find_similar_graphs(
                    user_profile.graph, 
                    similarity_threshold=0.3,
                    limit=10
                )
                
                for user_id, similarity in similar_graphs:
                    stored_graph = self.graph_service.retrieve_user_graph(user_id)
                    if stored_graph:
                        # 创建基于存储图谱的用户画像
                        candidate_profile = UserProfile(
                            user_id=user_id,
                            original_query="",  # 候选用户没有原始查询
                            graph=stored_graph
                        )
                        candidate_profiles.append(candidate_profile)
            
            logger.info(f"获取到 {len(candidate_profiles)} 个候选用户画像")
            return candidate_profiles
            
        except Exception as e:
            logger.error(f"获取候选用户失败：{e}")
            return []
    
    async def _build_candidate_profiles(self, usernames: List[str]) -> List[UserProfile]:
        """为候选用户构建画像"""
        candidate_profiles = []
        
        for username in usernames:
            try:
                # 获取用户完整历史
                user_data = await self.reddit_service.get_user_comprehensive_history(username)
                
                if user_data and user_data.get('content_stats', {}).get('total_content_length', 0) > 500:
                    # 提取所有内容文本
                    contents = []
                    for post in user_data.get('posts', []):
                        contents.append(f"{post['title']}\n{post['text']}")
                    
                    for comment in user_data.get('comments', []):
                        contents.append(comment['text'])
                    
                    # 构建用户画像
                    if contents:
                        # 确保UserProfiler已初始化
                        self._ensure_user_profiler_initialized()
                        
                        candidate_profile = await self.user_profiler.build_comprehensive_profile(
                            user_input=f"用户{username}的内容",
                            user_contents=contents[:10]  # 限制内容数量
                        )
                        
                        if candidate_profile:
                            # 更新用户ID为实际的Reddit用户名
                            candidate_profile.user_id = username
                            candidate_profiles.append(candidate_profile)
                            
                            # 存储到图谱服务
                            self.graph_service.store_user_graph(username, candidate_profile.graph)
                
            except Exception as e:
                logger.warning(f"构建候选用户 {username} 画像失败：{e}")
                continue
        
        logger.info(f"成功构建 {len(candidate_profiles)} 个候选用户画像")
        return candidate_profiles
    
    async def _perform_resonance_matching(self, user_profile: UserProfile,
                                        candidate_profiles: List[UserProfile],
                                        matching_options: Optional[Dict[str, Any]] = None) -> MatchingResult:
        """执行共鸣匹配"""
        try:
            options = matching_options or {}
            
            # 配置匹配参数
            max_results = options.get('max_results', 5)
            custom_weights = options.get('weights')
            
            # 执行候选用户排序
            ranked_candidates = await self.resonance_matcher.rank_candidates(
                user_profile=user_profile,
                candidate_profiles=candidate_profiles,
                weights=custom_weights,
                max_results=max_results
            )
            
            # 创建匹配上下文
            context = MatchingContext(
                user_profile=user_profile,
                candidate_pool_size=len(candidate_profiles),
                search_parameters=options,
                matching_strategy="hybrid",
                weights=custom_weights or self.resonance_matcher.default_weights,
                max_results=max_results
            )
            
            # 创建匹配结果
            matching_result = MatchingResult(
                context=context,
                ranked_candidates=ranked_candidates,
                total_candidates_processed=len(candidate_profiles),
                candidates_above_threshold=len(ranked_candidates)
            )
            
            return matching_result
            
        except Exception as e:
            logger.error(f"执行共鸣匹配失败：{e}")
            return MatchingResult(
                context=MatchingContext(
                    user_profile=user_profile,
                    candidate_pool_size=0,
                    search_parameters={}
                ),
                ranked_candidates=[],
                total_candidates_processed=0,
                candidates_above_threshold=0
            )
    
    async def _generate_recommendations(self, user_profile: UserProfile,
                                      matching_result: MatchingResult) -> List[Dict[str, Any]]:
        """生成推荐摘要"""
        recommendations = []
        
        try:
            for candidate in matching_result.ranked_candidates:
                # 生成详细推荐摘要
                recommendation_summary = await self.resonance_matcher.generate_recommendation_summary(
                    user_profile, candidate
                )
                
                # 构建推荐数据
                recommendation = {
                    'rank': candidate.rank,
                    'candidate_id': candidate.candidate.username,
                    'resonance_score': candidate.resonance_score.overall_score,
                    'recommendation_strength': candidate.get_recommendation_strength(),
                    'resonance_breakdown': {
                        'structural_similarity': candidate.resonance_score.structural_similarity,
                        'semantic_similarity': candidate.resonance_score.semantic_similarity,
                        'emotional_alignment': candidate.resonance_score.emotional_alignment,
                        'value_compatibility': candidate.resonance_score.value_compatibility,
                        'experience_overlap': candidate.resonance_score.experience_overlap
                    },
                    'strength_indicators': candidate.resonance_score.get_strength_indicators(),
                    'primary_reasons': candidate.primary_resonance_reasons,
                    'shared_themes': candidate.resonance_score.shared_themes,
                    'summary': recommendation_summary.summary_text,
                    'conversation_starters': recommendation_summary.conversation_starters,
                    'interaction_suggestions': {
                        'approach': recommendation_summary.suggested_approach,
                        'tone': recommendation_summary.tone_recommendation
                    },
                    'connection_points': recommendation_summary.key_connection_points
                }
                
                recommendations.append(recommendation)
            
            logger.info(f"生成 {len(recommendations)} 个推荐摘要")
            return recommendations
            
        except Exception as e:
            logger.error(f"生成推荐摘要失败：{e}")
            return []
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            'success': False,
            'error': error_message,
            'recommendations': [],
            'processing_time': 0.0,
            'system_stats': self.system_stats.copy()
        }
    
    def _update_system_stats(self, processing_time: float, matches_found: int) -> None:
        """更新系统统计信息"""
        self.system_stats['total_sessions'] += 1
        if matches_found > 0:
            self.system_stats['successful_matches'] += 1
        
        # 更新平均处理时间
        total_sessions = self.system_stats['total_sessions']
        current_avg = self.system_stats['avg_processing_time']
        self.system_stats['avg_processing_time'] = (
            (current_avg * (total_sessions - 1) + processing_time) / total_sessions
        )
    
    async def analyze_user_completeness(self, user_profile: UserProfile) -> Dict[str, Any]:
        """分析用户画像完整性并生成追问建议"""
        try:
            # 确保UserProfiler已初始化
            self._ensure_user_profiler_initialized()
            
            # 识别缺失维度
            missing_dimensions = self.user_profiler.identify_missing_dimensions(user_profile)
            
            # 生成追问问题
            questions = await self.user_profiler.generate_clarifying_questions(
                user_profile, max_questions=3
            )
            
            return {
                'completeness_score': user_profile.completeness_score,
                'missing_dimensions': missing_dimensions,
                'follow_up_questions': questions,
                'is_complete': user_profile.completeness_score >= 0.8
            }
            
        except Exception as e:
            logger.error(f"分析用户完整性失败：{e}")
            return {
                'completeness_score': 0.0,
                'missing_dimensions': [],
                'follow_up_questions': [],
                'is_complete': False
            }
    
    async def update_user_profile_with_answers(self, user_profile: UserProfile,
                                             questions_and_answers: Dict[str, str]) -> UserProfile:
        """基于用户回答更新画像"""
        try:
            # 确保UserProfiler已初始化
            self._ensure_user_profiler_initialized()
            
            updated_profile = await self.user_profiler.update_profile_with_answers(
                user_profile, questions_and_answers
            )
            
            # 更新图谱服务中的存储
            if updated_profile and updated_profile.graph:
                self.graph_service.store_user_graph(
                    updated_profile.user_id, updated_profile.graph
                )
            
            return updated_profile
            
        except Exception as e:
            logger.error(f"更新用户画像失败：{e}")
            return user_profile
    
    async def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            # 测试各组件状态
            reddit_status = await self.reddit_service.test_connection()
            ai_status = await self.ai_service.test_connection()
            
            # 获取图谱服务统计
            graph_stats = self.graph_service.get_storage_stats()
            
            # 获取匹配器统计
            matcher_stats = self.resonance_matcher.get_stats()
            
            return {
                'system_initialized': self.is_initialized,
                'service_status': {
                    'reddit_service': 'healthy' if reddit_status else 'degraded',
                    'ai_service': 'healthy' if ai_status else 'degraded',
                    'graph_service': 'healthy',
                    'integration_service': 'healthy'
                },
                'system_stats': self.system_stats,
                'graph_storage_stats': graph_stats,
                'matcher_stats': matcher_stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取系统健康状态失败：{e}")
            return {
                'system_initialized': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def close(self) -> None:
        """关闭集成服务"""
        try:
            logger.info("正在关闭CogBridges集成服务...")
            
            # 关闭各个组件
            await self.semantic_analyzer.close()
            await self.graph_builder.close()
            if self.user_profiler is not None:
                await self.user_profiler.close()
            await self.resonance_matcher.close()
            await self.reddit_service.close()
            await self.ai_service.close()
            
            self.graph_service.close()
            
            logger.info("CogBridges集成服务已关闭")
            
        except Exception as e:
            logger.error(f"关闭集成服务失败：{e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        return {
            'integration_service': self.system_stats.copy(),
            'is_initialized': self.is_initialized
        }