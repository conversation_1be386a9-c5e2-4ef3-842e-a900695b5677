"""
语义解析器 - 负责用户输入的深度理解和结构化解析
实现业务架构中的子任务A+B：语义解析与搜索意图提取
"""
import json
import logging
import hashlib
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timedelta
import re
import sys
import os

from ..models.user_models import ParsedQuery, UserProfile
from ..models.graph_models import UserGraph, NodeType

logger = logging.getLogger(__name__)

_ai_service_instance = None

def get_ai_service():
    """获取AIService的单例"""
    global _ai_service_instance
    if _ai_service_instance is None:
        from ..services.ai_service import AIService
        _ai_service_instance = AIService()
    return _ai_service_instance


class SemanticAnalyzer:
    """
    语义解析器 - 负责用户输入的深度理解
    
    核心功能：
    1. 解析用户输入，提取搜索意图和价值观信息
    2. 生成适合Reddit搜索的关键词
    3. 识别用户画像中缺失的重要维度
    4. 深度分析情绪状态、表达风格和价值观取向
    """
    
    def __init__(self, ai_service: Optional['AIService'] = None):
        """初始化语义解析器"""
        self.ai_service = ai_service or get_ai_service()
        
        # 缓存机制
        self._cache: Dict[str, Tuple[ParsedQuery, datetime]] = {}
        self._cache_ttl = timedelta(hours=24)  # 缓存24小时
        self._max_cache_size = 1000
        
        # 配置参数
        self.similarity_threshold = 0.85  # 语义相似度阈值
        self.max_keywords = 8  # 最大关键词数
        self.ai_analysis_enabled = True  # AI分析开关
        
        logger.info("语义解析器初始化完成")
    
    async def parse_user_input(self, query_text: str) -> ParsedQuery:
        """解析用户输入，提取搜索意图和价值观信息"""
        logger.info(f"开始解析用户输入：{query_text[:50]}...")
        
        # 检查缓存
        cache_key = self._get_cache_key(query_text)
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            logger.info("使用缓存的解析结果")
            return cached_result
        
        parsed_query = None
        
        # 优先尝试AI深度分析
        if self.ai_analysis_enabled and self.ai_service:
            try:
                logger.info("尝试使用AI进行深度语义分析...")
                parsed_query = await self._ai_deep_parse(query_text)
                logger.info(f"AI分析成功，置信度: {parsed_query.confidence:.3f}")
                
            except Exception as e:
                logger.warning(f"AI分析失败，将使用降级策略: {e}")
                parsed_query = None
        
        # 降级策略：基础关键词提取
        if parsed_query is None:
            logger.info("使用降级策略进行基础解析...")
            parsed_query = self._fallback_parse(query_text)
        
        # 存入缓存
        self._cache_result(cache_key, parsed_query)
        
        # 最终保障：确保搜索关键词都是英文
        parsed_query.search_intent = self._ensure_english_keywords(parsed_query.search_intent)
        
        # 关键词优化：根据常见同义词和相关概念扩展搜索关键词，提高Reddit搜索覆盖面
        parsed_query.search_intent = self._optimize_keywords(parsed_query.search_intent)
        
        logger.info(f"解析完成，提取到 {len(parsed_query.search_intent)} 个关键词，置信度: {parsed_query.confidence:.3f}")
        logger.info(f"最终搜索关键词: {parsed_query.search_intent}")
        return parsed_query
    
    async def _ai_deep_parse(self, query_text: str) -> ParsedQuery:
        """使用 LangChain 进行深度语义分析"""
        try:
            logger.info("使用 LangChain 语义分析链进行深度分析...")
            
            # 检查是否有新的 LangChain 方法
            if not hasattr(self.ai_service, 'get_semantic_analysis_chain'):
                logger.warning("AI服务未提供语义分析链，使用兼容模式")
                return await self._ai_deep_parse_legacy(query_text)
            
            # 获取语义分析链
            semantic_chain = self.ai_service.get_semantic_analysis_chain()
            
            # 使用 LangChain 链进行分析
            from ..models.ai_response_models import SemanticAnalysisResult
            analysis_result: SemanticAnalysisResult = await semantic_chain.ainvoke(query_text)
            
            logger.info(f"LangChain 分析成功，置信度: {analysis_result.confidence:.3f}")
            
            # 获取 embedding
            embedding = await self.ai_service.get_embedding(query_text)
            
            # 转换为 ParsedQuery 格式
            from ..models import EmotionType as OriginalEmotionType, ExpressionStyle as OriginalExpressionStyle
            
            # 将情绪状态字典转换为原有格式
            emotional_state = analysis_result.emotional_state
            
            # 提取表达风格（基于情绪状态推断）
            expression_style = self._infer_expression_style(emotional_state, analysis_result.values_info)
            
            return ParsedQuery(
                original_text=query_text,
                search_intent=analysis_result.search_keywords,
                values_info=analysis_result.values_info,
                emotional_state=emotional_state,
                topics=analysis_result.topics,
                confidence=analysis_result.confidence,
                core_concerns=analysis_result.core_concerns,
                decision_points=analysis_result.decision_points,
                life_domains=analysis_result.life_domains,
                support_needs=analysis_result.support_needs
            )
            
        except Exception as e:
            logger.error(f"LangChain 分析失败: {e}")
            # 降级到传统方法
            return await self._ai_deep_parse_legacy(query_text)
    
    async def _ai_deep_parse_legacy(self, query_text: str) -> ParsedQuery:
        """传统的 AI 深度分析方法（作为降级策略）"""
        try:
            # 保留原有的实现逻辑
            prompt = self._build_analysis_prompt(query_text)
            
            response = await self.ai_service.get_completion(
                prompt=prompt,
                temperature=0.3,
                max_tokens=4000,  # 增加到4000以确保thinking类模型的完整输出
                system_prompt="你是一个专业的心理分析师和语义解析专家。请严格按照JSON格式返回分析结果。如果你需要思考，请在思考完成后输出完整的JSON结果。确保JSON格式正确且完整。"
            )
            
            analysis_result = self._parse_ai_response(response)
            
            # 构建ParsedQuery对象
            parsed_query = ParsedQuery(
                original_text=query_text,
                search_intent=analysis_result.get("search_keywords", []),
                values_info=analysis_result.get("values_info", {}),
                emotional_state=analysis_result.get("emotional_state", {}),
                topics=analysis_result.get("topics", []),
                confidence=analysis_result.get("confidence", 0.8),
                # 新增字段
                core_concerns=analysis_result.get("core_concerns", []),
                decision_points=analysis_result.get("decision_points", []),
                life_domains=analysis_result.get("life_domains", []),
                support_needs=analysis_result.get("support_needs", [])
            )
            
            logger.info("传统AI深度分析完成")
            return parsed_query
            
        except Exception as e:
            logger.error(f"传统AI深度分析失败: {e}")
            raise
    
    def _build_analysis_prompt(self, query_text: str) -> str:
        """构建AI分析提示词"""
        return f"""请对以下用户输入进行深度语义分析，提取关键信息。

用户输入："{query_text}"

请以JSON格式返回以下信息：
{{
    "search_keywords": ["english keyword 1", "english keyword 2"],
    "topics": ["主题1", "主题2"],
    "emotional_state": {{
        "anxiety": 0.0,
        "confusion": 0.0,  
        "hope": 0.0,
        "frustration": 0.0,
        "sadness": 0.0,
        "fear": 0.0
    }},
    "values_info": {{
        "decision_style": "unknown",
        "priority_focus": "unknown",
        "risk_tolerance": "unknown"
    }},
    "core_concerns": ["核心关切1", "核心关切2"],
    "decision_points": ["决策点1", "决策点2"],
    "life_domains": ["生活领域1", "生活领域2"],
    "support_needs": ["支持需求1", "支持需求2"],
    "confidence": 0.0
}}

【重要】分析要求：
1. **search_keywords必须是英文**：无论用户输入什么语言，search_keywords都必须是适合在Reddit上搜索的英文关键词或短语，例如："career change", "graduate school", "relationship issues", "job interview"等
2. emotional_state中的数值应该基于文本内容客观评估（0-1）
3. values_info中的分类从给定选项中选择：decision_style(analytical/emotional/avoidant/seeking)，priority_focus(career/relationship/personal_growth/stability)，risk_tolerance(high/medium/low)
4. core_concerns要抓住用户最关心的核心问题
5. 确保返回有效的JSON格式
6. **search_keywords条数要求**：请输出**不少于5个且不超过8个**高质量英文关键词；这些关键词应当覆盖主话题的**多样同义词、相关概念及常见Reddit讨论表达**，避免只给出抽象概念。
7. **定量评估置信度**: 在'confidence'字段中，请对你本次分析的准确性和可靠性给出一个0.0到1.0之间的浮点数分数。如果用户输入清晰、你的分析非常有把握，则分数应接近1.0；如果用户输入含糊不清或矛盾，导致你的分析存在不确定性，则应给出较低的分数。**请不要总是返回一个固定的值。**

**关键要求：search_keywords必须100%是英文，绝对不能包含中文字符！**

请只返回JSON，不要包含任何解释或markdown标记。"""
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """解析AI响应结果"""
        try:
            # 提取JSON内容
            json_content = self.ai_service._extract_json_from_response(response)
            
            # 解析JSON
            result = json.loads(json_content)
            
            # 验证和标准化数据
            result = self._validate_and_normalize_result(result)
            
            logger.debug(f"AI响应解析成功: {result}")
            return result
            
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            # 返回默认结构
            return {
                "search_keywords": ["help", "advice"],
                "topics": ["生活困扰"],
                "emotional_state": {"confusion": 0.7, "anxiety": 0.5},
                "values_info": {"decision_style": "unknown"},
                "core_concerns": ["寻求建议"],
                "decision_points": ["人生选择"],
                "life_domains": ["个人成长"],
                "support_needs": ["情感支持"],
                "confidence": 0.3
            }
    
    def _validate_and_normalize_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """验证和标准化分析结果"""
        # 确保必需字段存在
        result.setdefault("search_keywords", [])
        result.setdefault("topics", [])
        result.setdefault("emotional_state", {})
        result.setdefault("values_info", {})
        result.setdefault("core_concerns", [])
        result.setdefault("decision_points", [])
        result.setdefault("life_domains", [])
        result.setdefault("support_needs", [])
        result.setdefault("confidence", 0.5)
        
        # 翻译中文关键词为英文
        result["search_keywords"] = self._ensure_english_keywords(result["search_keywords"])
        
        # 限制关键词数量
        if len(result["search_keywords"]) > self.max_keywords:
            result["search_keywords"] = result["search_keywords"][:self.max_keywords]
        
        # 确保情绪状态数值在0-1范围内
        for emotion, value in result["emotional_state"].items():
            if isinstance(value, (int, float)):
                result["emotional_state"][emotion] = max(0.0, min(1.0, float(value)))
        
        # 确保置信度在0-1范围内
        result["confidence"] = max(0.0, min(1.0, float(result["confidence"])))
        
        return result
    
    def _get_cache_key(self, query_text: str) -> str:
        """生成缓存键"""
        return hashlib.md5(query_text.encode("utf-8")).hexdigest()
    
    def _get_from_cache(self, cache_key: str) -> Optional[ParsedQuery]:
        """从缓存获取结果"""
        if cache_key in self._cache:
            result, timestamp = self._cache[cache_key]
            if datetime.now() - timestamp < self._cache_ttl:
                return result
            else:
                del self._cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: ParsedQuery) -> None:
        """缓存解析结果"""
        if len(self._cache) < self._max_cache_size:
            self._cache[cache_key] = (result, datetime.now())
    
    def _fallback_parse(self, query_text: str) -> ParsedQuery:
        """降级解析策略"""
        logger.warning("使用降级解析策略")
        
        keywords = self._extract_basic_keywords(query_text)
        emotional_state = self._extract_basic_emotions(query_text)
        topics = self._extract_basic_topics(query_text)
        
        return ParsedQuery(
            original_text=query_text,
            search_intent=keywords,
            values_info={"decision_style": "unknown"},
            emotional_state=emotional_state,
            topics=topics,
            confidence=0.3,
            core_concerns=["寻求建议"],
            decision_points=["人生选择"],
            life_domains=self._infer_life_domains(query_text),
            support_needs=["情感支持", "经验分享"]
        )
    
    def _extract_basic_keywords(self, text: str) -> List[str]:
        """基础关键词提取（降级策略）"""
        # 扩展的关键词字典，增加更多具体的映射
        keyword_mapping = {
            # 职业相关 - 更具体的映射
            "工作": ["work", "job", "career", "employment"],
            "职业": ["career", "profession", "occupation"],
            "就业": ["employment", "job", "career"],
            "辞职": ["quit job", "resignation", "leave job", "career change"],
            "跳槽": ["career change", "job change", "switch jobs"],
            "面试": ["interview", "job interview"],
            "求职": ["job search", "job hunting", "employment"],
            "职场": ["workplace", "office", "career"],
            "同事": ["colleague", "coworker", "workplace"],
            "老板": ["boss", "manager", "supervisor"],
            "薪资": ["salary", "pay", "income", "money"],
            "升职": ["promotion", "career advancement"],
            "成长": ["growth", "development", "progress"],
            
            # 创业相关 - 新增映射
            "创业": ["entrepreneurship", "startup", "business", "entrepreneur", "founder"],
            "失败": ["failure", "failed", "fail", "setback", "unsuccessful"],
            "坚持": ["persistence", "perseverance", "continue", "persist", "keep going"],
            "成绩": ["achievement", "success", "result", "accomplishment"],
            "反思": ["reflection", "self-reflection", "introspection", "rethink"],
            
            # 教育相关 - 增加具体的研究生相关词汇
            "学习": ["study", "learning", "education"],
            "读研": ["graduate school", "masters degree", "graduate studies", "postgraduate"],
            "考研": ["graduate exam", "masters application", "graduate school"],
            "研究生": ["graduate student", "masters", "postgraduate"],
            "学校": ["school", "education", "university"],
            "专业": ["major", "specialization", "field of study"],
            "学历": ["education", "degree", "qualification"],
            "深造": ["further study", "advanced education"],
            
            # 决策相关 - 针对纠结和选择的词汇
            "纠结": ["struggling", "torn between", "dilemma", "indecision"],
            "选择": ["choice", "decision", "options", "alternatives"],
            "决定": ["decision", "choice", "determine"],
            "犹豫": ["hesitate", "uncertain", "undecided"],
            "两难": ["dilemma", "difficult choice"],
            
            # 情绪相关
            "困惑": ["confusion", "confused", "uncertainty"],
            "迷茫": ["lost", "direction", "confused", "uncertain"],
            "焦虑": ["anxiety", "worried", "stress", "nervous"],
            "不安": ["anxiety", "unease", "worry", "concern"],
            "压力": ["stress", "pressure", "burden"],
            "未来": ["future", "planning", "career path"],
            
            # 关系相关
            "恋爱": ["relationship", "dating", "romance"],
            "感情": ["relationship", "romance", "love"],
            "分手": ["breakup", "relationship end"],
            "结婚": ["marriage", "wedding"],
            
            # 生活相关
            "生活": ["life", "lifestyle", "living"],
            "年龄": ["age", "life stage"],
            "经济": ["financial", "money", "economic"],
            "家庭": ["family", "home", "parents"]
        }
        
        found_keywords = []
        text_lower = text.lower()
        
        # 优先匹配更具体的关键词
        for chinese_word, english_keywords in keyword_mapping.items():
            if chinese_word in text:
                found_keywords.extend(english_keywords)
                # 记录匹配到的中文词，用于调试
                logger.debug(f"匹配到关键词: {chinese_word} -> {english_keywords}")
        
        # 特殊模式匹配
        patterns = {
            r'读研|研究生|考研': ["graduate school", "masters", "postgraduate", "advanced degree"],
            r'辞职|离职|换工作': ["quit job", "career change", "job transition", "resignation"], 
            r'职业发展|事业': ["career development", "professional growth", "career path"],
            r'人生选择|人生规划': ["life decision", "life planning", "major choice"],
            r'工作经验|经验': ["work experience", "professional experience"],
            r'创业.*失败': ["startup failure", "entrepreneurship fail", "business failure"],
            r'坚持.*创业': ["entrepreneurship persistence", "startup perseverance"],
            r'创业.*年': ["entrepreneurship journey", "startup experience", "business years"]
        }
        
        import re
        for pattern, keywords in patterns.items():
            if re.search(pattern, text):
                found_keywords.extend(keywords)
                logger.debug(f"模式匹配: {pattern} -> {keywords}")
        
        # 去重并保持顺序
        unique_keywords = []
        seen = set()
        for keyword in found_keywords:
            if keyword not in seen:
                unique_keywords.append(keyword)
                seen.add(keyword)
        
        # 如果没有找到或者找到的太少，添加通用但相关的关键词
        if len(unique_keywords) < 2:
            # 基于文本内容添加合适的通用关键词
            if any(word in text for word in ["工作", "职业", "辞职", "读研", "事业"]):
                unique_keywords.extend(["career advice", "life decision", "professional choice"])
            elif any(word in text for word in ["感情", "恋爱", "关系"]):
                unique_keywords.extend(["relationship advice", "emotional support"])
            else:
                unique_keywords.extend(["life advice", "personal guidance", "decision making"])
        
        # 限制数量并记录最终结果
        result = unique_keywords[:self.max_keywords]
        logger.info(f"关键词提取结果: {result}")
        
        return result
    
    def _extract_basic_emotions(self, text: str) -> Dict[str, float]:
        """基础情绪提取（降级策略）"""
        emotion_keywords = {
            "anxiety": ["焦虑", "担心", "不安", "紧张", "恐慌"],
            "confusion": ["困惑", "迷茫", "不知道", "纠结", "犹豫"],
            "sadness": ["难过", "伤心", "沮丧", "失落", "悲伤"],
            "frustration": ["挫折", "烦躁", "愤怒", "不满", "郁闷"],
            "fear": ["害怕", "恐惧", "担忧", "忧虑", "不安"],
            "hope": ["希望", "期待", "乐观", "向往", "憧憬"]
        }
        
        emotional_state = {}
        for emotion, keywords in emotion_keywords.items():
            score = 0.0
            for keyword in keywords:
                if keyword in text:
                    score += 0.2  # 每个关键词加0.2
            emotional_state[emotion] = min(1.0, score)  # 最大值为1.0
        
        # 如果没有检测到任何情绪，设置默认值
        if not any(emotional_state.values()):
            emotional_state = {"confusion": 0.7, "anxiety": 0.5}
        
        return emotional_state
    
    def _extract_basic_topics(self, text: str) -> List[str]:
        """基础主题提取（降级策略）"""
        topic_keywords = {
            "职业发展": ["工作", "职业", "就业", "辞职", "跳槽", "面试", "求职", "事业"],
            "教育学习": ["学习", "读研", "考研", "学校", "专业", "考试", "毕业"],
            "人际关系": ["恋爱", "感情", "朋友", "家人", "关系", "社交", "人际"],
            "生活规划": ["生活", "选择", "决定", "未来", "规划", "目标", "方向"],
            "心理健康": ["焦虑", "压力", "困惑", "迷茫", "情绪", "心理", "健康"],
            "个人成长": ["成长", "提升", "学习", "进步", "发展", "能力", "技能"]
        }
        
        found_topics = []
        for topic, keywords in topic_keywords.items():
            if any(keyword in text for keyword in keywords):
                found_topics.append(topic)
        
        # 如果没有找到特定主题，使用通用主题
        if not found_topics:
            found_topics = ["生活困扰"]
        
        return found_topics
    
    def _infer_life_domains(self, text: str) -> List[str]:
        """推断涉及的生活领域"""
        domain_keywords = {
            "职场": ["工作", "职业", "公司", "老板", "同事", "薪资"],
            "学业": ["学习", "学校", "专业", "考试", "研究"],
            "情感": ["恋爱", "感情", "关系", "伴侣", "结婚"],
            "家庭": ["家人", "父母", "家庭", "亲情"],
            "健康": ["身体", "健康", "医院", "疾病"],
            "财务": ["钱", "理财", "投资", "经济", "收入"],
            "社交": ["朋友", "社交", "人际", "交流"]
        }
        
        domains = []
        for domain, keywords in domain_keywords.items():
            if any(keyword in text for keyword in keywords):
                domains.append(domain)
        
        if not domains:
            domains = ["个人成长"]
        
        return domains
    
    def _ensure_english_keywords(self, keywords: List[str]) -> List[str]:
        """确保关键词都是英文，如果有中文则进行翻译"""
        if not keywords:
            return keywords
        
        # 中文到英文的直接映射表（常见的搜索关键词）
        chinese_to_english = {
            # 职业相关
            "辞职": "quit job",
            "离职": "resign",
            "读研": "graduate school",
            "研究生": "graduate student", 
            "考研": "graduate exam",
            "工作": "work",
            "职业": "career",
            "面试": "job interview",
            "求职": "job search",
            "跳槽": "career change",
            "升职": "promotion",
            "职场": "workplace",
            
            # 情感关系
            "恋爱": "dating",
            "感情": "relationship",
            "分手": "breakup",
            "结婚": "marriage",
            "单身": "single",
            "催婚": "marriage pressure",
            
            # 情绪状态
            "焦虑": "anxiety",
            "压力": "stress",
            "困惑": "confusion",
            "迷茫": "lost",
            "纠结": "struggling",
            "犹豫": "hesitation",
            
            # 生活相关
            "选择": "choice",
            "决定": "decision",
            "未来": "future",
            "生活": "life",
            "家庭": "family",
            "学习": "study",
            "成长": "growth"
        }
        
        english_keywords = []
        
        for keyword in keywords:
            # 检查是否包含中文字符
            if any('\u4e00' <= char <= '\u9fff' for char in keyword):
                # 包含中文，尝试翻译
                if keyword in chinese_to_english:
                    english_keywords.append(chinese_to_english[keyword])
                    logger.debug(f"翻译关键词: {keyword} -> {chinese_to_english[keyword]}")
                else:
                    # 如果没有直接映射，尝试拆分翻译
                    translated_parts = []
                    for word in chinese_to_english:
                        if word in keyword:
                            translated_parts.append(chinese_to_english[word])
                    
                    if translated_parts:
                        # 使用第一个匹配的翻译
                        english_keywords.append(translated_parts[0])
                        logger.debug(f"部分匹配翻译: {keyword} -> {translated_parts[0]}")
                    else:
                        # 无法翻译，使用后备词
                        fallback_keywords = self._get_fallback_english_keywords(keyword)
                        english_keywords.extend(fallback_keywords)
                        logger.warning(f"无法翻译关键词 '{keyword}'，使用后备词: {fallback_keywords}")
            else:
                # 已经是英文，直接使用
                english_keywords.append(keyword)
        
        # 去重
        unique_keywords = list(dict.fromkeys(english_keywords))
        logger.info(f"关键词英文化结果: {unique_keywords}")
        
        return unique_keywords
    
    def _get_fallback_english_keywords(self, chinese_keyword: str) -> List[str]:
        """为无法翻译的中文关键词提供后备英文关键词"""
        # 基于常见主题提供后备关键词
        topic_fallbacks = {
            "工作": ["work", "job"],
            "感情": ["relationship", "love"],
            "学习": ["study", "education"],
            "生活": ["life", "lifestyle"],
            "家庭": ["family"],
            "朋友": ["friends", "social"],
            "健康": ["health", "wellness"],
            "钱": ["money", "financial"]
        }
        
        # 检查是否包含某些主题词
        for topic, fallbacks in topic_fallbacks.items():
            if topic in chinese_keyword:
                return fallbacks
        
        # 默认后备关键词
        return ["advice", "help"]
    
    async def extract_search_intentions(self, parsed_query: ParsedQuery) -> List[str]:
        """从解析结果中提取更精确的搜索意图"""
        try:
            # 基于解析结果构建更精确的搜索关键词
            search_intentions = []
            
            # 基础搜索意图
            search_intentions.extend(parsed_query.search_intent)
            
            # 基于情绪状态添加关键词
            for emotion, score in parsed_query.emotional_state.items():
                if score > 0.6:  # 高情绪强度
                    if emotion == "anxiety":
                        search_intentions.extend(["stress", "anxiety", "worry"])
                    elif emotion == "confusion":
                        search_intentions.extend(["confused", "lost", "direction"])
                    elif emotion == "sadness":
                        search_intentions.extend(["depression", "sad", "down"])
            
            # 基于生活领域添加关键词
            domain_keywords = {
                "职场": ["workplace", "career", "job"],
                "学业": ["study", "school", "education"],
                "情感": ["relationship", "dating", "love"],
                "家庭": ["family", "parents", "home"],
                "健康": ["health", "medical", "wellness"]
            }
            
            for domain in parsed_query.life_domains:
                if domain in domain_keywords:
                    search_intentions.extend(domain_keywords[domain])
            
            # 去重并返回
            unique_intentions = list(dict.fromkeys(search_intentions))
            return unique_intentions[:self.max_keywords]
            
        except Exception as e:
            logger.error(f"提取搜索意图失败: {e}")
            return parsed_query.search_intent
    
    async def close(self) -> None:
        """关闭语义解析器"""
        if self.ai_service:
            await self.ai_service.close()
        logger.info("语义解析器已关闭")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "cache_size": len(self._cache),
            "max_cache_size": self._max_cache_size,
            "cache_ttl_hours": self._cache_ttl.total_seconds() / 3600,
            "ai_analysis_enabled": self.ai_analysis_enabled
        }
    
    def enable_ai_analysis(self, enabled: bool = True) -> None:
        """启用或禁用AI分析"""
        self.ai_analysis_enabled = enabled
        logger.info(f"AI分析{'启用' if enabled else '禁用'}")
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._cache.clear()
        logger.info("语义解析器缓存已清空")

    def _optimize_keywords(self, keywords: List[str]) -> List[str]:
        """根据常见同义词和相关概念扩展搜索关键词，提高Reddit搜索覆盖面"""
        if not keywords:
            return keywords

        # 同义词和相关概念映射
        synonym_map = {
            "entrepreneurship": ["startup", "startups", "entrepreneur", "small business", "founder", "self employment"],
            "startup": ["entrepreneurship", "founder", "new business", "venture"],
            "persistence": ["perseverance", "determination", "grit", "motivation", "discipline"],
            "failure": ["setback", "mistake", "learning from failure", "business failure", "entrepreneur failure"],
            "career change": ["switch jobs", "job transition", "quit job", "resignation"],
            "graduate school": ["masters", "postgraduate", "grad school", "advanced degree"],
            "anxiety": ["stress", "worry", "unease"],
            "confusion": ["uncertainty", "indecision", "dilemma"]
        }

        optimized: List[str] = []
        seen = set()

        def add_kw(kw: str):
            kw_lower = kw.lower()
            if kw_lower not in seen:
                optimized.append(kw_lower)
                seen.add(kw_lower)

        # 先添加原始关键词
        for kw in keywords:
            add_kw(kw)

        # 扩展同义词
        for kw in list(optimized):  # 使用拷贝避免动态扩展影响迭代
            if kw in synonym_map:
                for syn in synonym_map[kw]:
                    add_kw(syn)

        # 截断到最大数量
        return optimized[: self.max_keywords]

    def _infer_expression_style(self, emotional_state: Dict[str, float], values_info: Dict[str, str]) -> str:
        """基于情绪状态和价值观信息推断表达风格"""
        # 如果 values_info 中已有 decision_style，直接使用
        decision_style = values_info.get("decision_style", "unknown")
        if decision_style in ["analytical", "emotional", "avoidant", "seeking"]:
            return decision_style
        
        # 否则基于情绪状态推断
        if emotional_state.get("anxiety", 0) > 0.6 or emotional_state.get("fear", 0) > 0.6:
            return "avoidant"
        elif emotional_state.get("confusion", 0) > 0.6:
            return "seeking"
        elif emotional_state.get("frustration", 0) > 0.5 or emotional_state.get("sadness", 0) > 0.5:
            return "emotional"
        else:
            return "analytical"
