"""
匹配服务 - 整合所有组件实现用户匹配
"""
import logging
from typing import List, Tuple
from datetime import datetime
import asyncio
import sys
import os
import re

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .reddit_service import RedditService
from .ai_service import AIService
from .vector_service import VectorService
from models import (
    UserQuery, QueryProfile, RedditUser, RedditPost,
    MatchedUser, MatchingResult
)
from resona.config import settings

logger = logging.getLogger(__name__)

class MatchingService:
    """匹配服务类 - 协调整个匹配流程"""
    
    def __init__(self):
        """初始化匹配服务"""
        self.reddit_service = RedditService()
        self.ai_service = AIService()
        self.vector_service = VectorService()
        
    async def find_resonant_users(self, query: UserQuery) -> MatchingResult:
        """
        新的匹配流程 - 基于帖子搜索和评论质量的方法
        
        Args:
            query: 用户查询
            
        Returns:
            匹配结果
        """
        try:
            logger.info(f"==================== 开始新匹配流程 ====================")
            logger.info(f"用户输入：{query.text[:100]}...")
            
            # 1. 简化的查询分析（只为了返回结果格式兼容）
            logger.info(f"步骤1：生成查询画像...")
            query_profile = await self.ai_service.analyze_query(query.text)
            logger.info(f"查询画像生成完成")
            
            # 2. 直接用用户输入搜索相关帖子
            logger.info(f"步骤2：直接搜索相关帖子...")
            logger.info(f"使用原始用户输入在Reddit上搜索相关帖子...")
            relevant_posts = await self.reddit_service.search_posts_by_content(
                query.text, 
                limit=50  # 搜索更多帖子
            )
            
            if not relevant_posts:
                logger.warning("未找到相关帖子，匹配结束")
                return MatchingResult(
                    query_profile=query_profile,
                    matched_users=[]
                )
            
            logger.info(f"找到 {len(relevant_posts)} 个相关帖子")
            
            # 3. 使用AI过滤高相关性帖子
            logger.info(f"步骤3：过滤高相关性帖子...")
            high_relevance_posts = await self.ai_service.filter_relevant_posts(
                query.text, 
                relevant_posts, 
                threshold=0.65  # 适度降低阈值以获得更多候选
            )
            
            if not high_relevance_posts:
                logger.warning("未找到高相关性帖子，使用原始帖子列表")
                high_relevance_posts = relevant_posts[:20]  # 降级方案
            
            logger.info(f"筛选出 {len(high_relevance_posts)} 个高相关性帖子")
            
            # 4. 从这些帖子中提取优质评论者
            logger.info(f"步骤4：提取优质评论者...")
            quality_commenters = await self.reddit_service.extract_quality_commenters(
                high_relevance_posts,
                min_score=3,      # 降低分数要求
                min_length=80,    # 降低长度要求
                max_commenters=30 # 增加候选人数
            )
            
            if not quality_commenters:
                logger.warning("未找到优质评论者，匹配结束")
                return MatchingResult(
                    query_profile=query_profile,
                    matched_users=[]
                )
            
            logger.info(f"找到 {len(quality_commenters)} 个优质评论者")
            
            # 5. 获取候选人历史数据
            logger.info(f"步骤5：获取候选人历史数据...")
            candidate_users = await self.reddit_service.batch_get_users(quality_commenters)
            
            if not candidate_users:
                logger.warning("无法获取候选人历史数据")
                return MatchingResult(
                    query_profile=query_profile,
                    matched_users=[]
                )
            
            logger.info(f"成功获取 {len(candidate_users)} 个候选人的历史数据")
            
            # 6. AI最终排序和选择
            logger.info(f"步骤6：AI最终排序和选择推荐用户...")
            matched_users = await self.ai_service.rank_and_select_users(
                query, 
                candidate_users, 
                top_k=settings.top_k_matches
            )
            
            logger.info(f"==================== 匹配流程完成 ====================")
            logger.info(f"最终推荐 {len(matched_users)} 个用户")
            
            return MatchingResult(
                query_profile=query_profile,
                matched_users=matched_users
            )
            
        except Exception as e:
            logger.error(f"匹配过程出错: {e}")
            raise
    
    async def find_resonant_users_legacy(self, query: UserQuery) -> MatchingResult:
        """
        原始匹配流程 - 保留作为备选方案
        （基于主题关键词搜索 + 向量匹配）
        """
        try:
            logger.info(f"使用传统匹配流程作为备选方案...")
            
            # 1. LLM分析查询
            query_profile = await self.ai_service.analyze_query(query.text)
            
            # 2. 关键词搜索用户
            search_terms = " ".join(query_profile.main_topics[:3])
            if re.search(r"[\u4e00-\u9fff]", search_terms):
                search_terms = await self.ai_service.translate_to_english(search_terms)
            
            candidate_usernames = await self.reddit_service.search_users_by_topic(
                search_terms, limit=settings.reddit_search_limit
            )
            
            if not candidate_usernames:
                return MatchingResult(query_profile=query_profile, matched_users=[])
            
            # 3. 获取用户历史
            reddit_users = await self.reddit_service.batch_get_users(candidate_usernames)
            
            if not reddit_users:
                return MatchingResult(query_profile=query_profile, matched_users=[])
            
            # 4. 向量匹配
            users_with_embeddings = []
            for user in reddit_users:
                if not user.embedding_vector:
                    user.embedding_vector = await self.ai_service.generate_user_embedding(user)
                users_with_embeddings.append((user, user.embedding_vector))
            
            # 5. 向量搜索
            self.vector_service.batch_add_users(users_with_embeddings)
            similar_users = self.vector_service.search(
                query_profile.embedding_vector, k=settings.top_k_matches
            )
            
            # 6. 生成结果
            matched_users = []
            for user, similarity_score in similar_users:
                if similarity_score >= settings.min_similarity_score:
                    matched_posts = self._find_relevant_posts(user, query_profile)
                    resonance_summary = await self.ai_service.generate_resonance_summary(
                        query_profile, user, matched_posts
                    )
                    resonance_tags = self._generate_resonance_tags(
                        query_profile, user, similarity_score
                    )
                    suggested_message = await self.ai_service.generate_private_message(
                        query_profile, user, resonance_summary
                    )
                    
                    matched_users.append(MatchedUser(
                        user=user,
                        similarity_score=similarity_score,
                        matched_posts=matched_posts,
                        resonance_summary=resonance_summary,
                        resonance_tags=resonance_tags,
                        suggested_message=suggested_message
                    ))
            
            return MatchingResult(query_profile=query_profile, matched_users=matched_users)
            
        except Exception as e:
            logger.error(f"传统匹配流程出错: {e}")
            raise
    
    def _find_relevant_posts(self, user: RedditUser, query_profile: QueryProfile) -> List[RedditPost]:
        """
        找到用户最相关的帖子
        
        Args:
            user: Reddit 用户
            query_profile: 查询画像
            
        Returns:
            相关帖子列表
        """
        relevant_posts = []
        
        # 简单的关键词匹配（可以改进为更智能的方法）
        keywords = set(word.lower() for topic in query_profile.main_topics 
                      for word in topic.split())
        
        all_posts = sorted(user.posts, key=lambda p: p.timestamp, reverse=True)
        
        for post in all_posts[:20]:  # 只看最近20个帖子
            post_text_lower = post.text.lower()
            relevance_score = sum(1 for keyword in keywords if keyword in post_text_lower)
            
            if relevance_score > 0:
                relevant_posts.append((post, relevance_score))
        
        # 按相关度排序，返回前3个
        relevant_posts.sort(key=lambda x: x[1], reverse=True)
        return [post for post, _ in relevant_posts[:3]]
    
    def _generate_resonance_tags(self, query_profile: QueryProfile, 
                                user: RedditUser, similarity_score: float) -> List[str]:
        """
        生成共鸣标签
        
        Args:
            query_profile: 查询画像
            user: 匹配的用户
            similarity_score: 相似度分数
            
        Returns:
            标签列表
        """
        tags = []
        
        # 情绪匹配
        common_emotions = set(query_profile.emotions) & set(user.emotion_profile)
        if common_emotions:
            tags.append(f"😩 情绪相近：{', '.join(e.value for e in common_emotions)}")
        
        # 话题匹配
        common_topics = set(query_profile.main_topics) & set(user.topics)
        if common_topics:
            tags.append(f"🎯 共同话题：{', '.join(common_topics[:2])}")
        
        # 表达风格
        tags.append(f"🧠 表达风格：{query_profile.expression_style.value}")
        
        # 相似度等级
        if similarity_score > 0.9:
            tags.append("⭐ 高度共鸣")
        elif similarity_score > 0.8:
            tags.append("💫 深度共鸣")
        else:
            tags.append("✨ 潜在共鸣")
        
        return tags
    
    async def index_trending_users(self, limit: int = 100):
        """
        索引热门用户（用于构建初始数据库）
        
        Args:
            limit: 索引的用户数量
        """
        try:
            logger.info(f"开始索引热门用户，目标数量: {limit}")
            
            # 获取热门用户
            usernames = await self.reddit_service.get_trending_users(limit=limit)
            
            # 分批处理
            batch_size = 10
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i+batch_size]
                
                # 获取用户数据
                users = await self.reddit_service.batch_get_users(batch)
                
                # 生成 embeddings
                users_with_embeddings = []
                for user in users:
                    embedding = await self.ai_service.generate_user_embedding(user)
                    user.embedding_vector = embedding
                    users_with_embeddings.append((user, embedding))
                
                # 添加到索引
                self.vector_service.batch_add_users(users_with_embeddings)
                
                logger.info(f"已索引 {i + len(users)} / {len(usernames)} 个用户")
            
            # 保存索引
            self.vector_service.save_index()
            logger.info("索引完成并保存")
            
        except Exception as e:
            logger.error(f"索引用户时出错: {e}")
            raise
    
    async def get_system_status(self) -> dict:
        """
        获取系统状态
        
        Returns:
            状态信息字典
        """
        reddit_connected = await self.reddit_service.test_connection()
        deepinfra_connected = await self.ai_service.test_connection()
        vector_stats = self.vector_service.get_stats()
        
        return {
            "reddit_connected": reddit_connected,
            "deepinfra_connected": deepinfra_connected,
            "faiss_index_loaded": vector_stats["total_users"] > 0,
            "indexed_users_count": vector_stats["total_users"],
            "last_index_update": datetime.utcnow()
        }
    
    async def close(self):
        """关闭所有服务连接"""
        await self.reddit_service.close()
        await self.ai_service.close() 