{"session_info": {"session_id": "detailed_results_20250630_011529_2b12b753", "start_time": "2025-06-30T01:15:29.706044", "end_time": null, "status": "running", "user_prompt": "子任务D修复测试", "total_execution_time": null, "version": "2.0"}, "subtasks": {"子任务D修复测试": {"task_name": "子任务D修复测试", "timestamp": "2025-06-30T01:15:29.708450", "execution_time_seconds": 170.713868, "status": "success", "data": {"test_users": ["test_user_1", "test_user_2", "test_user_3"], "execution_time": 170.713868, "successful_graphs": 1, "failed_graphs": 0, "adaptive_features_tested": ["batch_size_reduction", "timeout_control", "exponential_backoff", "force_flush_results"]}, "metrics": {"data_size": 254, "data_type": "dict", "timestamp": "2025-06-30T01:15:29.709503"}, "error_info": null}}, "performance_metrics": {"total_start_time": "2025-06-30T01:15:29.706044", "subtask_timings": {"子任务D修复测试": 170.713868}}, "error_logs": [], "debug_info": {}, "statistics": null, "final_results": null}