#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业话题搜索工具

由于直接搜索推文内容有限制，采用以下策略：
1. 抓取知名创业者的推文
2. 在现有数据中搜索相关内容
3. 提供手动搜索的建议
"""

import requests
import json
import re
from datetime import datetime

API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
PROFILES_DATASET_ID = "gd_lwxmeb2u1cniijd7t4"

def get_entrepreneurship_influencers():
    """获取创业相关的知名人士推文"""
    print("👥 抓取知名创业者的推文...")
    
    # 知名创业者和投资人
    entrepreneurs = [
        "sama",           # <PERSON> (OpenAI)
        "naval",          # Naval Ravikant
        "paulg",          # <PERSON> (Y Comb<PERSON>)
        "jess<PERSON><PERSON>y",    # <PERSON>
        "pmarca",         # <PERSON>
    ]
    
    data = []
    for username in entrepreneurs:
        data.append({
            "url": f"https://x.com/{username}",
            "max_number_of_posts": 50  # 每人50条推文
        })
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json",
    }
    
    params = {
        "dataset_id": PROFILES_DATASET_ID,
        "include_errors": "true",
    }
    
    try:
        response = requests.post("https://api.brightdata.com/datasets/v3/trigger",
                               headers=headers, params=params, json=data, timeout=30)
        result = response.json()
        
        if response.status_code == 200:
            snapshot_id = result.get("snapshot_id")
            print(f"✅ 创业者推文抓取已启动，快照ID: {snapshot_id}")
            print(f"💰 成本: {len(entrepreneurs)} records")
            return snapshot_id
        else:
            print(f"❌ 抓取失败: {result}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def search_existing_data_for_keywords():
    """在现有数据中搜索相关关键词"""
    print("\n🔍 在现有数据中搜索创业相关内容...")
    
    # 相关关键词
    keywords = [
        "entrepreneur", "startup", "business", "failure", "success",
        "7 years", "persever", "struggle", "give up", "keep going"
    ]
    
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    
    try:
        # 获取所有现有快照
        response = requests.get("https://api.brightdata.com/datasets/v3/snapshots",
                              headers=headers, 
                              params={"dataset_id": PROFILES_DATASET_ID, "status": "ready"},
                              timeout=30)
        
        if response.status_code == 200:
            snapshots = response.json()
            print(f"📊 检查 {len(snapshots)} 个快照...")
            
            all_relevant_posts = []
            
            # 检查最近几个快照
            for snapshot in snapshots[-5:]:  # 最新5个
                snapshot_id = snapshot.get('id')
                posts = download_and_search_snapshot(snapshot_id, keywords)
                if posts:
                    all_relevant_posts.extend(posts)
            
            # 保存搜索结果
            if all_relevant_posts:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"brightdata_apitest/data/entrepreneurship_search_results_{timestamp}.json"
                
                with open(filename, "w", encoding="utf-8") as f:
                    json.dump(all_relevant_posts, f, indent=2, ensure_ascii=False)
                
                print(f"💾 找到 {len(all_relevant_posts)} 条相关推文，已保存到: {filename}")
                
                # 显示前10条
                print("\n📝 相关推文示例:")
                for i, post in enumerate(all_relevant_posts[:10], 1):
                    print(f"{i}. @{post['author']}: {post['content'][:80]}...")
                
                return filename
            else:
                print("❌ 未找到相关内容")
                return None
        
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return None

def download_and_search_snapshot(snapshot_id, keywords):
    """下载快照并搜索关键词"""
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    
    try:
        response = requests.get(f"https://api.brightdata.com/datasets/v3/snapshot/{snapshot_id}",
                              headers=headers, params={"format": "json"}, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            relevant_posts = []
            
            if isinstance(data, list):
                for user_data in data:
                    if "posts" in user_data and user_data["posts"]:
                        user_name = user_data.get("profile_name", "Unknown")
                        
                        for post in user_data["posts"]:
                            content = post.get("description", "").lower()
                            
                            # 检查是否包含关键词
                            for keyword in keywords:
                                if keyword.lower() in content:
                                    relevant_posts.append({
                                        "author": user_name,
                                        "content": post.get("description", ""),
                                        "date": post.get("date_posted", ""),
                                        "post_id": post.get("id", ""),
                                        "matched_keyword": keyword,
                                        "snapshot_id": snapshot_id
                                    })
                                    break  # 找到一个匹配即可
            
            return relevant_posts
        
    except Exception as e:
        print(f"❌ 处理快照 {snapshot_id} 异常: {e}")
        return []
    
    return []

def provide_manual_search_guide():
    """提供手动搜索指南"""
    print("\n📖 手动搜索指南:")
    print("由于API限制，建议您手动在Twitter搜索以下内容：")
    print()
    
    search_queries = [
        '"I\'ve been persevering in entrepreneurship for 7 years"',
        '"entrepreneurship 7 years" "haven\'t achieved success"',
        '"persevering entrepreneurship" failure',
        '"startup founder" "7 years" struggle',
        '"entrepreneur" "haven\'t achieved much"'
    ]
    
    for i, query in enumerate(search_queries, 1):
        encoded_query = query.replace('"', '%22').replace(' ', '+')
        twitter_url = f"https://x.com/search?q={encoded_query}&src=typed_query"
        print(f"{i}. 搜索: {query}")
        print(f"   URL: {twitter_url}")
        print()
    
    print("💡 操作步骤:")
    print("1. 复制上述URL到浏览器")
    print("2. 手动复制前10条推文的URL")
    print("3. 使用Posts Scraper抓取这些具体推文")

def main():
    print("🚀 创业话题搜索工具")
    print("目标: 找到关于'创业7年但没成功'的相关讨论")
    print("=" * 60)
    
    # 方案1：搜索现有数据
    existing_results = search_existing_data_for_keywords()
    
    print("\n" + "="*60)
    
    # 方案2：抓取创业者推文
    print("🎯 建议方案:")
    print("1. 抓取知名创业者的最新推文（可能包含相关讨论）")
    
    choice = input("是否启动创业者推文抓取？(y/n): ").strip().lower()
    if choice == 'y':
        snapshot_id = get_entrepreneurship_influencers()
        if snapshot_id:
            print("⏳ 请等待几分钟后检查结果")
    
    print("\n" + "="*60)
    
    # 方案3：手动搜索指南
    provide_manual_search_guide()
    
    print("\n🏆 总结:")
    print("• 已搜索现有数据中的相关内容")
    print("• 可选择抓取知名创业者推文")
    print("• 提供了手动搜索的具体方案")
    print("• 所有结果都会保存为JSON文件")

if __name__ == "__main__":
    main() 