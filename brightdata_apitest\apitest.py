import requests
import time
import json

class BrightDataAPI:
    def __init__(self, api_token, dataset_id):
        self.api_token = api_token
        self.dataset_id = dataset_id
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }
    
    def trigger_data_collection(self, user_urls_and_posts):
        """
        触发数据收集任务
        
        Args:
            user_urls_and_posts: 列表，包含用户URL和帖子数量
            例如: [{"url":"https://x.com/elonmusk","max_number_of_posts":100}]
        
        Returns:
            响应结果，包含快照ID
        """
        url = "https://api.brightdata.com/datasets/v3/trigger"
        params = {
            "dataset_id": self.dataset_id,
            "include_errors": "true",
        }
        
        response = requests.post(url, headers=self.headers, params=params, json=user_urls_and_posts)
        return response.json()
    
    def check_progress(self, snapshot_id):
        """
        检查数据收集进度
        
        Args:
            snapshot_id: 从trigger_data_collection返回的快照ID
        
        Returns:
            进度信息
        """
        url = f"https://api.brightdata.com/datasets/v3/progress/{snapshot_id}"
        headers = {
            "Authorization": f"Bearer {self.api_token}",
        }
        
        response = requests.get(url, headers=headers)
        return response.json()
    
    def get_ready_snapshots(self):
        """
        获取已完成的数据快照
        
        Returns:
            已完成的快照列表
        """
        url = "https://api.brightdata.com/datasets/v3/snapshots"
        headers = {
            "Authorization": f"Bearer {self.api_token}",
        }
        params = {
            "dataset_id": self.dataset_id,
            "status": "ready",
        }
        
        response = requests.get(url, headers=headers, params=params)
        return response.json()
    
    def get_user_posts(self, username, max_posts=100, wait_for_completion=True):
        """
        获取指定用户的帖子（完整流程）
        
        Args:
            username: 用户名（不包含@符号）
            max_posts: 最大帖子数量
            wait_for_completion: 是否等待任务完成
        
        Returns:
            用户帖子数据
        """
        # 构建用户URL
        user_url = f"https://x.com/{username}"
        data = [{"url": user_url, "max_number_of_posts": max_posts}]
        
        print(f"开始收集用户 {username} 的 {max_posts} 条帖子...")
        
        # 1. 触发数据收集
        trigger_result = self.trigger_data_collection(data)
        print(f"触发结果: {trigger_result}")
        
        if "snapshot_id" not in trigger_result:
            print("触发失败，无法获取快照ID")
            return None
        
        snapshot_id = trigger_result["snapshot_id"]
        print(f"快照ID: {snapshot_id}")
        
        if not wait_for_completion:
            return {"snapshot_id": snapshot_id, "status": "triggered"}
        
        # 2. 等待任务完成
        print("等待数据收集完成...")
        while True:
            progress = self.check_progress(snapshot_id)
            print(f"进度: {progress}")
            
            if progress.get("status") == "ready":
                print("数据收集完成！")
                break
            elif progress.get("status") == "failed":
                print("数据收集失败！")
                return None
            
            time.sleep(10)  # 等待10秒后再次检查
        
        # 3. 获取结果
        snapshots = self.get_ready_snapshots()
        
        # 查找我们的快照
        for snapshot in snapshots.get("snapshots", []):
            if snapshot.get("snapshot_id") == snapshot_id:
                return snapshot
        
        print("未找到对应的快照数据")
        return None

def main():
    # API配置
    API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
    DATASET_ID = "gd_lwxmeb2u1cniijd7t4"
    
    # 创建API实例
    api = BrightDataAPI(API_TOKEN, DATASET_ID)
    
    # 示例：获取指定用户的帖子
    username = "elonmusk"  # 可以修改为任何用户名
    max_posts = 50  # 可以修改帖子数量
    
    result = api.get_user_posts(username, max_posts, wait_for_completion=False)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 也可以批量获取多个用户
    print("\n批量获取多个用户示例:")
    users_data = [
        {"url":"https://x.com/elonmusk","max_number_of_posts":30},
        {"url":"https://x.com/BillGates","max_number_of_posts":20},
    ]
    batch_result = api.trigger_data_collection(users_data)
    print(f"批量结果: {json.dumps(batch_result, indent=2, ensure_ascii=False)}")

if __name__ == "__main__":
    main()

# 原始测试代码（注释保留）
# url = "https://api.brightdata.com/datasets/v3/trigger"
# headers = {
# 	"Authorization": "Bearer 1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71",
# 	"Content-Type": "application/json",
# }
# params = {
# 	"dataset_id": "gd_lwxmeb2u1cniijd7t4",
# 	"include_errors": "true",
# }
# data = [
# 	{"url":"https://x.com/elonmusk","max_number_of_posts":100},
# 	{"url":"https://x.com/cnn","max_number_of_posts":20},
# 	{"url":"https://x.com/BillGates","max_number_of_posts":35},
# 	{"url":"https://x.com/fabrizioromano","max_number_of_posts":10},
# ]

# response = requests.post(url, headers=headers, params=params, json=data)
# print(response.json())



# url = "https://api.brightdata.com/datasets/v3/progress/s_mcm5e7bbjcc5sblbu"
# headers = {
# 	"Authorization": "Bearer 1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71",
# }

# response = requests.get(url, headers=headers)
# print(response.json())





# url = "https://api.brightdata.com/datasets/v3/snapshots"
# headers = {
# 	"Authorization": "Bearer 1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71",
# }
# params = {
# 	"dataset_id": "gd_lwxmeb2u1cniijd7t4",
# 	"status": "ready",
# }

# response = requests.get(url, headers=headers, params=params)
# print(response.json())