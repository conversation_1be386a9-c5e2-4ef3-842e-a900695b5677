"""
Reddit 共鸣用户搜索推荐系统 - 主业务流水线
按照业务架构图实现9个子任务的完整流程
增加缓存支持和并行处理优化
"""
import logging
import asyncio
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timedelta
import os

from .services.ai_service import AIService
from .services.reddit_service import RedditService
from .services.twitter_service import TwitterService
from .core.semantic_analyzer import SemanticAnalyzer
from .core.graph_builder import GraphBuilder
from .core.user_profiler import UserProfiler
from .core.resonance_matcher import ResonanceMatcher
from .models.user_models import ParsedQuery
from .models.graph_models import UserGraph, NodeType, RelationType
from .utils.cache_manager import PipelineCacheManager
from .utils.detailed_results_manager import DetailedResultsManager
from .config import settings

logger = logging.getLogger(__name__)

class RedditResonancePipeline:
    """
    Reddit 共鸣用户搜索推荐系统主流水线
    
    实现业务架构图中的9个子任务：
    A. Reddit 搜索与候选人提取
    B. 提取评论者列表  
    C. Redditor 评论语料提取
    D. 图结构构建
    E. 用户三观图谱构建
    F. 图谱缺失识别与追问生成
    G. 匹配评估
    H. LLM 分析匹配度
    I. 用户推荐展示
    
    新功能：
    - 子任务ABC结果缓存
    - 直接跳转到子任务D
    - 优化并行处理
    """
    
    def __init__(self, platform: str = "reddit"):
        """
        初始化流水线组件
        
        Args:
            platform: 社交媒体平台 ("reddit" 或 "twitter")
        """
        # 创建基础服务
        self.ai_service = AIService()
        
        # 根据平台选择社交媒体服务
        self.platform = platform.lower()
        if self.platform == "twitter":
            self.social_service = TwitterService()
            logger.info("使用Twitter平台服务")
        else:
            self.social_service = RedditService()
            logger.info("使用Reddit平台服务")
        
        # 保持向后兼容
        self.reddit_service = self.social_service
        
        # 创建核心组件，共享依赖避免重复初始化
        self.semantic_analyzer = SemanticAnalyzer(self.ai_service)
        self.graph_builder = GraphBuilder(self.ai_service)
        
        # 传入已创建的graph_builder，避免UserProfiler重复创建
        self.user_profiler = UserProfiler(
            semantic_analyzer=self.semantic_analyzer,
            graph_builder=self.graph_builder,
            ai_service=self.ai_service
        )
        
        # 传入已创建的user_profiler，避免ResonanceMatcher重复创建
        self.resonance_matcher = ResonanceMatcher(
            user_profiler=self.user_profiler,
            ai_service=self.ai_service
        )
        
        self.cache_manager = PipelineCacheManager()
        self.results_manager = DetailedResultsManager()
        
        logger.info(f"{self.platform.title()}共鸣推荐流水线初始化完成（支持缓存和并行优化）")
    
    async def execute_full_pipeline(self, user_prompt: str, 
                                   additional_contents: List[str] = None,
                                   max_recommendations: int = 5,
                                   use_cache: bool = True,
                                   cache_key: Optional[str] = None) -> Dict[str, Any]:
        """
        执行完整的推荐流水线
        
        Args:
            user_prompt: 用户输入的自然语言描述
            additional_contents: 用户额外提供的内容
            max_recommendations: 最大推荐数量
            use_cache: 是否使用缓存
            cache_key: 指定使用的缓存键（用于直接跳转）
            
        Returns:
            完整的推荐结果
        """
        logger.info(f"开始执行{self.platform.title()}共鸣推荐流水线")
        logger.info(f"用户输入: {user_prompt[:100]}...")
        
        start_time = datetime.now()
        pipeline_results = {}
        
        session_id = None
        
        # 检查是否使用缓存或直接跳转
        if use_cache and cache_key:
            logger.info(f"🚀 直接跳转模式：使用缓存键 {cache_key}")
            cached_data = await self.cache_manager.load_abc_results(cache_key)
            if cached_data:
                logger.info("✅ 成功加载缓存的ABC结果，直接跳转到子任务D")
                pipeline_results['parsed_query'] = cached_data['parsed_query']
                pipeline_results['relevant_posts'] = cached_data['relevant_posts']
                pipeline_results['candidate_users'] = cached_data['candidate_users']
                
                # 获取并继续会话
                metadata = self.cache_manager.get_cache_metadata(cache_key)
                if metadata and 'session_id' in metadata:
                    session_id = metadata['session_id']
                    self.results_manager.continue_session(session_id)
                else:
                    session_id = self.results_manager.start_session(user_prompt="[从缓存继续]")
                    
                return await self._execute_from_task_d(pipeline_results, user_prompt, additional_contents, max_recommendations, start_time)
            else:
                logger.warning("⚠️  缓存加载失败，执行完整流程")
        
        # 如果不是跳转模式，则启动新会话
        if not session_id:
            session_id = self.results_manager.start_session(user_prompt=user_prompt)
        
        # 添加调试模式开关
        debug_mode = getattr(settings, 'debug_step_by_step', True)  # 默认开启步进模式
        
        def wait_for_confirmation(task_name: str):
            """等待用户确认继续"""
            if debug_mode:
                print(f"\n📋 准备执行: {task_name}")
                response = input("是否继续？输入 Y 确认，任何其他键跳过: ").strip().upper()
                if response != 'Y':
                    logger.info(f"用户选择跳过任务: {task_name}")
                    return False
                else:
                    logger.info(f"用户确认执行任务: {task_name}")
            return True
        
        try:
            # 子任务A: 用户输入层 - 语义解析
            if not wait_for_confirmation("子任务A: 用户输入语义解析"):
                total_time = (datetime.now() - start_time).total_seconds()
                self.results_manager.complete_session({"error": "用户跳过任务A", "status": "skipped"})
                return {
                    "success": False, 
                    "error": "用户跳过任务A",
                    "session_id": session_id,
                    "processing_time": total_time
                }
            logger.info("="*50)
            logger.info("子任务A: 用户输入语义解析")
            
            task_a_start = datetime.now()
            try:
                parsed_query = await self._task_a_parse_user_input(user_prompt)
                pipeline_results['parsed_query'] = parsed_query
                
                # 保存子任务A详细结果
                task_a_time = (datetime.now() - task_a_start).total_seconds()
                self.results_manager.save_subtask_result(
                    task_name="子任务A: 用户输入语义解析",
                    task_data={
                        "parsed_query": parsed_query.__dict__ if hasattr(parsed_query, '__dict__') else str(parsed_query),
                        "search_keywords": parsed_query.get_search_keywords() if hasattr(parsed_query, 'get_search_keywords') else [],
                        "topics": getattr(parsed_query, 'topics', []),
                        "emotional_state": getattr(parsed_query, 'emotional_state', {}),
                        "confidence": getattr(parsed_query, 'confidence', 0.0)
                    },
                    execution_time=task_a_time
                )
            except Exception as e:
                task_a_time = (datetime.now() - task_a_start).total_seconds()
                error_info = {"error_type": type(e).__name__, "error_message": str(e)}
                self.results_manager.save_subtask_result(
                    task_name="子任务A: 用户输入语义解析",
                    task_data={"error": "任务执行失败"},
                    execution_time=task_a_time,
                    error_info=error_info
                )
                raise
            
            # 子任务B: 搜索与候选人提取  
            task_b_name = f"子任务B: {self.platform.title()}搜索与候选人提取"
            if not wait_for_confirmation(task_b_name):
                total_time = (datetime.now() - start_time).total_seconds()
                self.results_manager.complete_session({"error": "用户跳过任务B", "status": "skipped"})
                return {
                    "success": False, 
                    "error": "用户跳过任务B",
                    "session_id": session_id,
                    "processing_time": total_time
                }
            logger.info("="*50)
            logger.info(task_b_name)
            
            task_b_start = datetime.now()
            try:
                search_keywords = parsed_query.get_search_keywords()
                relevant_posts = await self._task_b_social_search(search_keywords, user_prompt)
                pipeline_results['relevant_posts'] = relevant_posts
                
                # 保存子任务B详细结果
                task_b_time = (datetime.now() - task_b_start).total_seconds()
                self.results_manager.save_subtask_result(
                    task_name=task_b_name,
                    task_data={
                        "search_keywords": search_keywords,
                        "relevant_posts": relevant_posts,
                        "posts_count": len(relevant_posts),
                        "average_score": sum(p.get('score', 0) for p in relevant_posts) / len(relevant_posts) if relevant_posts else 0
                    },
                    execution_time=task_b_time
                )
            except Exception as e:
                task_b_time = (datetime.now() - task_b_start).total_seconds()
                error_info = {"error_type": type(e).__name__, "error_message": str(e)}
                self.results_manager.save_subtask_result(
                    task_name=task_b_name,
                    task_data={"error": "任务执行失败"},
                    execution_time=task_b_time,
                    error_info=error_info
                )
                raise
            
            # 子任务C: 提取评论者列表
            if not wait_for_confirmation("子任务C: 提取评论者列表"):
                total_time = (datetime.now() - start_time).total_seconds()
                self.results_manager.complete_session({"error": "用户跳过任务C", "status": "skipped"})
                return {
                    "success": False, 
                    "error": "用户跳过任务C",
                    "session_id": session_id,
                    "processing_time": total_time
                }
            logger.info("="*50)
            logger.info("子任务C: 提取评论者列表")
            
            task_c_start = datetime.now()
            try:
                candidate_users = await self._task_c_extract_commenters(relevant_posts)
                pipeline_results['candidate_users'] = candidate_users
                
                # 保存子任务C详细结果（包含详细的提取信息）
                task_c_time = (datetime.now() - task_c_start).total_seconds()
                
                # 构建详细的任务数据
                task_c_data = {
                    "candidate_users": candidate_users,
                    "users_count": len(candidate_users),
                    "source_posts": len(relevant_posts)
                }
                
                # 如果有详细提取结果，添加到任务数据中
                if hasattr(self, 'detailed_extraction_result') and self.detailed_extraction_result:
                    detailed_result = self.detailed_extraction_result
                    
                    # 添加提取统计信息
                    task_c_data.update({
                        "extraction_stats": detailed_result.get("extraction_stats", {}),
                        "selection_criteria": detailed_result.get("selection_criteria", {}),
                        "top_reasons_summary": detailed_result.get("top_reasons_summary", {}),
                        
                        # 详细的评论者信息
                        "selected_commenters_detailed": detailed_result.get("selected_commenters", []),
                        "all_commenters_summary": {
                            "total_unique_commenters": len(detailed_result.get("all_commenters", {})),
                            "commenters_with_multiple_posts": len([
                                u for u in detailed_result.get("all_commenters", {}).values()
                                if u.get("comment_count", 0) > 1
                            ])
                        },
                        
                        # 前10个候选者的选择理由
                        "top_selection_reasons": [
                            {
                                "username": c.get("username"),
                                "rank": c.get("rank"),
                                "selection_reasons": c.get("selection_reasons", []),
                                "quality_summary": {
                                    "comment_count": c.get("comment_count", 0),
                                    "avg_score": round(c.get("avg_score", 0), 2),
                                    "subreddit_diversity": c.get("subreddit_diversity", 0),
                                    "selection_confidence": round(c.get("selection_confidence", 0), 3)
                                }
                            }
                            for c in detailed_result.get("selected_commenters", [])[:10]
                        ],
                        
                        # 评论内容样本（每个用户取1-2条最好的评论）
                        "comment_samples": self._extract_comment_samples(detailed_result)
                    })
                    
                    # 清理详细结果以避免内存占用
                    self.detailed_extraction_result = None
                
                self.results_manager.save_subtask_result(
                    task_name="子任务C: 提取评论者列表",
                    task_data=task_c_data,
                    execution_time=task_c_time
                )
            except Exception as e:
                task_c_time = (datetime.now() - task_c_start).total_seconds()
                error_info = {"error_type": type(e).__name__, "error_message": str(e)}
                self.results_manager.save_subtask_result(
                    task_name="子任务C: 提取评论者列表",
                    task_data={"error": "任务执行失败"},
                    execution_time=task_c_time,
                    error_info=error_info
                )
                raise
            
            # 缓存ABC结果
            if use_cache:
                logger.info("💾 缓存子任务ABC结果...")
                session_id = self.results_manager.session_id # 获取当前会话ID
                cache_key = await self.cache_manager.save_abc_results(
                    user_prompt=user_prompt,
                    parsed_query=parsed_query,
                    relevant_posts=relevant_posts,
                    candidate_users=candidate_users,
                    metadata={
                        'execution_time': (datetime.now() - start_time).total_seconds(),
                        'search_keywords': search_keywords,
                        'session_id': session_id  # 将会话ID存入缓存
                    }
                )
                if cache_key:
                    logger.info(f"✅ ABC结果已缓存，键: {cache_key}")
                    pipeline_results['cache_key'] = cache_key
            
            # 继续执行剩余任务
            return await self._execute_from_task_d(pipeline_results, user_prompt, additional_contents, max_recommendations, start_time)
            
        except Exception as e:
            logger.error(f"流水线执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "session_id": f"failed_{int(start_time.timestamp())}",
                "processing_time": (datetime.now() - start_time).total_seconds()
            }
    
    async def _execute_full_flow(self, user_prompt: str, additional_contents: List[str], max_recommendations: int, start_time: datetime, wait_for_confirmation, use_cache: bool) -> Dict[str, Any]:
        """执行完整的流程"""
        pipeline_results = {}
        
        # 子任务A: 用户输入层 - 语义解析
        if not wait_for_confirmation("子任务A: 用户输入语义解析"):
            total_time = (datetime.now() - start_time).total_seconds()
            self.results_manager.complete_session({"error": "用户跳过任务A", "status": "skipped"})
            return {
                "success": False, 
                "error": "用户跳过任务A",
                "session_id": session_id,
                "processing_time": total_time
            }
        logger.info("="*50)
        logger.info("子任务A: 用户输入语义解析")
        
        task_a_start = datetime.now()
        try:
            parsed_query = await self._task_a_parse_user_input(user_prompt)
            pipeline_results['parsed_query'] = parsed_query
            
            # 保存子任务A详细结果
            task_a_time = (datetime.now() - task_a_start).total_seconds()
            self.results_manager.save_subtask_result(
                task_name="子任务A: 用户输入语义解析",
                task_data={
                    "parsed_query": parsed_query.__dict__ if hasattr(parsed_query, '__dict__') else str(parsed_query),
                    "search_keywords": parsed_query.get_search_keywords() if hasattr(parsed_query, 'get_search_keywords') else [],
                    "topics": getattr(parsed_query, 'topics', []),
                    "emotional_state": getattr(parsed_query, 'emotional_state', {}),
                    "confidence": getattr(parsed_query, 'confidence', 0.0)
                },
                execution_time=task_a_time
            )
        except Exception as e:
            task_a_time = (datetime.now() - task_a_start).total_seconds()
            error_info = {"error_type": type(e).__name__, "error_message": str(e)}
            self.results_manager.save_subtask_result(
                task_name="子任务A: 用户输入语义解析",
                task_data={"error": "任务执行失败"},
                execution_time=task_a_time,
                error_info=error_info
            )
            raise
        
        # 子任务B: Reddit搜索与候选人提取  
        if not wait_for_confirmation("子任务B: Reddit搜索与候选人提取"):
            total_time = (datetime.now() - start_time).total_seconds()
            self.results_manager.complete_session({"error": "用户跳过任务B", "status": "skipped"})
            return {
                "success": False, 
                "error": "用户跳过任务B",
                "session_id": session_id,
                "processing_time": total_time
            }
        logger.info("="*50)
        logger.info("子任务B: Reddit搜索与候选人提取")
        
        task_b_start = datetime.now()
        try:
            search_keywords = parsed_query.get_search_keywords()
            relevant_posts = await self._task_b_reddit_search(search_keywords, user_prompt)
            pipeline_results['relevant_posts'] = relevant_posts
            
            # 保存子任务B详细结果
            task_b_time = (datetime.now() - task_b_start).total_seconds()
            self.results_manager.save_subtask_result(
                task_name="子任务B: Reddit搜索与候选人提取",
                task_data={
                    "search_keywords": search_keywords,
                    "relevant_posts": relevant_posts,
                    "posts_count": len(relevant_posts),
                    "average_score": sum(p.get('score', 0) for p in relevant_posts) / len(relevant_posts) if relevant_posts else 0
                },
                execution_time=task_b_time
            )
        except Exception as e:
            task_b_time = (datetime.now() - task_b_start).total_seconds()
            error_info = {"error_type": type(e).__name__, "error_message": str(e)}
            self.results_manager.save_subtask_result(
                task_name="子任务B: Reddit搜索与候选人提取",
                task_data={"error": "任务执行失败"},
                execution_time=task_b_time,
                error_info=error_info
            )
            raise
        
        # 子任务C: 提取评论者列表
        if not wait_for_confirmation("子任务C: 提取评论者列表"):
            total_time = (datetime.now() - start_time).total_seconds()
            self.results_manager.complete_session({"error": "用户跳过任务C", "status": "skipped"})
            return {
                "success": False, 
                "error": "用户跳过任务C",
                "session_id": session_id,
                "processing_time": total_time
            }
        logger.info("="*50)
        logger.info("子任务C: 提取评论者列表")
        
        task_c_start = datetime.now()
        try:
            candidate_users = await self._task_c_extract_commenters(relevant_posts)
            pipeline_results['candidate_users'] = candidate_users
            
            # 保存子任务C详细结果（包含详细的提取信息）
            task_c_time = (datetime.now() - task_c_start).total_seconds()
            
            # 构建详细的任务数据
            task_c_data = {
                "candidate_users": candidate_users,
                "users_count": len(candidate_users),
                "source_posts": len(relevant_posts)
            }
            
            # 如果有详细提取结果，添加到任务数据中
            if hasattr(self, 'detailed_extraction_result') and self.detailed_extraction_result:
                detailed_result = self.detailed_extraction_result
                
                # 添加提取统计信息
                task_c_data.update({
                    "extraction_stats": detailed_result.get("extraction_stats", {}),
                    "selection_criteria": detailed_result.get("selection_criteria", {}),
                    "top_reasons_summary": detailed_result.get("top_reasons_summary", {}),
                    
                    # 详细的评论者信息
                    "selected_commenters_detailed": detailed_result.get("selected_commenters", []),
                    "all_commenters_summary": {
                        "total_unique_commenters": len(detailed_result.get("all_commenters", {})),
                        "commenters_with_multiple_posts": len([
                            u for u in detailed_result.get("all_commenters", {}).values()
                            if u.get("comment_count", 0) > 1
                        ])
                    },
                    
                    # 前10个候选者的选择理由
                    "top_selection_reasons": [
                        {
                            "username": c.get("username"),
                            "rank": c.get("rank"),
                            "selection_reasons": c.get("selection_reasons", []),
                            "quality_summary": {
                                "comment_count": c.get("comment_count", 0),
                                "avg_score": round(c.get("avg_score", 0), 2),
                                "subreddit_diversity": c.get("subreddit_diversity", 0),
                                "selection_confidence": round(c.get("selection_confidence", 0), 3)
                            }
                        }
                        for c in detailed_result.get("selected_commenters", [])[:10]
                    ],
                    
                    # 评论内容样本（每个用户取1-2条最好的评论）
                    "comment_samples": self._extract_comment_samples(detailed_result)
                })
                
                # 清理详细结果以避免内存占用
                self.detailed_extraction_result = None
            
            self.results_manager.save_subtask_result(
                task_name="子任务C: 提取评论者列表",
                task_data=task_c_data,
                execution_time=task_c_time
            )
        except Exception as e:
            task_c_time = (datetime.now() - task_c_start).total_seconds()
            error_info = {"error_type": type(e).__name__, "error_message": str(e)}
            self.results_manager.save_subtask_result(
                task_name="子任务C: 提取评论者列表",
                task_data={"error": "任务执行失败"},
                execution_time=task_c_time,
                error_info=error_info
            )
            raise
        
        # 缓存ABC结果
        if use_cache:
            logger.info("💾 缓存子任务ABC结果...")
            session_id = self.results_manager.session_id # 获取当前会话ID
            cache_key = await self.cache_manager.save_abc_results(
                user_prompt=user_prompt,
                parsed_query=parsed_query,
                relevant_posts=relevant_posts,
                candidate_users=candidate_users,
                metadata={
                    'execution_time': (datetime.now() - start_time).total_seconds(),
                    'search_keywords': search_keywords,
                    'session_id': session_id  # 将会话ID存入缓存
                }
            )
        
        # 继续执行剩余任务
        return await self._execute_from_task_d(pipeline_results, user_prompt, additional_contents, max_recommendations, start_time)
    
    async def _execute_from_task_d(self, pipeline_results: Dict[str, Any], user_prompt: str, additional_contents: List[str], max_recommendations: int, start_time: datetime) -> Dict[str, Any]:
        """从子任务D开始执行（用于缓存跳转）"""
        from .config import settings
        debug_mode = getattr(settings, 'debug_step_by_step', True)
        
        def wait_for_confirmation(task_name: str):
            """等待用户确认继续"""
            if debug_mode:
                print(f"\n📋 准备执行: {task_name}")
                response = input("是否继续？输入 Y 确认，任何其他键跳过: ").strip().upper()
                if response != 'Y':
                    logger.info(f"用户选择跳过任务: {task_name}")
                    return False
                else:
                    logger.info(f"用户确认执行任务: {task_name}")
            return True
        
        candidate_users = pipeline_results['candidate_users']
        
        # 子任务D: 用户评论语料提取与图结构构建（优化并行处理）
        task_d_name = f"子任务D: {self.platform.title()}用户语料提取与图谱构建"
        if not wait_for_confirmation(task_d_name):
            total_time = (datetime.now() - start_time).total_seconds()
            return {
                "success": False, 
                "error": "用户跳过任务D",
                "session_id": f"skipped_{int(start_time.timestamp())}",
                "processing_time": total_time
            }
        logger.info("="*50)
        logger.info(task_d_name)
        task_d_start = datetime.now()
        candidate_graphs = await self._task_d_build_candidate_graphs_optimized(candidate_users)
        pipeline_results['candidate_graphs'] = candidate_graphs
        
        task_d_time = (datetime.now() - task_d_start).total_seconds()
        
        # 为JSON序列化准备数据
        serializable_graphs = []
        for g in candidate_graphs:
            if isinstance(g, dict):
                graph_item = g.copy()
                if 'graph' in graph_item and isinstance(graph_item['graph'], UserGraph):
                    graph_item['graph'] = graph_item['graph'].to_dict()
                serializable_graphs.append(graph_item)

        task_d_data = {
            "candidate_graphs": serializable_graphs, # 使用可序列化的图谱
            "input_users_count": len(candidate_users),
            "successful_graphs": len([g for g in candidate_graphs if g and g.get('graph')]),
            "failed_users": len([g for g in candidate_graphs if g and g.get('error')]),
            "graph_construction_details": [
                {
                    "user_id": g.get('username', '') if g else '',
                    "success": bool(g and g.get('graph')),
                    "posts_collected": g.get('user_data', {}).get('posts_count', 0) if g else 0,
                    "comments_collected": g.get('user_data', {}).get('comments_count', 0) if g else 0,
                    "graph_nodes": len(g.get('graph').nodes) if g and g.get('graph') else 0,
                    "graph_edges": len(g.get('graph').edges) if g and g.get('graph') else 0,
                    "construction_time": g.get('build_time', 0) if g else 0,
                    "error": g.get('error', None) if g else "Unknown error",
                    "graph_details": {
                        "node_types": {
                            node_type.value: len([n for n in g.get('graph').nodes.values() if n.node_type == node_type])
                            for node_type in [NodeType.EXPERIENCE, NodeType.BELIEF, NodeType.EMOTION, NodeType.TOPIC]
                        } if g and g.get('graph') else {},
                        "edge_types": {
                            rel_type.value: len([e for e in g.get('graph').edges if e.relation_type == rel_type])
                            for rel_type in [RelationType.CAUSES, RelationType.INFLUENCES, RelationType.CONTRADICTS, RelationType.SUPPORTS]
                        } if g and g.get('graph') else {}
                    } if g and g.get('graph') else {}
                } for g in candidate_graphs
            ],
            "construction_summary": {
                "total_users_attempted": len(candidate_users),
                "successful_constructions": len([g for g in candidate_graphs if g and g.get('graph')]),
                "success_rate": len([g for g in candidate_graphs if g and g.get('graph')]) / len(candidate_users) if candidate_users else 0,
                "avg_construction_time": sum(g.get('build_time', 0) for g in candidate_graphs if g.get('build_time')) / len([g for g in candidate_graphs if g.get('build_time')]) if any(g.get('build_time') for g in candidate_graphs) else 0,
                "avg_nodes_per_graph": sum(len(g.get('graph').nodes) for g in candidate_graphs if g.get('graph')) / len([g for g in candidate_graphs if g.get('graph')]) if any(g.get('graph') for g in candidate_graphs) else 0,
                "avg_edges_per_graph": sum(len(g.get('graph').edges) for g in candidate_graphs if g.get('graph')) / len([g for g in candidate_graphs if g.get('graph')]) if any(g.get('graph') for g in candidate_graphs) else 0
            }
        }
        
        self.results_manager.save_subtask_result(
            task_name=task_d_name,
            task_data=task_d_data,
            execution_time=task_d_time,
            force_flush=True  # 调试模式下立即落盘
        )
        
        # 子任务E: 用户三观图谱构建
        if not wait_for_confirmation("子任务E: 用户三观图谱构建"):
            total_time = (datetime.now() - start_time).total_seconds()
            return {
                "success": False, 
                "error": "用户跳过任务E",
                "session_id": f"skipped_{int(start_time.timestamp())}",
                "processing_time": total_time
            }
        logger.info("="*50)
        logger.info("子任务E: 用户三观图谱构建")
        user_graph = await self._task_e_build_user_graph(user_prompt, additional_contents)
        pipeline_results['user_graph'] = user_graph
        
        # 子任务F: 图谱完整性分析与追问生成
        if not wait_for_confirmation("子任务F: 图谱完整性分析与追问"):
            total_time = (datetime.now() - start_time).total_seconds()
            return {
                "success": False, 
                "error": "用户跳过任务F",
                "session_id": f"skipped_{int(start_time.timestamp())}",
                "processing_time": total_time
            }
        logger.info("="*50)
        logger.info("子任务F: 图谱完整性分析与追问")
        completeness_analysis = await self._task_f_analyze_completeness(user_graph, candidate_graphs)
        pipeline_results['completeness_analysis'] = completeness_analysis
        
        # 子任务G & H: 匹配评估与LLM分析
        if not wait_for_confirmation("子任务G&H: 匹配评估与LLM分析"):
            total_time = (datetime.now() - start_time).total_seconds()
            return {
                "success": False, 
                "error": "用户跳过任务G&H",
                "session_id": f"skipped_{int(start_time.timestamp())}",
                "processing_time": total_time
            }
        logger.info("="*50)
        logger.info("子任务G&H: 匹配评估与LLM分析")
        matching_results = await self._task_gh_match_and_analyze(user_graph, candidate_graphs)
        pipeline_results['matching_results'] = matching_results
        
        # 子任务I: 用户推荐展示
        if not wait_for_confirmation("子任务I: 生成推荐展示"):
            total_time = (datetime.now() - start_time).total_seconds()
            return {
                "success": False, 
                "error": "用户跳过任务I",
                "session_id": f"skipped_{int(start_time.timestamp())}",
                "processing_time": total_time
            }
        logger.info("="*50)
        logger.info("子任务I: 生成推荐展示")
        final_recommendations = await self._task_i_generate_recommendations(
            matching_results, max_recommendations
        )
        pipeline_results['final_recommendations'] = final_recommendations
        
        # 计算总耗时
        total_time = (datetime.now() - start_time).total_seconds()
        
        # 构建最终结果
        final_result = {
            "success": True,
            "session_id": session_id,
            "user_prompt": user_prompt,
            "processing_time": total_time,
            "pipeline_results": pipeline_results,
            "recommendations": final_recommendations,
            "completeness_info": completeness_analysis,
            "stats": {
                "relevant_posts_found": len(pipeline_results.get('relevant_posts', [])),
                "candidate_users_found": len(candidate_users),
                "successful_graphs": len(candidate_graphs),
                "final_recommendations": len(final_recommendations)
            }
        }
        
        # 完成详细结果记录会话
        detailed_results_file = self.results_manager.complete_session(final_result)
        final_result["detailed_results_file"] = detailed_results_file
        
        logger.info(f"流水线执行完成，总耗时: {total_time:.2f}秒")
        logger.info(f"找到 {len(final_recommendations)} 个推荐用户")
        logger.info(f"详细结果已保存到: {detailed_results_file}")
        
        return final_result
    
    async def _task_a_parse_user_input(self, user_prompt: str) -> ParsedQuery:
        """
        子任务A: 用户输入层 - 语义解析
        将自然语言输入拆分为搜索关键词和初始价值观信息
        """
        logger.info("开始解析用户输入...")
        
        try:
            # 使用语义分析器解析用户输入
            parsed_query = await self.semantic_analyzer.parse_user_input(user_prompt)
            
            logger.info(f"解析结果:")
            logger.info(f"  - 搜索关键词: {parsed_query.get_search_keywords()}")
            logger.info(f"  - 主要话题: {parsed_query.topics}")
            logger.info(f"  - 情绪状态: {parsed_query.emotional_state}")
            
            return parsed_query
            
        except Exception as e:
            logger.error(f"用户输入解析失败: {e}")
            # 降级策略：简单关键词提取
            simple_keywords = self._extract_simple_keywords(user_prompt)
            return ParsedQuery(
                original_text=user_prompt,
                search_intent=simple_keywords,
                values_info={"decision_style": "unknown"},
                emotional_state={"confusion": 0.7},
                topics=["生活困扰"],
                confidence=0.3
            )
    
    async def _task_b_reddit_search(self, keywords: List[str], user_prompt: str = "") -> List[Dict[str, Any]]:
        """
        子任务B: Reddit搜索与候选人提取
        使用多阶段搜索：关键词搜索 → Embedding重排 → LLM精排
        """
        logger.info(f"开始多阶段Reddit搜索，关键词: {keywords}")
        
        try:
            from .config import settings
            from datetime import datetime
            
            # 第一阶段：增强关键词搜索（组合查询+并发）
            logger.info("第一阶段：增强关键词搜索")
            stage1_start = datetime.now()
            
            initial_posts = await self.reddit_service.search_posts_by_keywords_enhanced(
                keywords=keywords,
                limit=settings.post_search_limit,  # 默认100
                time_filter=settings.reddit_search_time_filter  # 默认"all"
            )
            
            stage1_time = (datetime.now() - stage1_start).total_seconds()
            
            if not initial_posts:
                logger.warning("关键词搜索未找到帖子")
                # 记录第一阶段结果（空结果）
                self.results_manager.save_subtask_result(
                    task_name="第一阶段：关键词搜索",
                    task_data={
                        "stage": "keyword_search",
                        "search_keywords": keywords,
                        "posts_found": 0,
                        "posts_details": [],
                        "quality_stats": {},
                        "search_settings": {
                            "limit": settings.post_search_limit,
                            "time_filter": settings.reddit_search_time_filter
                        }
                    },
                    execution_time=stage1_time
                )
                return []
            
            logger.info(f"关键词搜索找到 {len(initial_posts)} 个帖子")
            
            # 记录第一阶段详细结果
            stage1_data = {
                "stage": "keyword_search",
                "search_keywords": keywords,
                "posts_found": len(initial_posts),
                "posts_details": [
                    {
                        "id": p.get('id', ''),
                        "title": p.get('title', '')[:100],  # 限制长度
                        "subreddit": p.get('subreddit', ''),
                        "score": p.get('score', 0),
                        "num_comments": p.get('num_comments', 0),
                        "quality_score": p.get('quality_score', 0),
                        "text_length": len(p.get('text', '')),
                        "created_utc": p.get('created_utc'),
                        "rank": i + 1
                    } for i, p in enumerate(initial_posts[:50])  # 只记录前50个以避免过大
                ],
                "quality_stats": {
                    "avg_score": sum(p.get('score', 0) for p in initial_posts) / len(initial_posts),
                    "avg_comments": sum(p.get('num_comments', 0) for p in initial_posts) / len(initial_posts),
                    "avg_quality_score": sum(p.get('quality_score', 0) for p in initial_posts) / len(initial_posts),
                    "subreddit_distribution": self._get_subreddit_distribution(initial_posts),
                    "score_range": {
                        "min": min(p.get('score', 0) for p in initial_posts),
                        "max": max(p.get('score', 0) for p in initial_posts)
                    }
                },
                "search_settings": {
                    "limit": settings.post_search_limit,
                    "time_filter": settings.reddit_search_time_filter
                }
            }
            
            self.results_manager.save_subtask_result(
                task_name="第一阶段：关键词搜索",
                task_data=stage1_data,
                execution_time=stage1_time
            )
            
            # 第二阶段：Embedding语义重排序
            stage2_start = datetime.now()
            
            if len(initial_posts) > settings.embedding_rerank_count:
                logger.info("第二阶段：Embedding语义重排序")
                embedding_posts = await self.reddit_service.rerank_posts_by_embedding(
                    posts=initial_posts,
                    user_query=user_prompt,
                    top_k=settings.embedding_rerank_count  # 默认30
                )
                logger.info(f"Embedding重排后保留 {len(embedding_posts)} 个帖子")
                stage2_skipped = False
            else:
                embedding_posts = initial_posts
                logger.info("帖子数量不足，跳过Embedding重排序")
                stage2_skipped = True
            
            stage2_time = (datetime.now() - stage2_start).total_seconds()
            
            # 记录第二阶段详细结果
            stage2_data = {
                "stage": "embedding_rerank",
                "input_posts_count": len(initial_posts),
                "output_posts_count": len(embedding_posts),
                "was_skipped": stage2_skipped,
                "user_query": user_prompt[:200] if user_prompt else "",  # 限制长度
                "posts_details": [
                    {
                        "id": p.get('id', ''),
                        "title": p.get('title', '')[:100],
                        "subreddit": p.get('subreddit', ''),
                        "original_score": p.get('score', 0),
                        "quality_score": p.get('quality_score', 0),
                        "embedding_similarity": p.get('embedding_similarity'),
                        "combined_score": p.get('combined_score'),
                        "rank_after_embedding": i + 1
                    } for i, p in enumerate(embedding_posts[:30])  # 记录所有embedding重排后的帖子
                ],
                "quality_stats": {
                    "avg_embedding_similarity": sum(p.get('embedding_similarity', 0) for p in embedding_posts if p.get('embedding_similarity')) / len([p for p in embedding_posts if p.get('embedding_similarity')]) if any(p.get('embedding_similarity') for p in embedding_posts) else 0,
                    "avg_combined_score": sum(p.get('combined_score', 0) for p in embedding_posts if p.get('combined_score')) / len([p for p in embedding_posts if p.get('combined_score')]) if any(p.get('combined_score') for p in embedding_posts) else 0,
                    "similarity_range": {
                        "min": min((p.get('embedding_similarity', 0) for p in embedding_posts if p.get('embedding_similarity')), default=0),
                        "max": max((p.get('embedding_similarity', 0) for p in embedding_posts if p.get('embedding_similarity')), default=0)
                    }
                },
                "rerank_settings": {
                    "target_count": settings.embedding_rerank_count,
                    "embedding_weight": 0.7,
                    "quality_weight": 0.3
                }
            }
            
            self.results_manager.save_subtask_result(
                task_name="第二阶段：Embedding重排序",
                task_data=stage2_data,
                execution_time=stage2_time
            )
            
            # 第三阶段：LLM最终精排
            stage3_start = datetime.now()
            
            if user_prompt and len(embedding_posts) >= 5:  # 只有在有足够帖子且有用户查询时才进行LLM精排
                logger.info("第三阶段：LLM最终精排")
                final_posts = await self.reddit_service.llm_final_rank_posts(
                    posts=embedding_posts,
                    user_query=user_prompt,
                    top_k=min(settings.llm_final_rank_count, len(embedding_posts))  # 不超过现有帖子数
                )
                logger.info(f"LLM精排后保留 {len(final_posts)} 个最相关帖子")
                relevant_posts = final_posts
                stage3_skipped = False
            else:
                relevant_posts = embedding_posts
                logger.info("使用Embedding排序结果作为最终结果（跳过LLM精排）")
                stage3_skipped = True
            
            stage3_time = (datetime.now() - stage3_start).total_seconds()
            
            # 记录第三阶段详细结果
            stage3_data = {
                "stage": "llm_final_rank",
                "input_posts_count": len(embedding_posts),
                "output_posts_count": len(relevant_posts),
                "was_skipped": stage3_skipped,
                "skip_reason": "insufficient_posts_or_no_query" if stage3_skipped else None,
                "user_query": user_prompt[:200] if user_prompt else "",
                "posts_details": [
                    {
                        "id": p.get('id', ''),
                        "title": p.get('title', '')[:100],
                        "subreddit": p.get('subreddit', ''),
                        "original_score": p.get('score', 0),
                        "quality_score": p.get('quality_score', 0),
                        "embedding_similarity": p.get('embedding_similarity'),
                        "llm_score": p.get('llm_score'),
                        "llm_reason": p.get('llm_reason', '')[:100] if p.get('llm_reason') else "",
                        "final_score": p.get('final_score'),
                        "final_rank": i + 1
                    } for i, p in enumerate(relevant_posts[:15])  # 记录最终的精排结果
                ],
                "quality_stats": {
                    "avg_llm_score": sum(p.get('llm_score', 0) for p in relevant_posts if p.get('llm_score')) / len([p for p in relevant_posts if p.get('llm_score')]) if any(p.get('llm_score') for p in relevant_posts) else 0,
                    "avg_final_score": sum(p.get('final_score', 0) for p in relevant_posts if p.get('final_score')) / len([p for p in relevant_posts if p.get('final_score')]) if any(p.get('final_score') for p in relevant_posts) else 0,
                    "llm_score_range": {
                        "min": min((p.get('llm_score', 0) for p in relevant_posts if p.get('llm_score')), default=0),
                        "max": max((p.get('llm_score', 0) for p in relevant_posts if p.get('llm_score')), default=0)
                    },
                    "subreddit_distribution_final": self._get_subreddit_distribution(relevant_posts)
                },
                "llm_settings": {
                    "target_count": settings.llm_final_rank_count,
                    "llm_weight": 0.5,
                    "embedding_weight": 0.3,
                    "quality_weight": 0.2
                }
            }
            
            self.results_manager.save_subtask_result(
                task_name="第三阶段：LLM最终精排",
                task_data=stage3_data,
                execution_time=stage3_time
            )
            
            logger.info(f"多阶段搜索完成，最终获得 {len(relevant_posts)} 个高质量帖子")
            
            # 记录质量统计
            if relevant_posts:
                avg_quality = sum(p.get('quality_score', 0) for p in relevant_posts) / len(relevant_posts)
                avg_embedding_sim = sum(p.get('embedding_similarity', 0) for p in relevant_posts if 'embedding_similarity' in p)
                embedding_count = len([p for p in relevant_posts if 'embedding_similarity' in p])
                if embedding_count > 0:
                    avg_embedding_sim /= embedding_count
                    logger.info(f"质量统计 - 平均质量分数: {avg_quality:.3f}, 平均语义相似度: {avg_embedding_sim:.3f}")
            
            # 记录三阶段汇总统计
            summary_data = {
                "multi_stage_summary": {
                    "stage1_posts": len(initial_posts),
                    "stage2_posts": len(embedding_posts),
                    "stage3_posts": len(relevant_posts),
                    "stage2_skipped": stage2_skipped,
                    "stage3_skipped": stage3_skipped,
                    "total_execution_time": stage1_time + stage2_time + stage3_time,
                    "efficiency_metrics": {
                        "stage1_posts_per_second": len(initial_posts) / stage1_time if stage1_time > 0 else 0,
                        "stage2_reduction_ratio": len(embedding_posts) / len(initial_posts) if initial_posts else 0,
                        "stage3_reduction_ratio": len(relevant_posts) / len(embedding_posts) if embedding_posts else 0
                    }
                },
                "final_quality_overview": {
                    "avg_quality_score": sum(p.get('quality_score', 0) for p in relevant_posts) / len(relevant_posts) if relevant_posts else 0,
                    "avg_embedding_similarity": avg_embedding_sim if embedding_count > 0 else 0,
                    "avg_llm_score": sum(p.get('llm_score', 0) for p in relevant_posts if p.get('llm_score')) / len([p for p in relevant_posts if p.get('llm_score')]) if any(p.get('llm_score') for p in relevant_posts) else 0,
                    "subreddit_diversity": len(set(p.get('subreddit', '') for p in relevant_posts))
                }
            }
            
            self.results_manager.save_subtask_result(
                task_name="多阶段搜索汇总统计",
                task_data=summary_data,
                execution_time=stage1_time + stage2_time + stage3_time
            )
            
            return relevant_posts
            
        except Exception as e:
            logger.error(f"多阶段Reddit搜索失败: {e}")
            return []
    
    async def _task_b_social_search(self, keywords: List[str], user_prompt: str = "") -> List[Dict[str, Any]]:
        """
        子任务B: 社交媒体搜索与候选人提取（统一接口）
        使用多阶段搜索：关键词搜索 → Embedding重排 → LLM精排
        """
        platform_name = self.platform.title()
        logger.info(f"开始多阶段{platform_name}搜索，关键词: {keywords}")
        
        try:
            from .config import settings
            from datetime import datetime
            
            # 第一阶段：增强关键词搜索（组合查询+并发）
            logger.info("第一阶段：增强关键词搜索")
            stage1_start = datetime.now()
            
            initial_posts = await self.social_service.search_posts_by_keywords_enhanced(
                keywords=keywords,
                limit=getattr(settings, 'post_search_limit', 100),  # 默认100
                time_filter=getattr(settings, f'{self.platform}_search_time_filter', 'all')  # 默认"all"
            )
            
            stage1_time = (datetime.now() - stage1_start).total_seconds()
            
            if not initial_posts:
                logger.warning("关键词搜索未找到帖子")
                # 记录第一阶段结果（空结果）
                self.results_manager.save_subtask_result(
                    task_name="第一阶段：关键词搜索",
                    task_data={
                        "stage": "keyword_search",
                        "platform": self.platform,
                        "search_keywords": keywords,
                        "posts_found": 0,
                        "posts_details": [],
                        "quality_stats": {},
                        "search_settings": {
                            "limit": getattr(settings, 'post_search_limit', 100),
                            "time_filter": getattr(settings, f'{self.platform}_search_time_filter', 'all')
                        }
                    },
                    execution_time=stage1_time
                )
                return []
            
            logger.info(f"关键词搜索找到 {len(initial_posts)} 个帖子")
            
            # 记录第一阶段详细结果
            stage1_data = {
                "stage": "keyword_search",
                "platform": self.platform,
                "search_keywords": keywords,
                "posts_found": len(initial_posts),
                "posts_details": [
                    {
                        "id": p.get('id', ''),
                        "title": p.get('title', p.get('text', '')[:100]),  # Twitter用text前100字符作为title
                        "author": p.get('author', p.get('subreddit', '')),  # 平台兼容
                        "score": p.get('score', 0),
                        "num_comments": p.get('num_comments', p.get('reply_count', 0)),  # 平台兼容
                        "quality_score": p.get('quality_score', 0),
                        "text_length": len(p.get('text', '')),
                        "created_utc": p.get('created_utc'),
                        "rank": i + 1
                    } for i, p in enumerate(initial_posts[:50])  # 只记录前50个以避免过大
                ],
                "quality_stats": {
                    "avg_score": sum(p.get('score', 0) for p in initial_posts) / len(initial_posts),
                    "avg_comments": sum(p.get('num_comments', p.get('reply_count', 0)) for p in initial_posts) / len(initial_posts),
                    "avg_quality_score": sum(p.get('quality_score', 0) for p in initial_posts) / len(initial_posts),
                    "author_distribution": self._get_author_distribution(initial_posts),
                    "score_range": {
                        "min": min(p.get('score', 0) for p in initial_posts),
                        "max": max(p.get('score', 0) for p in initial_posts)
                    }
                },
                "search_settings": {
                    "limit": getattr(settings, 'post_search_limit', 100),
                    "time_filter": getattr(settings, f'{self.platform}_search_time_filter', 'all')
                }
            }
            
            self.results_manager.save_subtask_result(
                task_name="第一阶段：关键词搜索",
                task_data=stage1_data,
                execution_time=stage1_time
            )
            
            # 第二阶段：Embedding语义重排序
            stage2_start = datetime.now()
            
            embedding_rerank_count = getattr(settings, 'embedding_rerank_count', 30)
            if len(initial_posts) > embedding_rerank_count:
                logger.info("第二阶段：Embedding语义重排序")
                embedding_posts = await self.social_service.rerank_posts_by_embedding(
                    posts=initial_posts,
                    user_query=user_prompt,
                    top_k=embedding_rerank_count
                )
                logger.info(f"Embedding重排后保留 {len(embedding_posts)} 个帖子")
                stage2_skipped = False
            else:
                embedding_posts = initial_posts
                logger.info("帖子数量不足，跳过Embedding重排序")
                stage2_skipped = True
            
            stage2_time = (datetime.now() - stage2_start).total_seconds()
            
            # 记录第二阶段详细结果
            stage2_data = {
                "stage": "embedding_rerank",
                "platform": self.platform,
                "input_posts_count": len(initial_posts),
                "output_posts_count": len(embedding_posts),
                "was_skipped": stage2_skipped,
                "user_query": user_prompt[:200] if user_prompt else "",
                "posts_details": [
                    {
                        "id": p.get('id', ''),
                        "title": p.get('title', p.get('text', '')[:100]),
                        "author": p.get('author', p.get('subreddit', '')),
                        "original_score": p.get('score', 0),
                        "quality_score": p.get('quality_score', 0),
                        "embedding_similarity": p.get('embedding_similarity'),
                        "combined_score": p.get('combined_score'),
                        "rank_after_embedding": i + 1
                    } for i, p in enumerate(embedding_posts[:30])
                ],
                "quality_stats": {
                    "avg_embedding_similarity": sum(p.get('embedding_similarity', 0) for p in embedding_posts if p.get('embedding_similarity')) / len([p for p in embedding_posts if p.get('embedding_similarity')]) if any(p.get('embedding_similarity') for p in embedding_posts) else 0,
                    "avg_combined_score": sum(p.get('combined_score', 0) for p in embedding_posts if p.get('combined_score')) / len([p for p in embedding_posts if p.get('combined_score')]) if any(p.get('combined_score') for p in embedding_posts) else 0,
                    "similarity_range": {
                        "min": min((p.get('embedding_similarity', 0) for p in embedding_posts if p.get('embedding_similarity')), default=0),
                        "max": max((p.get('embedding_similarity', 0) for p in embedding_posts if p.get('embedding_similarity')), default=0)
                    }
                },
                "rerank_settings": {
                    "target_count": embedding_rerank_count,
                    "embedding_weight": 0.7,
                    "quality_weight": 0.3
                }
            }
            
            self.results_manager.save_subtask_result(
                task_name="第二阶段：Embedding重排序",
                task_data=stage2_data,
                execution_time=stage2_time
            )
            
            # 第三阶段：LLM最终精排
            stage3_start = datetime.now()
            
            llm_final_rank_count = getattr(settings, 'llm_final_rank_count', 10)
            if user_prompt and len(embedding_posts) >= 5:
                logger.info("第三阶段：LLM最终精排")
                final_posts = await self.social_service.llm_final_rank_posts(
                    posts=embedding_posts,
                    user_query=user_prompt,
                    top_k=min(llm_final_rank_count, len(embedding_posts))
                )
                logger.info(f"LLM精排后保留 {len(final_posts)} 个最相关帖子")
                relevant_posts = final_posts
                stage3_skipped = False
            else:
                relevant_posts = embedding_posts
                logger.info("使用Embedding排序结果作为最终结果（跳过LLM精排）")
                stage3_skipped = True
            
            stage3_time = (datetime.now() - stage3_start).total_seconds()
            
            # 记录第三阶段详细结果
            stage3_data = {
                "stage": "llm_final_rank",
                "platform": self.platform,
                "input_posts_count": len(embedding_posts),
                "output_posts_count": len(relevant_posts),
                "was_skipped": stage3_skipped,
                "skip_reason": "insufficient_posts_or_no_query" if stage3_skipped else None,
                "user_query": user_prompt[:200] if user_prompt else "",
                "posts_details": [
                    {
                        "id": p.get('id', ''),
                        "title": p.get('title', p.get('text', '')[:100]),
                        "author": p.get('author', p.get('subreddit', '')),
                        "original_score": p.get('score', 0),
                        "quality_score": p.get('quality_score', 0),
                        "embedding_similarity": p.get('embedding_similarity'),
                        "llm_score": p.get('llm_score'),
                        "llm_reason": p.get('llm_reason', '')[:100] if p.get('llm_reason') else "",
                        "final_score": p.get('final_score'),
                        "final_rank": i + 1
                    } for i, p in enumerate(relevant_posts[:15])
                ],
                "quality_stats": {
                    "avg_llm_score": sum(p.get('llm_score', 0) for p in relevant_posts if p.get('llm_score')) / len([p for p in relevant_posts if p.get('llm_score')]) if any(p.get('llm_score') for p in relevant_posts) else 0,
                    "avg_final_score": sum(p.get('final_score', 0) for p in relevant_posts if p.get('final_score')) / len([p for p in relevant_posts if p.get('final_score')]) if any(p.get('final_score') for p in relevant_posts) else 0,
                    "llm_score_range": {
                        "min": min((p.get('llm_score', 0) for p in relevant_posts if p.get('llm_score')), default=0),
                        "max": max((p.get('llm_score', 0) for p in relevant_posts if p.get('llm_score')), default=0)
                    },
                    "author_distribution_final": self._get_author_distribution(relevant_posts)
                },
                "llm_settings": {
                    "target_count": llm_final_rank_count,
                    "llm_weight": 0.5,
                    "embedding_weight": 0.3,
                    "quality_weight": 0.2
                }
            }
            
            self.results_manager.save_subtask_result(
                task_name="第三阶段：LLM最终精排",
                task_data=stage3_data,
                execution_time=stage3_time
            )
            
            logger.info(f"多阶段{platform_name}搜索完成，最终获得 {len(relevant_posts)} 个高质量帖子")
            
            # 记录质量统计
            if relevant_posts:
                avg_quality = sum(p.get('quality_score', 0) for p in relevant_posts) / len(relevant_posts)
                avg_embedding_sim = sum(p.get('embedding_similarity', 0) for p in relevant_posts if 'embedding_similarity' in p)
                embedding_count = len([p for p in relevant_posts if 'embedding_similarity' in p])
                if embedding_count > 0:
                    avg_embedding_sim /= embedding_count
                    logger.info(f"质量统计 - 平均质量分数: {avg_quality:.3f}, 平均语义相似度: {avg_embedding_sim:.3f}")
            
            # 记录三阶段汇总统计
            summary_data = {
                "multi_stage_summary": {
                    "platform": self.platform,
                    "stage1_posts": len(initial_posts),
                    "stage2_posts": len(embedding_posts),
                    "stage3_posts": len(relevant_posts),
                    "stage2_skipped": stage2_skipped,
                    "stage3_skipped": stage3_skipped,
                    "total_execution_time": stage1_time + stage2_time + stage3_time,
                    "efficiency_metrics": {
                        "stage1_posts_per_second": len(initial_posts) / stage1_time if stage1_time > 0 else 0,
                        "stage2_reduction_ratio": len(embedding_posts) / len(initial_posts) if initial_posts else 0,
                        "stage3_reduction_ratio": len(relevant_posts) / len(embedding_posts) if embedding_posts else 0
                    }
                },
                "final_quality_overview": {
                    "avg_quality_score": sum(p.get('quality_score', 0) for p in relevant_posts) / len(relevant_posts) if relevant_posts else 0,
                    "avg_embedding_similarity": avg_embedding_sim if embedding_count > 0 else 0,
                    "avg_llm_score": sum(p.get('llm_score', 0) for p in relevant_posts if p.get('llm_score')) / len([p for p in relevant_posts if p.get('llm_score')]) if any(p.get('llm_score') for p in relevant_posts) else 0,
                    "author_diversity": len(set(p.get('author', p.get('subreddit', '')) for p in relevant_posts))
                }
            }
            
            self.results_manager.save_subtask_result(
                task_name="多阶段搜索汇总统计",
                task_data=summary_data,
                execution_time=stage1_time + stage2_time + stage3_time
            )
            
            return relevant_posts
            
        except Exception as e:
            logger.error(f"多阶段{platform_name}搜索失败: {e}")
            return []
    
    async def _task_c_extract_commenters(self, posts: List[Dict[str, Any]]) -> List[str]:
        """
        子任务C: 提取评论者列表（增强版）
        从相关帖子中提取活跃评论者的Reddit ID，记录详细信息和选择原因
        """
        logger.info(f"开始从 {len(posts)} 个帖子中提取评论者（详细模式）...")
        
        try:
            # 转换为RedditPost对象
            reddit_posts = []
            for post in posts:
                reddit_posts.append(type('RedditPost', (), {
                    'id': post.get('id'),
                    'text': post.get('text', ''),
                    'subreddit': post.get('subreddit', ''),
                    'score': post.get('score', 0)
                })())
            
            # 使用详细版本提取优质评论者
            detailed_result = await self.social_service.extract_quality_commenters_detailed(
                posts=reddit_posts,
                min_score=0,
                min_length=100,
                max_commenters=10  # 只取 Top-10 高置信候选
            )
            
            # 检查是否有错误
            if "error" in detailed_result:
                logger.error(f"详细提取失败: {detailed_result['error']}")
                return []
            
            # 提取候选用户名列表（保持兼容性）
            selected_commenters = detailed_result.get('selected_commenters', [])
            candidate_users = [commenter['username'] for commenter in selected_commenters]
            
            # 保存详细结果到全局变量供结果管理器使用
            self.detailed_extraction_result = detailed_result
            
            logger.info(f"详细提取完成：{len(candidate_users)} 个候选用户")
            
            # 显示前3个用户的选择理由
            for i, commenter in enumerate(selected_commenters[:3]):
                reasons = '; '.join(commenter.get('selection_reasons', ['无理由']))
                logger.info(f"  候选{i+1}: {commenter['username']} - {reasons}")
            
            return candidate_users
            
        except Exception as e:
            logger.error(f"提取评论者失败: {e}")
            return []
    
    def _extract_comment_samples(self, detailed_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从详细提取结果中提取评论样本
        每个选中的用户提取1-2条最高质量的评论作为样本
        """
        comment_samples = []
        
        try:
            selected_commenters = detailed_result.get("selected_commenters", [])
            
            for commenter in selected_commenters[:10]:  # 只处理前10个用户
                username = commenter.get("username")
                comments = commenter.get("comments", [])
                
                # 按质量分数排序，取前2条
                sorted_comments = sorted(
                    comments, 
                    key=lambda x: x.get("quality_score", 0), 
                    reverse=True
                )
                
                top_comments = sorted_comments[:2]
                
                for comment in top_comments:
                    # 截取评论内容，避免过长
                    comment_text = comment.get("text", "")
                    if len(comment_text) > 300:
                        comment_text = comment_text[:300] + "..."
                    
                    comment_sample = {
                        "username": username,
                        "comment_id": comment.get("comment_id"),
                        "text": comment_text,
                        "reddit_score": comment.get("score", 0),
                        "quality_score": round(comment.get("quality_score", 0), 2),
                        "subreddit": comment.get("subreddit"),
                        "post_title": comment.get("post_title", "")[:100] + "..." if len(comment.get("post_title", "")) > 100 else comment.get("post_title", ""),
                        "created_time": comment.get("created_utc"),
                        "permalink": comment.get("permalink")
                    }
                    comment_samples.append(comment_sample)
            
            logger.info(f"提取了 {len(comment_samples)} 个评论样本")
            return comment_samples
            
        except Exception as e:
            logger.error(f"提取评论样本失败: {e}")
            return []
    
    async def _task_d_build_candidate_graphs_optimized(self, usernames: List[str]) -> List[Dict[str, Any]]:
        """
        子任务D: Redditor评论语料提取与图结构构建（LLM时间线版本）
        直接让LLM处理时间信息，构建时间感知的图谱
        """
        logger.info(f"🚀 开始LLM时间线感知的并行构建 {len(usernames)} 个用户图谱...")
        
        candidate_graphs = []
        
        # 优化并发控制：根据机器 CPU 核心数动态调整并发，避免过度排队
        cpu_cores = os.cpu_count() or 4  # 若无法检测则默认 4
        max_concurrent = min(cpu_cores * 2, 16, len(usernames))  # MVP 阶段上限控制在 16
        batch_size = 20  # 每批处理 20 个用户，可按需调整
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def build_single_graph_with_llm_timeline(username: str) -> Optional[Dict[str, Any]]:
            async with semaphore:
                try:
                    start_time = datetime.now()
                    
                    # 获取用户完整历史
                    user_data = await self.social_service.get_user_comprehensive_history(
                        username=username,
                        limit=150,  # 减少到150避免过长等待
                        filter_top_level_comments=True  # 只获取一级评论，提升质量
                    )
                    
                    if not user_data:
                        logger.warning(f"❌ 无法获取用户 {username} 的历史数据")
                        return None
                    
                    # 🕒 提取时间线内容并格式化为LLM友好的格式
                    temporal_formatted_contents = self._format_contents_with_timeline(user_data)
                    
                    if len(temporal_formatted_contents) < 3:
                        logger.warning(f"⚠️  用户 {username} 内容太少，跳过")
                        return None
                    
                    # 🎯 智能选择高质量的时间线内容
                    selected_contents = self._select_quality_temporal_contents(temporal_formatted_contents)[:15]
                    
                    # 🧠 直接让LLM构建时间线感知的图谱
                    user_graph = await self.graph_builder.build_user_graph(
                        contents=selected_contents,
                        user_context=f"{self.platform.title()}用户 {username}，请充分利用时间信息构建因果关系"
                    )
                    
                    elapsed = (datetime.now() - start_time).total_seconds()
                    
                    # 统计时间线关系
                    temporal_edges = [e for e in user_graph.edges if 'temporal' in e.evidence.lower() or 'time' in e.evidence.lower()]
                    
                    logger.info(f"✅ 用户 {username} LLM时间线图谱完成: {len(user_graph.nodes)} 个节点，{len(user_graph.edges)} 条边，其中 {len(temporal_edges)} 条时间线关系，耗时 {elapsed:.1f}s")
                    
                    # 📊 简化的统计信息
                    temporal_stats = {
                        'total_contents': len(selected_contents),
                        'temporal_edges_count': len(temporal_edges),
                        'llm_temporal_enabled': True
                    }
                    
                    return {
                        'username': username,
                        'user_data': user_data,
                        'graph': user_graph,
                        'content_stats': user_data.get('content_stats', {}),
                        'temporal_stats': temporal_stats,
                        'build_time': elapsed
                    }
                    
                except Exception as e:
                    logger.error(f"❌ 为用户 {username} 构建LLM时间线图谱失败: {e}")
                    return None
        
        # 分批并行处理
        total_processed = 0
        for i in range(0, len(usernames), batch_size):
            batch = usernames[i:i + batch_size]
            logger.info(f"📦 处理第 {i//batch_size + 1} 批，共 {len(batch)} 个用户")
            
            # 并发构建当前批次
            tasks = [build_single_graph_with_llm_timeline(username) for username in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 收集成功的结果
            for result in batch_results:
                if result is not None and not isinstance(result, Exception):
                    candidate_graphs.append(result)
                    total_processed += 1
            
            logger.info(f"📊 第 {i//batch_size + 1} 批完成，成功: {len([r for r in batch_results if r is not None and not isinstance(r, Exception)])}/{len(batch)}")
            
            # 短暂休息，避免API限制
            if i + batch_size < len(usernames):
                await asyncio.sleep(1)
        
        # 计算统计信息
        if candidate_graphs:
            avg_build_time = sum(cg.get('build_time', 0) for cg in candidate_graphs) / len(candidate_graphs)
            avg_nodes = sum(len(cg['graph'].nodes) for cg in candidate_graphs) / len(candidate_graphs)
            avg_temporal_edges = sum(cg.get('temporal_stats', {}).get('temporal_edges_count', 0) for cg in candidate_graphs) / len(candidate_graphs)
            
            logger.info(f"🎯 LLM时间线感知构建完成: {len(candidate_graphs)}/{len(usernames)} 成功")
            logger.info(f"📈 平均构建时间: {avg_build_time:.1f}s，平均节点数: {avg_nodes:.1f}")
            logger.info(f"⏰ 平均LLM时间线关系数: {avg_temporal_edges:.1f}")
        else:
            logger.warning("⚠️  没有成功构建任何用户图谱")
        
        return candidate_graphs
    
    def _format_contents_with_timeline(self, user_data: Dict[str, Any]) -> List[str]:
        """🕒 将用户内容格式化为包含时间信息的文本，直接供LLM处理"""
        formatted_contents = []
        
        # 收集所有内容并排序
        all_content_items = []
        
        # 收集帖子
        for post in user_data.get('posts', []):
            if post.get('title') and post.get('text'):
                all_content_items.append({
                    'timestamp': post.get('timestamp', datetime.now()),
                    'content': f"标题: {post['title']}\n内容: {post['text'][:500]}",
                    'type': 'post',
                    'score': post.get('score', 0)
                })
        
        # 收集评论
        for comment in user_data.get('comments', []):
            if comment.get('text') and len(comment['text']) > 50:
                all_content_items.append({
                    'timestamp': comment.get('timestamp', datetime.now()),
                    'content': comment['text'][:300],
                    'type': 'comment',
                    'score': comment.get('score', 0)
                })
        
        # 按时间排序
        all_content_items.sort(key=lambda x: x['timestamp'])
        
        # 格式化为带时间标记的文本
        for item in all_content_items:
            timestamp_str = item['timestamp'].strftime('【%Y年%m月%d日】')
            formatted_content = f"{timestamp_str} {item['content']}"
            formatted_contents.append(formatted_content)
        
        logger.debug(f"格式化了 {len(formatted_contents)} 个带时间标记的内容")
        return formatted_contents
    
    def _select_quality_temporal_contents(self, formatted_contents: List[str]) -> List[str]:
        """🎯 从格式化的时间线内容中选择高质量的部分"""
        if not formatted_contents:
            return []
        
        # 按质量评分排序
        scored_contents = []
        for content in formatted_contents:
            score = self._calculate_formatted_content_score(content)
            scored_contents.append((score, content))
        
        # 排序并选择高质量内容
        scored_contents.sort(key=lambda x: x[0], reverse=True)
        
        # 选择前面高质量的内容，同时保证时间分布均匀
        selected = []
        for score, content in scored_contents:
            if score > 2.0:  # 质量阈值
                selected.append(content)
        
        # 如果高质量内容太少，补充一些中等质量的
        if len(selected) < 5:
            for score, content in scored_contents:
                if 1.0 <= score <= 2.0 and len(selected) < 10:
                    selected.append(content)
        
        logger.debug(f"从 {len(formatted_contents)} 个内容中选择了 {len(selected)} 个高质量内容")
        return selected
    
    def _calculate_formatted_content_score(self, formatted_content: str) -> float:
        """📊 计算格式化内容的质量分数"""
        score = 0.0
        
        # 去掉时间标记，分析纯文本
        if '】' in formatted_content:
            text = formatted_content.split('】', 1)[1].strip()
        else:
            text = formatted_content
        
        # 长度适中加分
        if 100 <= len(text) <= 500:
            score += 3.0
        elif 50 <= len(text) <= 800:
            score += 2.0
        elif len(text) > 30:
            score += 1.0
        
        # 情感表达加分
        emotion_indicators = ['觉得', '认为', '感觉', '希望', '担心', '困惑', '迷茫', '焦虑', '开心', '失望', '后来', '现在', '当时']
        emotion_count = sum(1 for word in emotion_indicators if word in text)
        score += emotion_count * 0.5
        
        # 经历描述加分
        experience_indicators = ['经历', '遇到', '发生', '经过', '体验', '参加', '做了', '试过', '学会', '明白']
        experience_count = sum(1 for word in experience_indicators if word in text)
        score += experience_count * 0.8
        
        # 时间表达加分（这些词表明时间发展）
        temporal_indicators = ['后来', '然后', '现在', '当时', '之前', '以前', '最近', '开始', '结果', '最终']
        temporal_count = sum(1 for word in temporal_indicators if word in text)
        score += temporal_count * 1.0
        
        # 避免短评和链接
        if len(text.strip()) < 30 or text.startswith('http'):
            score -= 2.0
        
        return max(0.0, score)
    

    
    async def _task_e_build_user_graph(self, user_prompt: str, 
                                     additional_contents: List[str] = None) -> UserGraph:
        """
        子任务E: 用户三观图谱构建
        基于用户输入构建初始三观图谱
        """
        logger.info("开始构建用户三观图谱...")
        
        try:
            # 收集所有用户内容
            all_contents = [user_prompt]
            if additional_contents:
                all_contents.extend(additional_contents)
            
            # 构建用户图谱
            user_graph = await self.graph_builder.build_user_graph(
                contents=all_contents,
                user_context="目标用户画像"
            )
            
            logger.info(f"用户图谱构建完成: {len(user_graph.nodes)} 个节点，{len(user_graph.edges)} 条边")
            
            return user_graph
            
        except Exception as e:
            logger.error(f"用户图谱构建失败: {e}")
            # 返回空图谱
            return UserGraph(user_id="target_user", created_at=datetime.now())
    
    async def _task_f_analyze_completeness(self, user_graph: UserGraph, 
                                         candidate_graphs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        子任务F: 图谱完整性分析与追问生成
        识别用户图谱的缺失部分，生成针对性追问
        """
        logger.info("开始分析图谱完整性...")
        
        try:
            # 使用用户画像分析器
            completeness_result = await self.user_profiler.analyze_completeness(
                user_graph=user_graph,
                reference_graphs=[cg['graph'] for cg in candidate_graphs]
            )
            
            logger.info(f"完整性分析结果:")
            logger.info(f"  - 完整性分数: {completeness_result.get('completeness_score', 0):.2f}")
            logger.info(f"  - 生成追问数量: {len(completeness_result.get('follow_up_questions', []))}")
            
            return completeness_result
            
        except Exception as e:
            logger.error(f"完整性分析失败: {e}")
            return {
                'completeness_score': 0.7,
                'is_complete': True,
                'follow_up_questions': [],
                'missing_aspects': []
            }
    
    async def _task_gh_match_and_analyze(self, user_graph: UserGraph, 
                                       candidate_graphs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        子任务G&H: 匹配评估与LLM分析
        计算图结构匹配度并使用LLM分析匹配原因
        """
        logger.info(f"开始匹配分析，候选者数量: {len(candidate_graphs)}")
        
        matching_results = []
        
        # 创建用户Profile用于匹配
        from .models.user_models import UserProfile
        user_profile = UserProfile(
            user_id="target_user",
            original_query="目标用户图谱",  # 添加必需的 original_query 字段
            graph=user_graph,
            created_at=user_graph.created_at
        )
        
        for candidate in candidate_graphs:
            try:
                candidate_graph = candidate['graph']
                username = candidate['username']
                
                # 创建候选用户Profile
                candidate_profile = UserProfile(
                    user_id=username,
                    original_query="",  # 候选用户没有原始查询
                    graph=candidate_graph,
                    created_at=candidate_graph.created_at
                )
                
                # 计算共鸣匹配度
                resonance_score = await self.resonance_matcher.calculate_resonance_score(
                    user_profile=user_profile,
                    candidate_profile=candidate_profile
                )
                
                # 生成推荐摘要和对话建议
                from .models.user_models import CandidateUser, UserContentHistory, QualityMetrics
                
                # 将posts和comments从字典列表转换为字符串列表
                posts_text = []
                for post in candidate['user_data'].get('posts', []):
                    if isinstance(post, dict):
                        title = post.get('title', '')
                        text = post.get('text', '')
                        if title and text:
                            posts_text.append(f"{title}\n{text}")
                        elif title:
                            posts_text.append(title)
                        elif text:
                            posts_text.append(text)
                    elif isinstance(post, str):
                        posts_text.append(post)
                
                comments_text = []
                for comment in candidate['user_data'].get('comments', []):
                    if isinstance(comment, dict):
                        text = comment.get('text', '')
                        if text:
                            comments_text.append(text)
                    elif isinstance(comment, str):
                        comments_text.append(comment)
                
                # 创建候选用户对象
                candidate_user = CandidateUser(
                    username=username,
                    content_history=UserContentHistory(
                        username=username,
                        posts=posts_text,
                        comments=comments_text,
                        total_content_length=candidate['content_stats'].get('total_length', 100)
                    ),
                    quality_metrics=QualityMetrics(
                        content_length=candidate['content_stats'].get('total_length', 100),
                        engagement_score=0.7,
                        coherence_score=0.8,
                        depth_score=0.7,
                        authenticity_score=0.8,
                        overall_quality=0.75
                    ),
                    user_graph=candidate_graph
                )
                
                # 创建排序候选对象
                from .models.matching_models import RankedCandidate
                ranked_candidate = RankedCandidate(
                    candidate=candidate_user,
                    resonance_score=resonance_score,
                    rank=1,
                    primary_resonance_reasons=[]
                )
                
                # 生成推荐摘要
                recommendation_summary = await self.resonance_matcher.generate_recommendation_summary(
                    user_profile=user_profile,
                    matched_candidate=ranked_candidate
                )
                
                # 添加候选者信息
                matching_result = {
                    'username': username,
                    'resonance_score': resonance_score.overall_score,
                    'structural_similarity': resonance_score.structural_similarity,
                    'semantic_similarity': resonance_score.semantic_similarity,
                    'emotional_alignment': resonance_score.emotional_alignment,
                    'value_compatibility': resonance_score.value_compatibility,
                    'experience_overlap': resonance_score.experience_overlap,
                    'reasoning': recommendation_summary.summary_text,
                    'shared_themes': resonance_score.shared_themes,
                    'conversation_starters': recommendation_summary.conversation_starters,
                    'user_data': candidate['user_data'],
                    'content_stats': candidate['content_stats']
                }
                
                matching_results.append(matching_result)
                
                logger.info(f"用户 {username} 匹配完成，共鸣分数: {resonance_score.overall_score:.3f}")
                
            except Exception as e:
                logger.error(f"匹配分析失败 - {candidate.get('username', 'unknown')}: {e}")
                continue
        
        # 按共鸣分数排序
        matching_results.sort(key=lambda x: x['resonance_score'], reverse=True)
        
        logger.info(f"匹配分析完成，有效结果: {len(matching_results)} 个")
        
        return matching_results
    
    async def _task_i_generate_recommendations(self, matching_results: List[Dict[str, Any]], 
                                             max_recommendations: int = 5) -> List[Dict[str, Any]]:
        """
        子任务I: 用户推荐展示
        生成最终的推荐结果，包含推荐理由和对话建议
        """
        logger.info(f"开始生成推荐展示，候选结果: {len(matching_results)} 个")
        
        # 筛选高质量匹配
        high_quality_matches = [
            result for result in matching_results 
            if result['resonance_score'] >= 0.6  # 共鸣阈值
        ]
        
        # 取前N个推荐
        top_matches = high_quality_matches[:max_recommendations]
        
        final_recommendations = []
        
        for i, match in enumerate(top_matches):
            try:
                # 确定推荐强度
                score = match['resonance_score']
                if score >= 0.8:
                    strength = "强烈推荐"
                elif score >= 0.7:
                    strength = "推荐"
                else:
                    strength = "一般推荐"
                
                # 生成推荐摘要
                summary = await self._generate_recommendation_summary(match)
                
                # 生成开场语
                conversation_starters = match.get('conversation_starters', [])
                if not conversation_starters:
                    conversation_starters = await self._generate_conversation_starters(match)
                
                recommendation = {
                    'rank': i + 1,
                    'candidate_id': match['username'],
                    'resonance_score': score,
                    'recommendation_strength': strength,
                    'summary': summary,
                    'reasoning': match.get('reasoning', ''),
                    'shared_themes': match.get('shared_themes', []),
                    'conversation_starters': conversation_starters,
                    'reddit_profile_url': f"https://reddit.com/user/{match['username']}",
                    'content_stats': match['content_stats'],
                    'matching_details': {
                        'structural_similarity': match['structural_similarity'],
                        'semantic_similarity': match['semantic_similarity'],
                        'emotional_alignment': match['emotional_alignment'],
                        'value_compatibility': match['value_compatibility'],
                        'experience_overlap': match['experience_overlap']
                    }
                }
                
                final_recommendations.append(recommendation)
                
            except Exception as e:
                logger.error(f"生成推荐失败 - {match.get('username', 'unknown')}: {e}")
                continue
        
        logger.info(f"最终推荐生成完成: {len(final_recommendations)} 个")
        
        return final_recommendations
    
    async def _generate_recommendation_summary(self, match: Dict[str, Any]) -> str:
        """生成推荐摘要"""
        try:
            username = match['username']
            reasoning = match.get('reasoning', '')
            shared_themes = match.get('shared_themes', [])
            
            if reasoning:
                return reasoning
            elif shared_themes:
                themes_text = "、".join(shared_themes[:3])
                return f"在{themes_text}等方面有共同经历和观点"
            else:
                return f"与用户{username}在多个维度上显示出良好的共鸣匹配"
            
        except Exception as e:
            logger.error(f"生成推荐摘要失败: {e}")
            return "显示出良好的匹配潜力"
    
    async def _generate_conversation_starters(self, match: Dict[str, Any]) -> List[str]:
        """生成对话开场语"""
        try:
            shared_themes = match.get('shared_themes', [])
            
            if not shared_themes:
                return [
                    f"您好，我看到您的一些观点很有见地",
                    f"想和您交流一下相关的话题"
                ]
            
            starters = []
            for theme in shared_themes[:2]:
                starters.append(f"我看到您在{theme}方面很有经验，想请教一下")
            
            return starters
            
        except Exception as e:
            logger.error(f"生成对话开场语失败: {e}")
            return ["您好，希望能和您交流一下"]
    
    def _extract_simple_keywords(self, text: str) -> List[str]:
        """简单关键词提取（降级策略）"""
        import re
        
        # 移除标点符号，分词
        cleaned_text = re.sub(r'[^\w\s]', '', text)
        words = cleaned_text.split()
        
        # 过滤停用词
        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        
        return keywords[:5]  # 返回前5个关键词
    
    def _get_subreddit_distribution(self, posts: List[Dict[str, Any]]) -> Dict[str, int]:
        """计算subreddit分布统计"""
        distribution = {}
        for post in posts:
            subreddit = post.get('subreddit', 'unknown')
            if subreddit:
                distribution[subreddit] = distribution.get(subreddit, 0) + 1
        
        # 按频次排序，返回前10个
        sorted_distribution = dict(sorted(distribution.items(), key=lambda x: x[1], reverse=True)[:10])
        return sorted_distribution
    
    def _get_author_distribution(self, posts: List[Dict[str, Any]]) -> Dict[str, int]:
        """计算作者分布统计（平台通用）"""
        distribution = {}
        for post in posts:
            # 平台兼容：Reddit用subreddit，Twitter用author
            author = post.get('author', post.get('subreddit', 'unknown'))
            if author:
                distribution[author] = distribution.get(author, 0) + 1
        
        # 按频次排序，返回前10个
        sorted_distribution = dict(sorted(distribution.items(), key=lambda x: x[1], reverse=True)[:10])
        return sorted_distribution
    
    async def close(self):
        """关闭流水线，清理资源"""
        logger.info("关闭Reddit共鸣推荐流水线...")
        
        try:
            await self.social_service.close()
            await self.ai_service.close()
            logger.info("流水线资源清理完成")
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")
    
    async def list_cached_sessions(self) -> List[Dict[str, Any]]:
        """列出所有缓存的会话"""
        return self.cache_manager.list_cached_sessions()
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return await self.cache_manager.get_cache_stats() 