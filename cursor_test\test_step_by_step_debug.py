"""
步进调试测试脚本 - 增强版
允许用户逐步测试流水线的每个功能模块
支持缓存子任务ABC并直接跳转到子任务D
新增：详细结果保存功能，记录每个子任务的详细执行过程
"""
import asyncio
import sys
import os
import logging
import json
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 配置日志输出
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout)  # 输出到控制台
    ]
)

from resona.pipeline import RedditResonancePipeline
from resona.config import settings

# ============================================================================
# 快速模式配置
# ============================================================================

class FastModeConfig:
    """快速模式配置 - 大幅减少数据量以提高运行速度"""
    
    @classmethod
    def apply_fast_config(cls):
        """应用快速模式配置"""
        print("⚡ 应用快速模式配置...")
        
        # 保存原始配置
        cls.original_config = {
            'reddit_search_limit': settings.reddit_search_limit,
            'reddit_history_limit': settings.reddit_history_limit,
            'post_search_limit': settings.post_search_limit,
            'max_quality_commenters': settings.max_quality_commenters,
            'top_k_matches': settings.top_k_matches,
            'embedding_rerank_count': settings.embedding_rerank_count,
            'llm_final_rank_count': settings.llm_final_rank_count,
        }
        
        # 应用快速配置（大幅减少数据量）
        settings.reddit_search_limit = 10          # 原始：50，减少80%
        settings.reddit_history_limit = 20         # 原始：100，减少80%
        settings.post_search_limit = 15             # 原始：100，减少85%
        settings.max_quality_commenters = 5        # 原始：30，减少83%
        settings.top_k_matches = 2                 # 原始：5，减少60%
        settings.embedding_rerank_count = 8        # 原始：30，减少73%
        settings.llm_final_rank_count = 4          # 原始：10，减少60%
        
        print("📊 快速模式配置已应用:")
        print(f"   - Reddit搜索帖子: {settings.post_search_limit} (原始: {cls.original_config['post_search_limit']})")
        print(f"   - 每用户历史数据: {settings.reddit_history_limit} (原始: {cls.original_config['reddit_history_limit']})")
        print(f"   - 最大候选用户: {settings.max_quality_commenters} (原始: {cls.original_config['max_quality_commenters']})")
        print(f"   - 推荐数量: {settings.top_k_matches} (原始: {cls.original_config['top_k_matches']})")
        print(f"   - 预计运行时间: 1-2分钟 (正常模式: 5-10分钟)")
        print()
    
    @classmethod
    def restore_original_config(cls):
        """恢复原始配置"""
        if hasattr(cls, 'original_config'):
            print("🔄 恢复原始配置...")
            for key, value in cls.original_config.items():
                setattr(settings, key, value)

class DetailedResultsCapture:
    """详细结果捕获器 - 记录每个子任务的详细执行过程"""
    
    def __init__(self):
        self.results = {
            "session_info": {},
            "task_a_details": {},  # 用户输入语义解析详情
            "task_b_details": {},  # Reddit搜索详情（关键词搜索、Embedding重排、LLM精排）
            "task_c_details": {},  # 评论者提取详情
            "task_d_details": {},  # 图谱构建详情
            "task_e_details": {},  # 用户图谱构建详情
            "task_f_details": {},  # 完整性分析详情
            "task_gh_details": {}, # 匹配分析详情
            "task_i_details": {},  # 推荐生成详情
            "final_summary": {}
        }
        self.start_time = datetime.now()
        
    def set_session_info(self, user_prompt: str, session_id: str):
        """设置会话基本信息"""
        self.results["session_info"] = {
            "user_prompt": user_prompt,
            "session_id": session_id,
            "start_time": self.start_time.isoformat(),
            "test_mode": "step_by_step_debug"
        }
    
    def capture_task_a(self, parsed_query: Any):
        """捕获子任务A的详细结果"""
        self.results["task_a_details"] = {
            "task_name": "用户输入语义解析",
            "execution_time": datetime.now().isoformat(),
            "search_keywords": getattr(parsed_query, 'search_keywords', []),
            "topics": getattr(parsed_query, 'topics', []),
            "emotional_state": getattr(parsed_query, 'emotional_state', {}),
            "values_info": getattr(parsed_query, 'values_info', {}),
            "confidence": getattr(parsed_query, 'confidence', 0.0),
            "core_concerns": getattr(parsed_query, 'core_concerns', []),
            "decision_points": getattr(parsed_query, 'decision_points', []),
            "life_domains": getattr(parsed_query, 'life_domains', []),
            "support_needs": getattr(parsed_query, 'support_needs', []),
            "original_text": getattr(parsed_query, 'original_text', '')
        }
    
    def capture_task_b(self, relevant_posts: List[Dict[str, Any]], search_keywords: List[str]):
        """捕获子任务B的详细结果（多阶段搜索过程）"""
        # 统计每个阶段的结果
        initial_posts = [p for p in relevant_posts if not p.get('embedding_similarity')]
        embedding_posts = [p for p in relevant_posts if p.get('embedding_similarity')]
        llm_ranked_posts = [p for p in relevant_posts if p.get('llm_rank_score')]
        
        self.results["task_b_details"] = {
            "task_name": "Reddit搜索与候选人提取（多阶段）",
            "execution_time": datetime.now().isoformat(),
            "search_keywords": search_keywords,
            "total_posts_found": len(relevant_posts),
            
            # 第一阶段：关键词搜索
            "stage_1_keyword_search": {
                "description": "增强关键词搜索",
                "posts_found": len(initial_posts) if initial_posts else len(relevant_posts),
                "sample_posts": [
                    {
                        "title": p.get('title', '')[:100],
                        "subreddit": p.get('subreddit', ''),
                        "score": p.get('score', 0),
                        "quality_score": p.get('quality_score', 0)
                    } for p in relevant_posts[:5]  # 显示前5个帖子作为样本
                ]
            },
            
            # 第二阶段：Embedding重排
            "stage_2_embedding_rerank": {
                "description": "Embedding语义重排序",
                "posts_after_rerank": len(embedding_posts) if embedding_posts else 0,
                "embedding_similarities": [
                    {
                        "title": p.get('title', '')[:50],
                        "similarity": p.get('embedding_similarity', 0),
                        "rank": i+1
                    } for i, p in enumerate(embedding_posts[:10])  # 显示前10个重排结果
                ]
            },
            
            # 第三阶段：LLM精排
            "stage_3_llm_final_rank": {
                "description": "LLM最终精排",
                "posts_after_llm_rank": len(llm_ranked_posts) if llm_ranked_posts else 0,
                "llm_ranked_results": [
                    {
                        "title": p.get('title', '')[:50],
                        "llm_rank_score": p.get('llm_rank_score', 0),
                        "rank": i+1,
                        "relevance_reason": p.get('relevance_reason', '')[:100]
                    } for i, p in enumerate(llm_ranked_posts[:10])  # 显示前10个LLM排序结果
                ]
            },
            
            # 最终选中的帖子详情
            "final_selected_posts": [
                {
                    "id": p.get('id', ''),
                    "title": p.get('title', ''),
                    "text": p.get('text', '')[:200],  # 前200字符
                    "subreddit": p.get('subreddit', ''),
                    "score": p.get('score', 0),
                    "quality_score": p.get('quality_score', 0),
                    "embedding_similarity": p.get('embedding_similarity', None),
                    "llm_rank_score": p.get('llm_rank_score', None),
                    "final_rank": i+1
                } for i, p in enumerate(relevant_posts)
            ],
            
            # 质量统计
            "quality_stats": {
                "avg_quality_score": sum(p.get('quality_score', 0) for p in relevant_posts) / len(relevant_posts) if relevant_posts else 0,
                "avg_embedding_similarity": sum(p.get('embedding_similarity', 0) for p in relevant_posts if p.get('embedding_similarity')) / len([p for p in relevant_posts if p.get('embedding_similarity')]) if any(p.get('embedding_similarity') for p in relevant_posts) else 0,
                "subreddit_distribution": self._get_subreddit_distribution(relevant_posts)
            }
        }
    
    def capture_task_c(self, candidate_users: List[str], posts: List[Dict[str, Any]]):
        """捕获子任务C的详细结果"""
        self.results["task_c_details"] = {
            "task_name": "提取评论者列表",
            "execution_time": datetime.now().isoformat(),
            "source_posts_count": len(posts),
            "candidate_users_found": len(candidate_users),
            "candidate_users": candidate_users,
            "extraction_stats": {
                "posts_with_comments": len([p for p in posts if p.get('comments_count', 0) > 0]),
                "avg_comments_per_post": sum(p.get('comments_count', 0) for p in posts) / len(posts) if posts else 0
            }
        }
    
    def capture_task_d(self, candidate_graphs: List[Dict[str, Any]]):
        """捕获子任务D的详细结果"""
        self.results["task_d_details"] = {
            "task_name": "Redditor语料提取与图谱构建",
            "execution_time": datetime.now().isoformat(),
            "total_candidates": len(candidate_graphs),
            "successful_graphs": len([g for g in candidate_graphs if g.get('graph')]),
            "graph_construction_details": [
                {
                    "user_id": g.get('user_id', ''),
                    "success": bool(g.get('graph')),
                    "posts_collected": g.get('posts_count', 0),
                    "comments_collected": g.get('comments_count', 0),
                    "graph_nodes": len(g.get('graph', {}).get('nodes', [])) if g.get('graph') else 0,
                    "graph_edges": len(g.get('graph', {}).get('edges', [])) if g.get('graph') else 0,
                    "construction_time": g.get('construction_time', 0),
                    "error": g.get('error', None)
                } for g in candidate_graphs
            ]
        }
    
    def capture_task_e(self, user_graph: Any):
        """捕获子任务E的详细结果"""
        self.results["task_e_details"] = {
            "task_name": "用户三观图谱构建",
            "execution_time": datetime.now().isoformat(),
            "graph_constructed": bool(user_graph),
            "user_graph_details": {
                "nodes_count": len(getattr(user_graph, 'nodes', [])) if user_graph else 0,
                "edges_count": len(getattr(user_graph, 'edges', [])) if user_graph else 0,
                "confidence": getattr(user_graph, 'confidence', 0.0) if user_graph else 0.0,
                "completeness": getattr(user_graph, 'completeness', 0.0) if user_graph else 0.0
            }
        }
    
    def capture_task_f(self, completeness_analysis: Dict[str, Any]):
        """捕获子任务F的详细结果"""
        self.results["task_f_details"] = {
            "task_name": "图谱完整性分析与追问",
            "execution_time": datetime.now().isoformat(),
            "completeness_analysis": completeness_analysis
        }
    
    def capture_task_gh(self, matching_results: List[Dict[str, Any]]):
        """捕获子任务G&H的详细结果"""
        self.results["task_gh_details"] = {
            "task_name": "匹配评估与LLM分析",
            "execution_time": datetime.now().isoformat(),
            "total_matches": len(matching_results),
            "matching_details": [
                {
                    "candidate_id": result.get('candidate_id', ''),
                    "resonance_score": result.get('resonance_score', 0),
                    "match_reasons": result.get('match_reasons', []),
                    "conversation_potential": result.get('conversation_potential', ''),
                    "compatibility_analysis": result.get('compatibility_analysis', {}),
                    "graph_similarity": result.get('graph_similarity', 0)
                } for result in matching_results
            ],
            "score_distribution": {
                "high_quality_matches": len([r for r in matching_results if r.get('resonance_score', 0) >= 0.8]),
                "medium_quality_matches": len([r for r in matching_results if 0.6 <= r.get('resonance_score', 0) < 0.8]),
                "low_quality_matches": len([r for r in matching_results if r.get('resonance_score', 0) < 0.6]),
                "avg_score": sum(r.get('resonance_score', 0) for r in matching_results) / len(matching_results) if matching_results else 0
            }
        }
    
    def capture_task_i(self, final_recommendations: List[Dict[str, Any]]):
        """捕获子任务I的详细结果"""
        self.results["task_i_details"] = {
            "task_name": "生成推荐展示",
            "execution_time": datetime.now().isoformat(),
            "recommendations_count": len(final_recommendations),
            "recommendations": [
                {
                    "candidate_id": rec.get('candidate_id', ''),
                    "resonance_score": rec.get('resonance_score', 0),
                    "recommendation_summary": rec.get('summary', ''),
                    "conversation_starters": rec.get('conversation_starters', []),
                    "match_strengths": rec.get('match_strengths', []),
                    "potential_topics": rec.get('potential_topics', [])
                } for rec in final_recommendations
            ]
        }
    
    def finalize_results(self, final_result: Dict[str, Any]):
        """完成结果收集，添加最终摘要"""
        end_time = datetime.now()
        total_time = (end_time - self.start_time).total_seconds()
        
        self.results["final_summary"] = {
            "success": final_result.get('success', False),
            "total_execution_time": total_time,
            "end_time": end_time.isoformat(),
            "final_stats": final_result.get('stats', {}),
            "error": final_result.get('error', None)
        }
    
    def save_to_file(self, base_filename: str = None) -> str:
        """保存详细结果到JSON文件"""
        if not base_filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"detailed_results_{timestamp}"
        
        # 确保cursor_test目录存在
        results_dir = os.path.join(os.path.dirname(__file__), "detailed_results")
        os.makedirs(results_dir, exist_ok=True)
        
        filename = os.path.join(results_dir, f"{base_filename}.json")
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 详细结果已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
            return ""
    
    def _get_subreddit_distribution(self, posts: List[Dict[str, Any]]) -> Dict[str, int]:
        """获取subreddit分布统计"""
        distribution = {}
        for post in posts:
            subreddit = post.get('subreddit', 'unknown')
            distribution[subreddit] = distribution.get(subreddit, 0) + 1
        return distribution

def display_menu():
    """显示主菜单"""
    print("🔧 Reddit共鸣推荐系统 - 步进调试测试（增强版）")
    print("=" * 70)
    print("请选择执行模式：")
    print("1. 完整流程执行（包含缓存子任务ABC + 详细结果捕获）")
    print("2. 🚀 快速模式执行（大幅减少数据量，1-2分钟完成）")
    print("3. 🎯 跳入指定子任务执行（可选择任意起始点）")
    print("4. 查看已有缓存")
    print("5. 使用指定缓存直接跳转到子任务D（+ 详细结果捕获）")
    print("6. 🏃‍♂️ 使用缓存快速跳转（快速模式）")
    print("7. 查看已保存的详细执行结果")
    print("8. 清理所有缓存")
    print("9. 退出")
    print("-" * 70)
    print("💡 快速模式说明：搜索15个帖子(原100)，5个候选用户(原30)，20条用户历史(原100)")
    print("🎯 子任务跳转：可从任意子任务开始，自动处理依赖关系")
    print("-" * 70)

def display_subtask_menu():
    """显示子任务选择菜单"""
    print("\n🎯 子任务选择器")
    print("=" * 60)
    print("请选择要执行的子任务（将从该任务开始执行到完成）：")
    print()
    print("A. 用户输入语义解析（无依赖）")
    print("B. Reddit搜索与候选人提取（依赖：A）")
    print("C. 提取评论者列表（依赖：A,B）")
    print("D. Redditor语料提取与图谱构建（依赖：A,B,C）")
    print("E. 用户三观图谱构建（依赖：用户输入）")
    print("F. 图谱完整性分析与追问（依赖：D,E）")
    print("G. 匹配评估与LLM分析（依赖：D,E,F）")
    print("H. 生成推荐展示（依赖：G）")
    print()
    print("⚡ 特殊选项：")
    print("FA. 快速模式 - 从A开始（减少数据量）")
    print("FD. 快速模式 - 从D开始（需要缓存）")
    print()
    print("0. 返回主菜单")
    print("-" * 60)
    print("💡 说明：选择任务后将自动处理依赖关系，缺失的前置任务将使用模拟数据或提示用户")

async def show_cache_list(pipeline):
    """显示已有缓存列表"""
    print("\n💾 查看已有缓存...")
    try:
        cached_sessions = await pipeline.list_cached_sessions()
        if not cached_sessions:
            print("❌ 没有找到已缓存的ABC结果")
            return None
        
        print(f"📋 找到 {len(cached_sessions)} 个缓存:")
        print("-" * 50)
        for i, session in enumerate(cached_sessions, 1):
            cache_key = session.get('cache_key', 'unknown')
            timestamp = session.get('timestamp', 'unknown')
            user_prompt = session.get('user_prompt', 'N/A')[:50] + "..." if len(session.get('user_prompt', '')) > 50 else session.get('user_prompt', 'N/A')
            posts_count = session.get('posts_count', 0)
            users_count = session.get('users_count', 0)
            
            print(f"{i}. 缓存键: {cache_key}")
            print(f"   时间: {timestamp}")
            print(f"   查询: {user_prompt}")
            print(f"   数据: {posts_count}个帖子, {users_count}个用户")
            print("-" * 30)
        
        return cached_sessions
    except Exception as e:
        print(f"❌ 获取缓存列表失败: {e}")
        return None

async def run_full_pipeline_with_detailed_capture(test_prompt: str):
    """运行完整流水线并捕获详细结果"""
    print(f"📝 测试输入: {test_prompt}")
    print()
    
    # 初始化详细结果捕获器
    results_capture = DetailedResultsCapture()
    
    try:
        # 初始化流水线
        print("🚀 初始化流水线...")
        pipeline = RedditResonancePipeline()
        print("✅ 流水线初始化完成")
        print()
        
        print("🎯 开始执行完整流水线（增强版 - 捕获详细结果）...")
        print("提示：输入 Y 继续执行该步骤，其他任何键跳过")
        print("-" * 60)
        
        # 设置会话信息
        session_id = f"debug_session_{int(datetime.now().timestamp())}"
        results_capture.set_session_info(test_prompt, session_id)
        
        # 执行完整流水线并启用缓存
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            additional_contents=None,
            max_recommendations=3,
            use_cache=True  # 启用缓存
        )
        
        # 捕获pipeline_results中的详细数据
        if result.get("success") and result.get('pipeline_results'):
            pipeline_results = result['pipeline_results']
            
            # 捕获子任务A的结果
            if 'parsed_query' in pipeline_results:
                results_capture.capture_task_a(pipeline_results['parsed_query'])
                print("✅ 已捕获子任务A详细结果")
            
            # 捕获子任务B的结果
            if 'relevant_posts' in pipeline_results:
                search_keywords = pipeline_results['parsed_query'].get_search_keywords() if pipeline_results.get('parsed_query') else []
                results_capture.capture_task_b(pipeline_results['relevant_posts'], search_keywords)
                print("✅ 已捕获子任务B详细结果（多阶段搜索过程）")
            
            # 捕获子任务C的结果
            if 'candidate_users' in pipeline_results:
                posts = pipeline_results.get('relevant_posts', [])
                results_capture.capture_task_c(pipeline_results['candidate_users'], posts)
                print("✅ 已捕获子任务C详细结果")
            
            # 捕获子任务D的结果
            if 'candidate_graphs' in pipeline_results:
                results_capture.capture_task_d(pipeline_results['candidate_graphs'])
                print("✅ 已捕获子任务D详细结果")
            
            # 捕获子任务E的结果
            if 'user_graph' in pipeline_results:
                results_capture.capture_task_e(pipeline_results['user_graph'])
                print("✅ 已捕获子任务E详细结果")
            
            # 捕获子任务F的结果
            if 'completeness_analysis' in pipeline_results:
                results_capture.capture_task_f(pipeline_results['completeness_analysis'])
                print("✅ 已捕获子任务F详细结果")
            
            # 捕获子任务G&H的结果
            if 'matching_results' in pipeline_results:
                results_capture.capture_task_gh(pipeline_results['matching_results'])
                print("✅ 已捕获子任务G&H详细结果")
            
            # 捕获子任务I的结果
            if 'final_recommendations' in pipeline_results:
                results_capture.capture_task_i(pipeline_results['final_recommendations'])
                print("✅ 已捕获子任务I详细结果")
        
        # 完成结果收集
        results_capture.finalize_results(result)
        
        # 保存详细结果到文件
        print("\n💾 保存详细执行结果...")
        saved_filename = results_capture.save_to_file()
        
        print()
        print("=" * 60)
        print("📊 执行结果摘要:")
        
        if result.get("success"):
            print("✅ 流水线执行成功")
            print(f"⏱️  总耗时: {result.get('processing_time', 0):.2f}秒")
            print(f"📈 推荐数量: {len(result.get('recommendations', []))}")
            
            # 显示缓存信息
            pipeline_results = result.get('pipeline_results', {})
            cache_key = pipeline_results.get('cache_key')
            if cache_key:
                print(f"💾 ABC结果已缓存，键: {cache_key}")
                print("   下次可以使用此键直接跳转到子任务D")
            
            # 显示统计信息
            stats = result.get('stats', {})
            print(f"📋 找到相关帖子: {stats.get('relevant_posts_found', 0)}")
            print(f"👥 候选用户数: {stats.get('candidate_users_found', 0)}")
            print(f"🔗 成功构建图谱: {stats.get('successful_graphs', 0)}")
            
            # 显示推荐用户
            recommendations = result.get('recommendations', [])
            if recommendations:
                print("\n🎯 推荐用户:")
                for i, rec in enumerate(recommendations[:3], 1):
                    print(f"  {i}. {rec.get('candidate_id', 'unknown')} (分数: {rec.get('resonance_score', 0):.3f})")
            
            # 显示详细结果文件信息
            if saved_filename:
                print(f"\n📁 详细执行结果已保存: {os.path.basename(saved_filename)}")
                print("   包含每个子任务的详细过程，便于审查和优化")
        else:
            print("❌ 流水线执行失败")
            print(f"错误信息: {result.get('error', '未知错误')}")
        
        # 清理
        await pipeline.close()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()

async def run_full_pipeline_with_cache(test_prompt: str):
    """运行完整流水线并缓存ABC结果（保持原有功能）"""
    return await run_full_pipeline_with_detailed_capture(test_prompt)

async def run_fast_pipeline_with_detailed_capture(test_prompt: str):
    """运行快速模式完整流水线并捕获详细结果"""
    print(f"📝 测试输入: {test_prompt}")
    print()
    
    # 应用快速模式配置
    FastModeConfig.apply_fast_config()
    
    # 初始化详细结果捕获器
    results_capture = DetailedResultsCapture()
    
    try:
        # 初始化流水线
        print("🚀 初始化流水线...")
        pipeline = RedditResonancePipeline()
        print("✅ 流水线初始化完成")
        print()
        
        print("⚡ 开始执行快速模式完整流水线（捕获详细结果）...")
        print("提示：输入 Y 继续执行该步骤，其他任何键跳过")
        print("-" * 60)
        
        # 设置会话信息
        session_id = f"fast_debug_session_{int(datetime.now().timestamp())}"
        results_capture.set_session_info(test_prompt, session_id)
        
        # 执行完整流水线并启用缓存
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            additional_contents=None,
            max_recommendations=2,  # 快速模式减少推荐数量
            use_cache=True  # 启用缓存
        )
        
        # 捕获pipeline_results中的详细数据（与原版本相同）
        if result.get("success") and result.get('pipeline_results'):
            pipeline_results = result['pipeline_results']
            
            # 捕获各子任务的结果
            if 'parsed_query' in pipeline_results:
                results_capture.capture_task_a(pipeline_results['parsed_query'])
                print("✅ 已捕获子任务A详细结果")
            
            if 'relevant_posts' in pipeline_results:
                search_keywords = pipeline_results['parsed_query'].get_search_keywords() if pipeline_results.get('parsed_query') else []
                results_capture.capture_task_b(pipeline_results['relevant_posts'], search_keywords)
                print("✅ 已捕获子任务B详细结果（多阶段搜索过程）")
            
            if 'candidate_users' in pipeline_results:
                posts = pipeline_results.get('relevant_posts', [])
                results_capture.capture_task_c(pipeline_results['candidate_users'], posts)
                print("✅ 已捕获子任务C详细结果")
            
            if 'candidate_graphs' in pipeline_results:
                results_capture.capture_task_d(pipeline_results['candidate_graphs'])
                print("✅ 已捕获子任务D详细结果")
            
            if 'user_graph' in pipeline_results:
                results_capture.capture_task_e(pipeline_results['user_graph'])
                print("✅ 已捕获子任务E详细结果")
            
            if 'completeness_analysis' in pipeline_results:
                results_capture.capture_task_f(pipeline_results['completeness_analysis'])
                print("✅ 已捕获子任务F详细结果")
            
            if 'matching_results' in pipeline_results:
                results_capture.capture_task_gh(pipeline_results['matching_results'])
                print("✅ 已捕获子任务G&H详细结果")
            
            if 'final_recommendations' in pipeline_results:
                results_capture.capture_task_i(pipeline_results['final_recommendations'])
                print("✅ 已捕获子任务I详细结果")
        
        # 完成结果收集
        results_capture.finalize_results(result)
        
        # 保存详细结果到文件
        print("\n💾 保存详细执行结果...")
        saved_filename = results_capture.save_to_file("fast_mode_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
        
        print()
        print("=" * 60)
        print("📊 快速模式执行结果摘要:")
        
        if result.get("success"):
            print("✅ 快速模式流水线执行成功")
            print(f"⏱️  总耗时: {result.get('processing_time', 0):.2f}秒")
            print(f"📈 推荐数量: {len(result.get('recommendations', []))}")
            
            # 显示缓存信息
            pipeline_results = result.get('pipeline_results', {})
            cache_key = pipeline_results.get('cache_key')
            if cache_key:
                print(f"💾 ABC结果已缓存，键: {cache_key}")
                print("   下次可以使用此键直接跳转到子任务D")
            
            # 显示统计信息
            stats = result.get('stats', {})
            print(f"📋 找到相关帖子: {stats.get('relevant_posts_found', 0)}")
            print(f"👥 候选用户数: {stats.get('candidate_users_found', 0)}")
            print(f"🔗 成功构建图谱: {stats.get('successful_graphs', 0)}")
            
            # 显示推荐用户
            recommendations = result.get('recommendations', [])
            if recommendations:
                print("\n🎯 推荐用户:")
                for i, rec in enumerate(recommendations[:2], 1):  # 快速模式只显示前2个
                    print(f"  {i}. {rec.get('candidate_id', 'unknown')} (分数: {rec.get('resonance_score', 0):.3f})")
            
            # 显示详细结果文件信息
            if saved_filename:
                print(f"\n📁 详细执行结果已保存: {os.path.basename(saved_filename)}")
                print("   🚀 快速模式标记，包含优化后的执行过程")
        else:
            print("❌ 快速模式流水线执行失败")
            print(f"错误信息: {result.get('error', '未知错误')}")
        
        # 清理
        await pipeline.close()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
    finally:
        # 恢复原始配置
        FastModeConfig.restore_original_config()

async def run_fast_from_cache(cache_key: str, test_prompt: str):
    """使用指定缓存快速跳转到子任务D（快速模式）"""
    print(f"🚀 使用缓存键 {cache_key} 快速跳转到子任务D...")
    print(f"📝 测试查询: {test_prompt}")
    print()
    
    # 应用快速模式配置
    FastModeConfig.apply_fast_config()
    
    # 初始化详细结果捕获器
    results_capture = DetailedResultsCapture()
    
    try:
        # 初始化流水线
        print("🚀 初始化流水线...")
        pipeline = RedditResonancePipeline()
        print("✅ 流水线初始化完成")
        print()
        
        print("⚡ 快速跳转模式：从子任务D开始执行（快速模式 - 捕获详细结果）...")
        print("提示：输入 Y 继续执行该步骤，其他任何键跳过")
        print("-" * 60)
        
        # 设置会话信息
        session_id = f"fast_cache_jump_session_{int(datetime.now().timestamp())}"
        results_capture.set_session_info(test_prompt, session_id)
        results_capture.results["session_info"]["cache_mode"] = True
        results_capture.results["session_info"]["cache_key"] = cache_key
        results_capture.results["session_info"]["fast_mode"] = True
        
        # 使用缓存直接跳转执行
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            additional_contents=None,
            max_recommendations=2,  # 快速模式
            cache_key=cache_key,
            use_cache=True
        )
        
        # 捕获结果（主要是D之后的任务）
        if result.get("success") and result.get('pipeline_results'):
            pipeline_results = result['pipeline_results']
            
            # 从缓存跳转主要捕获D之后的任务
            if 'candidate_graphs' in pipeline_results:
                results_capture.capture_task_d(pipeline_results['candidate_graphs'])
                print("✅ 已捕获子任务D详细结果")
            
            if 'user_graph' in pipeline_results:
                results_capture.capture_task_e(pipeline_results['user_graph'])
                print("✅ 已捕获子任务E详细结果")
            
            if 'completeness_analysis' in pipeline_results:
                results_capture.capture_task_f(pipeline_results['completeness_analysis'])
                print("✅ 已捕获子任务F详细结果")
            
            if 'matching_results' in pipeline_results:
                results_capture.capture_task_gh(pipeline_results['matching_results'])
                print("✅ 已捕获子任务G&H详细结果")
            
            if 'final_recommendations' in pipeline_results:
                results_capture.capture_task_i(pipeline_results['final_recommendations'])
                print("✅ 已捕获子任务I详细结果")
        
        # 完成结果收集
        results_capture.finalize_results(result)
        
        # 保存详细结果
        saved_filename = results_capture.save_to_file("fast_cache_jump_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
        
        print()
        print("=" * 60)
        print("📊 快速缓存跳转执行结果:")
        
        if result.get("success"):
            print("✅ 快速缓存跳转执行成功")
            print(f"⏱️  跳转耗时: {result.get('processing_time', 0):.2f}秒")
            print(f"📈 推荐数量: {len(result.get('recommendations', []))}")
            
            stats = result.get('stats', {})
            final_stats = result.get('final_stats', {})
            print(f"🔗 成功构建图谱: {final_stats.get('successful_graphs', stats.get('successful_graphs', 0))}")
            
            recommendations = result.get('recommendations', [])
            if recommendations:
                print("\n🎯 推荐用户:")
                for i, rec in enumerate(recommendations[:2], 1):
                    print(f"  {i}. {rec.get('candidate_id', 'unknown')} (分数: {rec.get('resonance_score', 0):.3f})")
            
            if saved_filename:
                print(f"\n📁 快速跳转结果已保存: {os.path.basename(saved_filename)}")
        else:
            print("❌ 快速缓存跳转执行失败")
            print(f"错误: {result.get('error', '未知错误')}")
        
        await pipeline.close()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复原始配置
        FastModeConfig.restore_original_config()

# ============================================================================
# 子任务执行器
# ============================================================================

class SubtaskExecutor:
    """子任务执行器 - 支持从任意子任务开始执行"""
    
    def __init__(self, pipeline: 'RedditResonancePipeline', fast_mode: bool = False):
        self.pipeline = pipeline
        self.fast_mode = fast_mode
        self.mock_data = {}
        self.execution_results = {}
        
        if fast_mode:
            FastModeConfig.apply_fast_config()
    
    def create_mock_data(self):
        """创建模拟数据以支持跳转执行"""
        from resona.models.user_models import ParsedQuery
        
        # 模拟子任务A的结果
        self.mock_data['parsed_query'] = ParsedQuery(
            original_text="模拟用户输入：关于职业发展的困扰",
            topics=["career", "professional_development"],
            emotional_state={"uncertainty": 0.8, "concern": 0.7},
            values_info={"achievement": 0.6, "stability": 0.8},
            confidence=0.75,
            search_keywords=["career", "job", "professional", "development", "uncertainty"]
        )
        
        # 模拟子任务B的结果 
        self.mock_data['relevant_posts'] = [
            {
                "id": f"mock_post_{i}",
                "title": f"模拟帖子{i}：关于职业发展的讨论",
                "text": f"这是第{i}个模拟帖子的内容，讨论职业发展相关话题...",
                "subreddit": "careerguidance" if i % 2 == 0 else "jobs",
                "score": 10 + i,
                "url": f"https://reddit.com/mock_post_{i}",
                "quality_score": 0.7 + (i * 0.05),
                "is_mock": True
            }
            for i in range(5 if self.fast_mode else 10)
        ]
        
        # 模拟子任务C的结果
        self.mock_data['candidate_users'] = [
            f"mock_user_{i}" for i in range(3 if self.fast_mode else 5)
        ]
    
    async def execute_from_subtask(self, start_task: str, user_prompt: str = None):
        """从指定子任务开始执行"""
        if not user_prompt:
            user_prompt = "我对职业发展很困惑，不知道应该如何规划未来的道路。"
        
        print(f"🎯 从子任务{start_task.upper()}开始执行")
        print(f"📝 用户输入: {user_prompt}")
        print()
        
        results_capture = DetailedResultsCapture()
        session_id = f"subtask_{start_task}_{int(datetime.now().timestamp())}"
        results_capture.set_session_info(user_prompt, session_id)
        
        start_time = datetime.now()
        
        try:
            # 根据起始任务执行相应的流程
            if start_task.upper() == 'A':
                return await self._execute_from_a(user_prompt, results_capture, start_time)
            elif start_task.upper() == 'B':
                return await self._execute_from_b(user_prompt, results_capture, start_time)
            elif start_task.upper() == 'C':
                return await self._execute_from_c(user_prompt, results_capture, start_time)
            elif start_task.upper() == 'D':
                return await self._execute_from_d(user_prompt, results_capture, start_time)
            elif start_task.upper() == 'E':
                return await self._execute_from_e(user_prompt, results_capture, start_time)
            elif start_task.upper() == 'F':
                return await self._execute_from_f(user_prompt, results_capture, start_time)
            elif start_task.upper() == 'G':
                return await self._execute_from_g(user_prompt, results_capture, start_time)
            elif start_task.upper() == 'H':
                return await self._execute_from_h(user_prompt, results_capture, start_time)
            else:
                print(f"❌ 不支持的子任务: {start_task}")
                return False
                
        except Exception as e:
            print(f"❌ 子任务{start_task.upper()}执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            if self.fast_mode:
                FastModeConfig.restore_original_config()
    
    async def _execute_from_a(self, user_prompt: str, results_capture, start_time):
        """从子任务A开始执行完整流程"""
        print("🚀 执行子任务A：用户输入语义解析")
        
        # 执行子任务A
        parsed_query = await self.pipeline._task_a_parse_user_input(user_prompt)
        self.execution_results['parsed_query'] = parsed_query
        results_capture.capture_task_a(parsed_query)
        print("✅ 子任务A完成")
        
        # 继续执行后续任务
        return await self._continue_from_b(user_prompt, results_capture, start_time)
    
    async def _execute_from_b(self, user_prompt: str, results_capture, start_time):
        """从子任务B开始执行"""
        print("🚀 执行子任务B：Reddit搜索与候选人提取")
        
        # 检查依赖
        if 'parsed_query' not in self.execution_results:
            print("⚠️  缺少子任务A的结果，使用模拟数据")
            self.create_mock_data()
            self.execution_results['parsed_query'] = self.mock_data['parsed_query']
            results_capture.capture_task_a(self.mock_data['parsed_query'])
        
        # 执行子任务B
        search_keywords = self.execution_results['parsed_query'].get_search_keywords()
        relevant_posts = await self.pipeline._task_b_reddit_search(search_keywords, user_prompt)
        self.execution_results['relevant_posts'] = relevant_posts
        results_capture.capture_task_b(relevant_posts, search_keywords)
        print("✅ 子任务B完成")
        
        return await self._continue_from_c(user_prompt, results_capture, start_time)
    
    async def _continue_from_b(self, user_prompt: str, results_capture, start_time):
        """从B任务开始继续执行"""
        return await self._execute_from_b(user_prompt, results_capture, start_time)
    
    async def _execute_from_c(self, user_prompt: str, results_capture, start_time):
        """从子任务C开始执行"""
        print("🚀 执行子任务C：提取评论者列表")
        
        # 检查依赖
        await self._ensure_dependencies(['parsed_query', 'relevant_posts'], results_capture)
        
        # 执行子任务C
        candidate_users = await self.pipeline._task_c_extract_commenters(self.execution_results['relevant_posts'])
        self.execution_results['candidate_users'] = candidate_users
        results_capture.capture_task_c(candidate_users, self.execution_results['relevant_posts'])
        print("✅ 子任务C完成")
        
        return await self._continue_from_d(user_prompt, results_capture, start_time)
    
    async def _continue_from_c(self, user_prompt: str, results_capture, start_time):
        """从C任务开始继续执行"""
        return await self._execute_from_c(user_prompt, results_capture, start_time)
    
    async def _execute_from_d(self, user_prompt: str, results_capture, start_time):
        """从子任务D开始执行"""
        print("🚀 执行子任务D：Redditor语料提取与图谱构建")
        
        # 检查依赖
        await self._ensure_dependencies(['parsed_query', 'relevant_posts', 'candidate_users'], results_capture)
        
        # 执行子任务D
        candidate_graphs = await self.pipeline._task_d_build_candidate_graphs_optimized(self.execution_results['candidate_users'])
        self.execution_results['candidate_graphs'] = candidate_graphs
        results_capture.capture_task_d(candidate_graphs)
        print("✅ 子任务D完成")
        
        return await self._continue_from_e(user_prompt, results_capture, start_time)
    
    async def _continue_from_d(self, user_prompt: str, results_capture, start_time):
        """从D任务开始继续执行"""
        return await self._execute_from_d(user_prompt, results_capture, start_time)
    
    async def _execute_from_e(self, user_prompt: str, results_capture, start_time):
        """从子任务E开始执行"""
        print("🚀 执行子任务E：用户三观图谱构建")
        
        # 子任务E只依赖用户输入
        user_graph = await self.pipeline._task_e_build_user_graph(user_prompt)
        self.execution_results['user_graph'] = user_graph
        results_capture.capture_task_e(user_graph)
        print("✅ 子任务E完成")
        
        return await self._continue_from_f(user_prompt, results_capture, start_time)
    
    async def _continue_from_e(self, user_prompt: str, results_capture, start_time):
        """从E任务开始继续执行"""
        return await self._execute_from_e(user_prompt, results_capture, start_time)
    
    async def _execute_from_f(self, user_prompt: str, results_capture, start_time):
        """从子任务F开始执行"""
        print("🚀 执行子任务F：图谱完整性分析与追问")
        
        # 检查依赖
        await self._ensure_dependencies(['candidate_graphs', 'user_graph'], results_capture, include_mock_user_graph=True)
        
        # 执行子任务F
        completeness_analysis = await self.pipeline._task_f_analyze_completeness(
            self.execution_results['user_graph'], 
            self.execution_results['candidate_graphs']
        )
        self.execution_results['completeness_analysis'] = completeness_analysis
        results_capture.capture_task_f(completeness_analysis)
        print("✅ 子任务F完成")
        
        return await self._continue_from_g(user_prompt, results_capture, start_time)
    
    async def _continue_from_f(self, user_prompt: str, results_capture, start_time):
        """从F任务开始继续执行"""
        return await self._execute_from_f(user_prompt, results_capture, start_time)
    
    async def _execute_from_g(self, user_prompt: str, results_capture, start_time):
        """从子任务G开始执行"""
        print("🚀 执行子任务G：匹配评估与LLM分析")
        
        # 检查依赖
        await self._ensure_dependencies(['user_graph', 'candidate_graphs'], results_capture, include_mock_user_graph=True)
        
        # 执行子任务G
        matching_results = await self.pipeline._task_gh_match_and_analyze(
            self.execution_results['user_graph'],
            self.execution_results['candidate_graphs']
        )
        self.execution_results['matching_results'] = matching_results
        results_capture.capture_task_gh(matching_results)
        print("✅ 子任务G完成")
        
        return await self._continue_from_h(user_prompt, results_capture, start_time)
    
    async def _continue_from_g(self, user_prompt: str, results_capture, start_time):
        """从G任务开始继续执行"""
        return await self._execute_from_g(user_prompt, results_capture, start_time)
    
    async def _execute_from_h(self, user_prompt: str, results_capture, start_time):
        """从子任务H开始执行"""
        print("🚀 执行子任务H：生成推荐展示")
        
        # 检查依赖
        await self._ensure_dependencies(['matching_results'], results_capture)
        
        # 执行子任务H
        final_recommendations = await self.pipeline._task_i_generate_recommendations(
            self.execution_results['matching_results'],
            max_recommendations=2 if self.fast_mode else 3
        )
        self.execution_results['final_recommendations'] = final_recommendations
        results_capture.capture_task_i(final_recommendations)
        print("✅ 子任务H完成")
        
        # 完成整个流程
        total_time = (datetime.now() - start_time).total_seconds()
        
        final_result = {
            "success": True,
            "recommendations": final_recommendations,
            "processing_time": total_time,
            "stats": {
                "relevant_posts_found": len(self.execution_results.get('relevant_posts', [])),
                "candidate_users_found": len(self.execution_results.get('candidate_users', [])),
                "successful_graphs": len([g for g in self.execution_results.get('candidate_graphs', []) if g.get('graph')]),
                "final_recommendations": len(final_recommendations)
            }
        }
        
        results_capture.finalize_results(final_result)
        saved_filename = results_capture.save_to_file(f"subtask_execution_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        print()
        print("=" * 60)
        print("🎉 子任务执行完成！")
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"📈 最终推荐: {len(final_recommendations)}")
        
        if final_recommendations:
            print("\n🎯 推荐结果:")
            for i, rec in enumerate(final_recommendations, 1):
                print(f"  {i}. {rec.get('candidate_id', 'unknown')} (分数: {rec.get('resonance_score', 0):.3f})")
        
        if saved_filename:
            print(f"\n📁 详细结果已保存: {os.path.basename(saved_filename)}")
        
        return True
    
    async def _continue_from_h(self, user_prompt: str, results_capture, start_time):
        """从H任务开始继续执行"""
        return await self._execute_from_h(user_prompt, results_capture, start_time)
    
    async def _ensure_dependencies(self, required_keys: List[str], results_capture, include_mock_user_graph: bool = False):
        """确保所需的依赖数据存在"""
        self.create_mock_data()  # 确保模拟数据已创建
        
        for key in required_keys:
            if key not in self.execution_results:
                print(f"⚠️  缺少依赖数据: {key}，使用模拟数据")
                
                if key == 'parsed_query':
                    self.execution_results[key] = self.mock_data['parsed_query']
                    results_capture.capture_task_a(self.mock_data['parsed_query'])
                elif key == 'relevant_posts':
                    self.execution_results[key] = self.mock_data['relevant_posts']
                    results_capture.capture_task_b(self.mock_data['relevant_posts'], self.mock_data['parsed_query'].get_search_keywords())
                elif key == 'candidate_users':
                    self.execution_results[key] = self.mock_data['candidate_users']
                    results_capture.capture_task_c(self.mock_data['candidate_users'], self.mock_data['relevant_posts'])
                elif key == 'candidate_graphs':
                    # 为候选图谱创建模拟数据
                    mock_graphs = [
                        {
                            "user_id": user,
                            "graph": {
                                "user_id": user,
                                "nodes": [
                                    {"node_id": f"node_{i}", "content": f"模拟节点{i}", "node_type": "topic", "weight": 0.8}
                                    for i in range(3)
                                ],
                                "edges": [
                                    {"source_id": "node_0", "target_id": "node_1", "relation_type": "relates", "weight": 0.7}
                                ]
                            }
                        }
                        for user in self.execution_results.get('candidate_users', self.mock_data['candidate_users'])
                    ]
                    self.execution_results[key] = mock_graphs
                    results_capture.capture_task_d(mock_graphs)
                elif key == 'user_graph' and include_mock_user_graph:
                    # 创建模拟用户图谱
                    from resona.models.graph_models import UserGraph, GraphNode, GraphEdge, NodeType, RelationType
                    mock_user_graph = UserGraph(
                        user_id="mock_user",
                        nodes=[
                            GraphNode(
                                node_id="user_node_1",
                                content="模拟用户价值观节点",
                                node_type=NodeType.BELIEF,
                                weight=0.9
                            )
                        ],
                        edges=[],
                        confidence=0.7,
                        completeness=0.6
                    )
                    self.execution_results[key] = mock_user_graph
                    results_capture.capture_task_e(mock_user_graph)
                elif key == 'matching_results':
                    # 创建模拟匹配结果
                    mock_matching = [
                        {
                            "candidate_id": user,
                            "resonance_score": 0.7 + (i * 0.1),
                            "match_reasons": [f"原因{i+1}", f"原因{i+2}"],
                            "conversation_potential": "良好"
                        }
                        for i, user in enumerate(self.execution_results.get('candidate_users', self.mock_data['candidate_users']))
                    ]
                    self.execution_results[key] = mock_matching
                    results_capture.capture_task_gh(mock_matching)

async def execute_subtask_mode():
    """执行子任务模式"""
    while True:
        display_subtask_menu()
        choice = input("请选择子任务 (A-H, FA, FD, 0): ").strip().upper()
        
        if choice == '0':
            return
        
        if choice not in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'FA', 'FD']:
            print("❌ 无效选择，请重新输入")
            continue
        
        # 获取用户输入
        print(f"\n🎯 准备执行子任务{choice}")
        use_default = input("使用默认测试查询？(Y/n): ").strip().lower()
        
        if use_default in ['', 'y', 'yes']:
            user_prompt = "我坚持创业7年了，但一直都没什么成绩，每天都在学习反思，和别人聊天的时候总能意识到自己懂得很多东西，但是到自己做了又总是失败。我还应该继续坚持吗？"
        else:
            user_prompt = input("请输入您的测试查询: ").strip()
            if not user_prompt:
                print("❌ 输入不能为空")
                continue
        
        # 处理特殊选项
        fast_mode = False
        if choice == 'FA':
            choice = 'A'
            fast_mode = True
            print("⚡ 启用快速模式")
        elif choice == 'FD':
            choice = 'D'
            fast_mode = True
            print("⚡ 启用快速模式")
            
            # 对于FD，检查是否有缓存
            pipeline = RedditResonancePipeline()
            cached_sessions = await show_cache_list(pipeline)
            await pipeline.close()
            
            if not cached_sessions:
                print("❌ 没有可用缓存，无法执行快速模式从D开始")
                print("💡 建议先运行选项FA创建缓存，或选择其他子任务")
                continue
            
            try:
                cache_idx = int(input("请选择要使用的缓存编号: ").strip()) - 1
                if 0 <= cache_idx < len(cached_sessions):
                    cache_key = cached_sessions[cache_idx]['cache_key']
                    # 使用现有的缓存跳转功能
                    await run_fast_from_cache(cache_key, user_prompt)
                    continue
                else:
                    print("❌ 无效编号")
                    continue
            except ValueError:
                print("❌ 请输入有效数字")
                continue
        
        # 执行子任务
        try:
            pipeline = RedditResonancePipeline()
            executor = SubtaskExecutor(pipeline, fast_mode=fast_mode)
            
            success = await executor.execute_from_subtask(choice, user_prompt)
            
            if success:
                print(f"\n🎉 子任务{choice}执行成功！")
            else:
                print(f"\n❌ 子任务{choice}执行失败")
            
            await pipeline.close()
            
        except Exception as e:
            print(f"\n❌ 执行出错: {e}")
        
        print("\n" + "="*50)
        input("按回车键继续...")
        print()

async def run_from_cache(cache_key: str, test_prompt: str):
    """使用指定缓存直接跳转到子任务D（增强版 - 捕获详细结果）"""
    print(f"🚀 使用缓存键 {cache_key} 直接跳转到子任务D...")
    print(f"📝 测试查询: {test_prompt}")
    print()
    
    # 初始化详细结果捕获器
    results_capture = DetailedResultsCapture()
    
    try:
        # 初始化流水线
        print("🚀 初始化流水线...")
        pipeline = RedditResonancePipeline()
        print("✅ 流水线初始化完成")
        print()
        
        print("⚡ 直接跳转模式：从子任务D开始执行（增强版 - 捕获详细结果）...")
        print("提示：输入 Y 继续执行该步骤，其他任何键跳过")
        print("-" * 60)
        
        # 设置会话信息
        session_id = f"cache_jump_session_{int(datetime.now().timestamp())}"
        results_capture.set_session_info(test_prompt, session_id)
        results_capture.results["session_info"]["cache_mode"] = True
        results_capture.results["session_info"]["cache_key"] = cache_key
        
        # 使用缓存直接跳转执行
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            additional_contents=None,
            max_recommendations=3,
            use_cache=True,
            cache_key=cache_key  # 指定缓存键
        )
        
        # 捕获pipeline_results中的详细数据（从子任务D开始）
        if result.get("success") and result.get('pipeline_results'):
            pipeline_results = result['pipeline_results']
            
            # 由于是从缓存跳转，子任务ABC的数据来自缓存
            # 我们仍然可以捕获这些缓存的数据作为参考
            if 'parsed_query' in pipeline_results:
                results_capture.capture_task_a(pipeline_results['parsed_query'])
                print("✅ 已捕获子任务A详细结果（来自缓存）")
            
            if 'relevant_posts' in pipeline_results:
                search_keywords = pipeline_results['parsed_query'].get_search_keywords() if pipeline_results.get('parsed_query') else []
                results_capture.capture_task_b(pipeline_results['relevant_posts'], search_keywords)
                print("✅ 已捕获子任务B详细结果（来自缓存）")
            
            if 'candidate_users' in pipeline_results:
                posts = pipeline_results.get('relevant_posts', [])
                results_capture.capture_task_c(pipeline_results['candidate_users'], posts)
                print("✅ 已捕获子任务C详细结果（来自缓存）")
            
            # 以下是实际执行的任务，会有新的详细结果
            if 'candidate_graphs' in pipeline_results:
                results_capture.capture_task_d(pipeline_results['candidate_graphs'])
                print("✅ 已捕获子任务D详细结果（新执行）")
            
            if 'user_graph' in pipeline_results:
                results_capture.capture_task_e(pipeline_results['user_graph'])
                print("✅ 已捕获子任务E详细结果（新执行）")
            
            if 'completeness_analysis' in pipeline_results:
                results_capture.capture_task_f(pipeline_results['completeness_analysis'])
                print("✅ 已捕获子任务F详细结果（新执行）")
            
            if 'matching_results' in pipeline_results:
                results_capture.capture_task_gh(pipeline_results['matching_results'])
                print("✅ 已捕获子任务G&H详细结果（新执行）")
            
            if 'final_recommendations' in pipeline_results:
                results_capture.capture_task_i(pipeline_results['final_recommendations'])
                print("✅ 已捕获子任务I详细结果（新执行）")
        
        # 完成结果收集
        results_capture.finalize_results(result)
        
        # 保存详细结果到文件
        print("\n💾 保存详细执行结果...")
        saved_filename = results_capture.save_to_file(f"cache_jump_{cache_key[:8]}")
        
        print()
        print("=" * 60)
        print("📊 执行结果摘要:")
        
        if result.get("success"):
            print("✅ 流水线执行成功（从缓存恢复）")
            print(f"⏱️  总耗时: {result.get('processing_time', 0):.2f}秒")
            print(f"📈 推荐数量: {len(result.get('recommendations', []))}")
            
            # 显示统计信息
            stats = result.get('stats', {})
            print(f"📋 使用缓存的帖子: {stats.get('relevant_posts_found', 0)}")
            print(f"👥 使用缓存的用户: {stats.get('candidate_users_found', 0)}")
            print(f"🔗 成功构建图谱: {stats.get('successful_graphs', 0)}")
            
            # 显示推荐用户
            recommendations = result.get('recommendations', [])
            if recommendations:
                print("\n🎯 推荐用户:")
                for i, rec in enumerate(recommendations[:3], 1):
                    print(f"  {i}. {rec.get('candidate_id', 'unknown')} (分数: {rec.get('resonance_score', 0):.3f})")
            
            # 显示详细结果文件信息
            if saved_filename:
                print(f"\n📁 详细执行结果已保存: {os.path.basename(saved_filename)}")
                print("   包含缓存跳转模式下的详细过程，便于审查和优化")
        else:
            print("❌ 流水线执行失败")
            error_msg = result.get('error', '未知错误')
            if "缓存" in error_msg or "cache" in error_msg.lower():
                print("💡 可能缓存已过期或损坏，建议重新执行完整流程")
            print(f"错误信息: {error_msg}")
        
        # 清理
        await pipeline.close()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()

def view_detailed_results():
    """查看已保存的详细执行结果"""
    print("\n📁 查看已保存的详细执行结果...")
    
    # 获取详细结果目录
    results_dir = os.path.join(os.path.dirname(__file__), "detailed_results")
    
    if not os.path.exists(results_dir):
        print("❌ 详细结果目录不存在，请先执行一次完整流程")
        return
    
    # 获取所有JSON文件
    json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    
    if not json_files:
        print("❌ 没有找到详细结果文件，请先执行一次完整流程")
        return
    
    # 按修改时间排序（最新的在前）
    json_files.sort(key=lambda x: os.path.getmtime(os.path.join(results_dir, x)), reverse=True)
    
    print(f"📋 找到 {len(json_files)} 个详细结果文件:")
    print("-" * 60)
    
    for i, filename in enumerate(json_files, 1):
        filepath = os.path.join(results_dir, filename)
        file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
        file_size = os.path.getsize(filepath)
        
        print(f"{i}. {filename}")
        print(f"   时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   大小: {file_size / 1024:.1f} KB")
        
        # 尝试读取文件的基本信息
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                session_info = data.get('session_info', {})
                final_summary = data.get('final_summary', {})
                
                user_prompt = session_info.get('user_prompt', 'N/A')[:50] + "..." if len(session_info.get('user_prompt', '')) > 50 else session_info.get('user_prompt', 'N/A')
                success = final_summary.get('success', False)
                exec_time = final_summary.get('total_execution_time', 0)
                
                print(f"   查询: {user_prompt}")
                print(f"   状态: {'✅ 成功' if success else '❌ 失败'}")
                print(f"   耗时: {exec_time:.2f}秒")
                
                if session_info.get('cache_mode'):
                    print(f"   模式: 缓存跳转 (键: {session_info.get('cache_key', '')[:8]}...)")
                else:
                    print(f"   模式: 完整流程")
                    
        except Exception as e:
            print(f"   ⚠️  文件读取异常: {e}")
        
        print("-" * 40)
    
    # 让用户选择查看具体文件
    try:
        choice = input("\n请选择要查看的文件编号（直接回车返回主菜单）: ").strip()
        if not choice:
            return
        
        index = int(choice) - 1
        if 0 <= index < len(json_files):
            selected_file = json_files[index]
            view_detailed_result_file(os.path.join(results_dir, selected_file))
        else:
            print("❌ 无效的编号")
    except ValueError:
        print("❌ 请输入有效的数字")

def view_detailed_result_file(filepath: str):
    """查看具体的详细结果文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"\n📋 详细结果文件: {os.path.basename(filepath)}")
        print("=" * 70)
        
        # 显示会话信息
        session_info = data.get('session_info', {})
        print("📝 会话信息:")
        print(f"   用户查询: {session_info.get('user_prompt', 'N/A')}")
        print(f"   会话ID: {session_info.get('session_id', 'N/A')}")
        print(f"   开始时间: {session_info.get('start_time', 'N/A')}")
        print(f"   测试模式: {session_info.get('test_mode', 'N/A')}")
        if session_info.get('cache_mode'):
            print(f"   缓存模式: 是 (键: {session_info.get('cache_key', 'N/A')})")
        print()
        
        # 显示各子任务概览
        task_names = {
            'task_a_details': '子任务A: 用户输入语义解析',
            'task_b_details': '子任务B: Reddit搜索与候选人提取',
            'task_c_details': '子任务C: 提取评论者列表',
            'task_d_details': '子任务D: Redditor语料提取与图谱构建',
            'task_e_details': '子任务E: 用户三观图谱构建',
            'task_f_details': '子任务F: 图谱完整性分析与追问',
            'task_gh_details': '子任务G&H: 匹配评估与LLM分析',
            'task_i_details': '子任务I: 生成推荐展示'
        }
        
        print("🔍 子任务执行概览:")
        for task_key, task_name in task_names.items():
            task_data = data.get(task_key, {})
            if task_data:
                exec_time = task_data.get('execution_time', 'N/A')
                print(f"   ✅ {task_name}")
                print(f"      执行时间: {exec_time}")
            else:
                print(f"   ❌ {task_name} (未执行)")
        print()
        
        # 显示关键统计
        final_summary = data.get('final_summary', {})
        print("📊 执行统计:")
        print(f"   执行状态: {'✅ 成功' if final_summary.get('success', False) else '❌ 失败'}")
        print(f"   总耗时: {final_summary.get('total_execution_time', 0):.2f}秒")
        print(f"   结束时间: {final_summary.get('end_time', 'N/A')}")
        
        final_stats = final_summary.get('final_stats', {})
        if final_stats:
            print(f"   找到帖子: {final_stats.get('relevant_posts_found', 0)}")
            print(f"   候选用户: {final_stats.get('candidate_users_found', 0)}")
            print(f"   成功图谱: {final_stats.get('successful_graphs', 0)}")
            print(f"   最终推荐: {final_stats.get('final_recommendations', 0)}")
        print()
        
        # 显示子任务B的详细信息（多阶段搜索）
        task_b = data.get('task_b_details', {})
        if task_b:
            print("🔎 子任务B详细信息（多阶段搜索）:")
            print(f"   搜索关键词: {task_b.get('search_keywords', [])}")
            print(f"   总帖子数: {task_b.get('total_posts_found', 0)}")
            
            # 第一阶段
            stage1 = task_b.get('stage_1_keyword_search', {})
            if stage1:
                print(f"   阶段1 - 关键词搜索: {stage1.get('posts_found', 0)} 个帖子")
            
            # 第二阶段
            stage2 = task_b.get('stage_2_embedding_rerank', {})
            if stage2:
                print(f"   阶段2 - Embedding重排: {stage2.get('posts_after_rerank', 0)} 个帖子")
                similarities = stage2.get('embedding_similarities', [])
                if similarities:
                    print(f"   Top 3 Embedding相似度:")
                    for i, sim in enumerate(similarities[:3]):
                        print(f"     {i+1}. {sim.get('title', '')[:30]}... (相似度: {sim.get('similarity', 0):.3f})")
            
            # 第三阶段
            stage3 = task_b.get('stage_3_llm_final_rank', {})
            if stage3:
                print(f"   阶段3 - LLM精排: {stage3.get('posts_after_llm_rank', 0)} 个帖子")
                llm_results = stage3.get('llm_ranked_results', [])
                if llm_results:
                    print(f"   Top 3 LLM排序:")
                    for i, result in enumerate(llm_results[:3]):
                        print(f"     {i+1}. {result.get('title', '')[:30]}... (分数: {result.get('llm_rank_score', 0):.3f})")
            
            # 质量统计
            quality_stats = task_b.get('quality_stats', {})
            if quality_stats:
                print(f"   质量统计:")
                print(f"     平均质量分数: {quality_stats.get('avg_quality_score', 0):.3f}")
                print(f"     平均Embedding相似度: {quality_stats.get('avg_embedding_similarity', 0):.3f}")
            print()
        
        # 显示推荐结果详情
        task_i = data.get('task_i_details', {})
        if task_i and task_i.get('recommendations'):
            recommendations = task_i['recommendations']
            print(f"🎯 推荐结果详情 ({len(recommendations)} 个):")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. 用户: {rec.get('candidate_id', 'unknown')}")
                print(f"      共鸣分数: {rec.get('resonance_score', 0):.3f}")
                summary = rec.get('recommendation_summary', '')
                if summary:
                    print(f"      推荐理由: {summary[:100]}...")
            print()
        
        # 询问是否查看更多详细信息
        print("💡 提示：以上仅显示关键信息，完整的详细结果包含更多子任务的详细数据")
        print("   如需查看特定子任务的详细信息，可以直接编辑JSON文件或使用JSON查看工具")
        
    except Exception as e:
        print(f"❌ 查看文件失败: {e}")

async def clear_all_cache():
    """清理所有缓存"""
    print("\n🗑️  清理所有缓存...")
    try:
        pipeline = RedditResonancePipeline()
        # 这里需要实现清理缓存的方法
        print("⚠️  注意：目前需要手动删除 data/pipeline_cache/ 目录下的文件")
        print("或重启应用程序来清理内存缓存")
        await pipeline.close()
    except Exception as e:
        print(f"❌ 清理缓存失败: {e}")

async def main():
    """主测试函数"""
    # 测试用的输入
    test_prompt = "我坚持创业7年了，但一直都没什么成绩，每天都在学习反思，和别人聊天的时候总能意识到自己懂得很多东西，但是到自己做了又总是失败。我还应该继续坚持吗？"
    
    while True:
        try:
            display_menu()
            choice = input("请选择 (1-9): ").strip()
            
            if choice == '1':
                # 完整流程执行（增强版 - 捕获详细结果）
                await run_full_pipeline_with_cache(test_prompt)
                
            elif choice == '2':
                # 快速模式执行
                print("\n🚀 快速模式执行")
                print("数据量大幅减少，预计运行时间：1-2分钟")
                await run_fast_pipeline_with_detailed_capture(test_prompt)
                
            elif choice == '3':
                # 子任务跳转模式
                await execute_subtask_mode()
                
            elif choice == '4':
                # 查看已有缓存
                pipeline = RedditResonancePipeline()
                await show_cache_list(pipeline)
                await pipeline.close()
                
            elif choice == '5':
                # 使用指定缓存直接跳转（增强版 - 捕获详细结果）
                pipeline = RedditResonancePipeline()
                cached_sessions = await show_cache_list(pipeline)
                await pipeline.close()
                
                if cached_sessions:
                    try:
                        index = int(input("\n请选择要使用的缓存编号: ").strip()) - 1
                        if 0 <= index < len(cached_sessions):
                            cache_key = cached_sessions[index]['cache_key']
                            await run_from_cache(cache_key, test_prompt)
                        else:
                            print("❌ 无效的编号")
                    except ValueError:
                        print("❌ 请输入有效的数字")
                        
            elif choice == '6':
                # 使用缓存快速跳转（快速模式）
                print("\n🏃‍♂️ 缓存快速跳转（快速模式）")
                pipeline = RedditResonancePipeline()
                cached_sessions = await show_cache_list(pipeline)
                await pipeline.close()
                
                if cached_sessions:
                    try:
                        index = int(input("\n请选择要使用的缓存编号: ").strip()) - 1
                        if 0 <= index < len(cached_sessions):
                            cache_key = cached_sessions[index]['cache_key']
                            await run_fast_from_cache(cache_key, test_prompt)
                        else:
                            print("❌ 无效的编号")
                    except ValueError:
                        print("❌ 请输入有效的数字")
                        
            elif choice == '7':
                # 查看已保存的详细执行结果
                view_detailed_results()
                
            elif choice == '8':
                # 清理所有缓存
                await clear_all_cache()
                
            elif choice == '9':
                # 退出
                break
                
            else:
                print("❌ 无效选择，请重新输入")
            
            print("\n" + "-" * 60)
            input("按回车键继续...")
            print()
            
        except KeyboardInterrupt:
            print("\n🛑 用户中断执行")
            break
        except Exception as e:
            print(f"\n❌ 执行出错: {e}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()

if __name__ == "__main__":
    print("⚙️  Reddit共鸣推荐系统 - 步进调试测试（增强版 - 详细结果捕获）")
    print("💡 功能说明：")
    print("   - 可以执行完整流程并缓存子任务ABC的结果")
    print("   - 可以使用已有缓存直接跳转到子任务D，大幅节省时间")
    print("   - 支持查看和管理已有缓存")
    print("   🆕 新增：详细结果捕获功能")
    print("     * 自动保存每个子任务的详细执行过程")
    print("     * 记录embedding重排、LLM精排的具体结果")
    print("     * 保存为结构化JSON文件，便于审查和优化")
    print("     * 支持查看历史执行结果和详细分析")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试结束") 