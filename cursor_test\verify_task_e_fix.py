#!/usr/bin/env python3
"""
子任务E修复验证脚本 - 快速验证增强降级策略是否成功集成

验证项目：
1. 增强降级策略是否已集成到GraphBuilder
2. 图谱构建质量是否显著提升  
3. 处理速度是否明显加快
4. AI服务状态验证
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.core.graph_builder import GraphBuilder
from resona.services.ai_service import AIService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def verify_task_e_fix():
    """验证子任务E修复是否成功"""
    print("🔧 子任务E修复验证")
    print("=" * 60)
    
    test_content = """我坚持创业7年了，但一直都没什么成绩，每天都在学习反思，和别人聊天的时候总能意识到自己懂得很多东西，但实际执行的时候总是不行。现在感觉很迷茫，不知道是继续坚持还是该换个方向。看到身边很多朋友都有稳定的工作和收入，开始怀疑自己的选择是否正确。"""
    
    print(f"📝 测试内容: {test_content[:50]}...")
    print()
    
    # 验证1：AI服务状态
    print("🤖 验证1：AI服务状态")
    try:
        ai_service = AIService()
        embedding_ok = False
        llm_ok = False
        
        try:
            test_embedding = await ai_service.get_embedding("测试")
            if test_embedding and len(test_embedding) > 0:
                embedding_ok = True
                print("  ✅ Embedding服务正常")
            else:
                print("  ❌ Embedding服务异常")
        except Exception as e:
            print(f"  ❌ Embedding测试失败: {e}")
        
        try:
            test_response = await ai_service.get_completion("说'测试'", max_tokens=10)
            if test_response and len(test_response.strip()) > 0:
                llm_ok = True
                print("  ✅ LLM服务正常")
            else:
                print("  ❌ LLM服务异常")
        except Exception as e:
            print(f"  ❌ LLM测试失败: {e}")
        
        await ai_service.close()
        
        ai_available = embedding_ok and llm_ok
        print(f"  📊 AI服务整体状态: {'✅ 正常' if ai_available else '❌ 异常'}")
        
    except Exception as e:
        print(f"  ❌ AI服务初始化失败: {e}")
        ai_available = False
    
    print()
    
    # 验证2：增强降级策略测试
    print("🚀 验证2：增强降级策略测试")
    try:
        # 强制使用降级策略（不使用AI服务）
        graph_builder = GraphBuilder(ai_service=None)
        
        start_time = datetime.now()
        elements = graph_builder._fallback_analyze_content(test_content)
        end_time = datetime.now()
        
        execution_time = (end_time - start_time).total_seconds()
        nodes_count = len(elements.get_all_nodes())
        edges_count = len(elements.get_all_edges())
        
        print(f"  📊 执行结果:")
        print(f"    - 节点数量: {nodes_count}")
        print(f"    - 边数量: {edges_count}")
        print(f"    - 执行时间: {execution_time:.4f}秒")
        
        # 分析节点类型分布
        node_types = {
            "experience": len(elements.experience_nodes),
            "belief": len(elements.belief_nodes),
            "emotion": len(elements.emotion_nodes),
            "topic": len(elements.topic_nodes)
        }
        print(f"    - 节点类型分布: {node_types}")
        
        # 质量评估
        quality_score = 0
        if nodes_count >= 15:  # 期望至少15个节点
            quality_score += 25
        elif nodes_count >= 10:
            quality_score += 15
        elif nodes_count >= 5:
            quality_score += 5
        
        if edges_count >= 10:  # 期望至少10条边
            quality_score += 25
        elif edges_count >= 5:
            quality_score += 15
        elif edges_count >= 3:
            quality_score += 5
        
        if execution_time <= 0.1:  # 期望0.1秒内完成
            quality_score += 25
        elif execution_time <= 0.5:
            quality_score += 15
        elif execution_time <= 2.0:
            quality_score += 5
        
        # 检查节点类型多样性
        diverse_types = sum(1 for count in node_types.values() if count > 0)
        if diverse_types >= 4:
            quality_score += 25
        elif diverse_types >= 3:
            quality_score += 15
        elif diverse_types >= 2:
            quality_score += 5
        
        print(f"  🎯 质量评分: {quality_score}/100")
        
        if quality_score >= 80:
            print("  ✅ 增强降级策略工作优秀")
        elif quality_score >= 60:
            print("  ✅ 增强降级策略工作良好")
        elif quality_score >= 40:
            print("  ⚠️ 增强降级策略工作一般")
        else:
            print("  ❌ 增强降级策略需要优化")
        
        # 显示一些节点示例
        if elements.get_all_nodes():
            print(f"  📋 节点示例:")
            for i, node in enumerate(elements.get_all_nodes()[:3]):
                print(f"    {i+1}. [{node.node_type.value}] {node.content}")
        
    except Exception as e:
        print(f"  ❌ 增强降级策略测试失败: {e}")
        quality_score = 0
    
    print()
    
    # 验证3：完整图谱构建测试
    print("🏗️ 验证3：完整图谱构建测试")
    try:
        if ai_available:
            # 使用AI服务的完整测试
            graph_builder_with_ai = GraphBuilder()
            start_time = datetime.now()
            user_graph = await graph_builder_with_ai.build_user_graph([test_content])
            end_time = datetime.now()
            
            ai_execution_time = (end_time - start_time).total_seconds()
            ai_nodes_count = len(user_graph.nodes)
            ai_edges_count = len(user_graph.edges)
            
            print(f"  🤖 AI模式结果:")
            print(f"    - 节点数量: {ai_nodes_count}")
            print(f"    - 边数量: {ai_edges_count}")
            print(f"    - 执行时间: {ai_execution_time:.4f}秒")
            
            # 对比分析
            if ai_nodes_count > 0 and nodes_count > 0:
                node_ratio = nodes_count / ai_nodes_count
                print(f"  📊 降级策略对比AI模式:")
                print(f"    - 节点数量比: {node_ratio:.2f} ({nodes_count}/{ai_nodes_count})")
                print(f"    - 速度比: {ai_execution_time/execution_time:.1f}x 更快")
                
                if node_ratio >= 0.7:
                    print("  ✅ 降级策略质量接近AI模式")
                elif node_ratio >= 0.5:
                    print("  ✅ 降级策略质量良好")
                else:
                    print("  ⚠️ 降级策略质量需要提升")
        else:
            print("  ⚠️ AI服务不可用，跳过AI模式对比")
            print("  ✅ 增强降级策略确保系统在AI服务异常时仍能正常工作")
    
    except Exception as e:
        print(f"  ❌ 完整图谱构建测试失败: {e}")
    
    print()
    
    # 总结
    print("📊 验证总结")
    print("=" * 60)
    
    if ai_available and quality_score >= 80:
        status = "🎉 优秀"
        summary = "AI服务正常，增强降级策略工作优秀，系统状态最佳"
    elif not ai_available and quality_score >= 60:
        status = "✅ 良好" 
        summary = "AI服务异常但增强降级策略确保系统稳定运行"
    elif quality_score >= 40:
        status = "⚠️ 一般"
        summary = "系统基本正常，但可能需要进一步优化"
    else:
        status = "❌ 需要修复"
        summary = "系统存在问题，需要立即检查和修复"
    
    print(f"系统状态: {status}")
    print(f"状态说明: {summary}")
    print()
    print("🔧 修复验证完成！")
    
    return {
        "ai_available": ai_available,
        "quality_score": quality_score,
        "nodes_count": nodes_count,
        "edges_count": edges_count,
        "execution_time": execution_time,
        "status": status
    }

if __name__ == "__main__":
    asyncio.run(verify_task_e_fix()) 