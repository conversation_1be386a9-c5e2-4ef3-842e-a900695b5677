#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业单元测试：测试子任务跳转功能、快速模式配置、datetime序列化修复等
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch
from datetime import datetime
import json

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestDatetimeSerializationFix(unittest.TestCase):
    """测试datetime序列化修复功能"""
    
    def test_datetime_serialization(self):
        """测试datetime对象是否能正确序列化"""
        try:
            from resona.utils.detailed_results_manager import json_serial_default
            
            # 测试datetime对象
            test_datetime = datetime(2025, 6, 30, 16, 30, 45, 123456)
            result = json_serial_default(test_datetime)
            
            self.assertEqual(result, "2025-06-30T16:30:45.123456")
            self.assertIsInstance(result, str)
            
            print("✅ datetime序列化测试通过")
            
        except ImportError as e:
            self.fail(f"导入失败: {e}")
    
    def test_json_dumps_with_datetime(self):
        """测试包含datetime的完整JSON序列化"""
        try:
            from resona.utils.detailed_results_manager import json_serial_default
            
            test_data = {
                "timestamp": datetime.now(),
                "nested": {
                    "created_at": datetime(2025, 6, 30, 12, 0, 0)
                },
                "simple_field": "test"
            }
            
            # 这应该不会抛出异常
            json_str = json.dumps(test_data, default=json_serial_default, ensure_ascii=False)
            parsed_data = json.loads(json_str)
            
            # 验证结构保持不变且datetime已转换为字符串
            self.assertIn("timestamp", parsed_data)
            self.assertIsInstance(parsed_data["timestamp"], str)
            self.assertEqual(parsed_data["simple_field"], "test")
            
            print("✅ 复杂JSON+datetime序列化测试通过")
            
        except Exception as e:
            self.fail(f"JSON序列化测试失败: {e}")

class TestConfigValidation(unittest.TestCase):
    """测试配置系统的基本功能"""
    
    def test_settings_import(self):
        """测试配置文件是否能正确导入"""
        try:
            from resona.config import settings
            
            # 验证关键配置项存在
            required_attrs = [
                'post_search_limit',
                'reddit_history_limit', 
                'max_quality_commenters',
                'top_k_matches'
            ]
            
            for attr in required_attrs:
                self.assertTrue(hasattr(settings, attr), f"配置项 {attr} 不存在")
                value = getattr(settings, attr)
                self.assertIsInstance(value, int, f"配置项 {attr} 应该是整数")
                self.assertGreater(value, 0, f"配置项 {attr} 应该大于0")
            
            print("✅ 配置系统验证通过")
            
        except ImportError as e:
            self.fail(f"配置导入失败: {e}")

class TestModelImports(unittest.TestCase):
    """测试核心模型类的导入"""
    
    def test_user_models_import(self):
        """测试用户模型导入"""
        try:
            from resona.models.user_models import ParsedQuery
            
            # 验证ParsedQuery类的基本属性
            query_attrs = ['original_text', 'topics', 'emotional_state', 'values_info']
            
            # 创建一个简单的测试实例
            test_query = ParsedQuery(
                original_text="测试输入",
                topics=["test"],
                emotional_state={"anxiety": 0.5},
                values_info={"stability": 0.7}
            )
            
            for attr in query_attrs:
                self.assertTrue(hasattr(test_query, attr), f"ParsedQuery缺少属性 {attr}")
            
            print("✅ 用户模型导入测试通过")
            
        except ImportError as e:
            self.fail(f"用户模型导入失败: {e}")
    
    def test_graph_models_import(self):
        """测试图谱模型导入"""
        try:
            from resona.models.graph_models import UserGraph, GraphNode, NodeType
            
            # 验证基本类存在
            self.assertTrue(hasattr(UserGraph, '__init__'))
            self.assertTrue(hasattr(GraphNode, '__init__'))
            
            print("✅ 图谱模型导入测试通过")
            
        except ImportError as e:
            self.fail(f"图谱模型导入失败: {e}")

class TestStepByStepDebugScript(unittest.TestCase):
    """测试步进调试脚本的基本功能"""
    
    def test_script_import(self):
        """测试调试脚本是否能正确导入"""
        try:
            script_path = os.path.join(os.path.dirname(__file__), "test_step_by_step_debug.py")
            
            # 检查文件是否存在
            self.assertTrue(os.path.exists(script_path), "test_step_by_step_debug.py 文件不存在")
            
            # 尝试导入脚本中的关键类
            import importlib.util
            spec = importlib.util.spec_from_file_location("debug_module", script_path)
            debug_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(debug_module)
            
            # 验证关键类存在
            self.assertTrue(hasattr(debug_module, 'SubtaskExecutor'), "SubtaskExecutor类不存在")
            self.assertTrue(hasattr(debug_module, 'FastModeConfig'), "FastModeConfig类不存在")
            self.assertTrue(hasattr(debug_module, 'DetailedResultsCapture'), "DetailedResultsCapture类不存在")
            
            print("✅ 步进调试脚本导入测试通过")
            
        except Exception as e:
            self.fail(f"调试脚本导入失败: {e}")

class TestFastModeConfig(unittest.TestCase):
    """测试快速模式配置功能"""
    
    def setUp(self):
        """测试前准备"""
        try:
            from resona.config import settings
            # 保存原始配置
            self.original_config = {
                'post_search_limit': getattr(settings, 'post_search_limit', 100),
                'reddit_history_limit': getattr(settings, 'reddit_history_limit', 100),
                'max_quality_commenters': getattr(settings, 'max_quality_commenters', 30),
            }
        except ImportError:
            self.skipTest("无法导入配置系统")
    
    def tearDown(self):
        """测试后清理"""
        try:
            from resona.config import settings
            # 恢复原始配置
            for key, value in self.original_config.items():
                setattr(settings, key, value)
        except ImportError:
            pass
    
    def test_fast_mode_config_class(self):
        """测试FastModeConfig类的基本功能"""
        try:
            # 导入调试脚本中的FastModeConfig
            script_path = os.path.join(os.path.dirname(__file__), "test_step_by_step_debug.py")
            import importlib.util
            spec = importlib.util.spec_from_file_location("debug_module", script_path)
            debug_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(debug_module)
            
            FastModeConfig = debug_module.FastModeConfig
            
            # 验证类方法存在
            self.assertTrue(hasattr(FastModeConfig, 'apply_fast_config'))
            self.assertTrue(hasattr(FastModeConfig, 'restore_original_config'))
            
            print("✅ FastModeConfig类结构验证通过")
            
        except Exception as e:
            self.fail(f"FastModeConfig测试失败: {e}")

def run_component_tests():
    """运行组件测试套件"""
    print("🧪 开始运行组件单元测试...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDatetimeSerializationFix,
        TestConfigValidation,
        TestModelImports,
        TestStepByStepDebugScript,
        TestFastModeConfig
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果摘要
    print("\n" + "=" * 60)
    print("🎯 测试结果摘要:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
            print(f"    原因: {traceback.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback else '详见完整输出'}")
    
    if result.errors:
        print("\n⚠️ 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
            print(f"    错误: {traceback.split('Error:')[-1].strip() if 'Error:' in traceback else '详见完整输出'}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100 if result.testsRun > 0 else 0
    print(f"\n✅ 测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_component_tests()
    print(f"\n{'🎉 所有测试通过！' if success else '❌ 有测试失败，请检查上述输出'}")
    sys.exit(0 if success else 1) 