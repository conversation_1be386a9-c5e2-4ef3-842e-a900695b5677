"""
社交媒体服务抽象基类
定义所有平台（Reddit、Twitter等）需要实现的核心接口
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
from datetime import datetime

class BaseSocialService(ABC):
    """社交媒体服务抽象基类"""
    
    @abstractmethod
    async def search_posts_by_keywords_enhanced(self, keywords: List[str], 
                                               limit: int = 100,
                                               time_filter: str = "all") -> List[Dict[str, Any]]:
        """
        增强的关键词搜索帖子
        
        Args:
            keywords: 搜索关键词列表
            limit: 搜索帖子数量限制
            time_filter: 时间过滤器（all/month/week/day）
            
        Returns:
            帖子列表，每个帖子包含id/text/score/created_utc/subreddit等字段
        """
        pass
    
    @abstractmethod
    async def rerank_posts_by_embedding(self, posts: List[Dict[str, Any]], 
                                       user_query: str, 
                                       top_k: int = 30) -> List[Dict[str, Any]]:
        """
        使用嵌入向量重新排序帖子
        
        Args:
            posts: 原始帖子列表
            user_query: 用户查询文本
            top_k: 返回前k个最相关帖子
            
        Returns:
            重新排序后的帖子列表
        """
        pass
    
    @abstractmethod
    async def llm_final_rank_posts(self, posts: List[Dict[str, Any]], 
                                  user_query: str, 
                                  top_k: int = 10) -> List[Dict[str, Any]]:
        """
        LLM最终精排帖子
        
        Args:
            posts: 嵌入排序后的帖子列表
            user_query: 用户查询文本
            top_k: 返回前k个最相关帖子
            
        Returns:
            LLM精排后的帖子列表
        """
        pass
    
    @abstractmethod
    async def extract_quality_commenters_detailed(self, posts: List[Any], 
                                                min_score: int = 0, 
                                                min_length: int = 100,
                                                max_commenters: int = 50) -> Dict[str, Any]:
        """
        从帖子中提取优质评论者/互动者（详细版本）
        
        Args:
            posts: 帖子列表
            min_score: 最低互动分数
            min_length: 最短内容长度
            max_commenters: 最大候选人数量
            
        Returns:
            包含详细候选人信息的字典
        """
        pass
    
    @abstractmethod
    async def get_user_comprehensive_history(self, username: str, limit: int = 200, 
                                           filter_top_level_comments: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取用户全面历史内容（实现时间采样）
        
        Args:
            username: 用户名/用户ID
            limit: 历史内容数量限制
            filter_top_level_comments: 是否只获取顶级回复
            
        Returns:
            用户历史内容字典，包含posts/comments/content_stats等
        """
        pass
    
    @abstractmethod
    async def close(self):
        """关闭服务，清理资源"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """测试连接是否正常"""
        pass 