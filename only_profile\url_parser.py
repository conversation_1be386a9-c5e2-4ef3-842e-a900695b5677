"""
Reddit链接解析模块
支持解析Reddit Post、Comment、User等各种链接格式
"""
import re
from typing import Optional, Dict, Any, Union
from urllib.parse import urlparse
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class RedditLinkType(Enum):
    """Reddit链接类型枚举"""
    POST = "post"
    COMMENT = "comment"
    USER = "user"
    SUBREDDIT = "subreddit"
    UNKNOWN = "unknown"

@dataclass
class RedditLinkInfo:
    """Reddit链接信息"""
    link_type: RedditLinkType
    original_url: str
    username: Optional[str] = None
    subreddit: Optional[str] = None
    post_id: Optional[str] = None
    comment_id: Optional[str] = None
    title: Optional[str] = None
    is_valid: bool = True
    error_message: Optional[str] = None

class RedditUrlParser:
    """Reddit链接解析器"""
    
    def __init__(self):
        # 编译正则表达式模式
        self.patterns = {
            # 用户链接：/u/username 或 /user/username
            'user': re.compile(r'^https?://(?:www\.)?reddit\.com/(?:u|user)/([a-zA-Z0-9_-]+)/?(?:\?.*)?$'),
            
            # 帖子链接：/r/subreddit/comments/postid/title/
            'post': re.compile(r'^https?://(?:www\.)?reddit\.com/r/([a-zA-Z0-9_-]+)/comments/([a-zA-Z0-9]+)/?(?:[^/]*/?)?(?:\?.*)?$'),
            
            # 评论链接：/r/subreddit/comments/postid/title/commentid/
            'comment': re.compile(r'^https?://(?:www\.)?reddit\.com/r/([a-zA-Z0-9_-]+)/comments/([a-zA-Z0-9]+)/[^/]*/([a-zA-Z0-9]+)/?(?:\?.*)?$'),
            
            # 子版块链接：/r/subreddit/
            'subreddit': re.compile(r'^https?://(?:www\.)?reddit\.com/r/([a-zA-Z0-9_-]+)/?(?:\?.*)?$'),
            
            # 旧版链接格式
            'old_post': re.compile(r'^https?://(?:old\.)?reddit\.com/r/([a-zA-Z0-9_-]+)/comments/([a-zA-Z0-9]+)/?(?:[^/]*/?)?(?:\?.*)?$'),
            'old_comment': re.compile(r'^https?://(?:old\.)?reddit\.com/r/([a-zA-Z0-9_-]+)/comments/([a-zA-Z0-9]+)/[^/]*/([a-zA-Z0-9]+)/?(?:\?.*)?$'),
            'old_user': re.compile(r'^https?://(?:old\.)?reddit\.com/(?:u|user)/([a-zA-Z0-9_-]+)/?(?:\?.*)?$'),
            
            # 移动版链接
            'mobile_post': re.compile(r'^https?://(?:m\.)?reddit\.com/r/([a-zA-Z0-9_-]+)/comments/([a-zA-Z0-9]+)/?(?:[^/]*/?)?(?:\?.*)?$'),
            'mobile_comment': re.compile(r'^https?://(?:m\.)?reddit\.com/r/([a-zA-Z0-9_-]+)/comments/([a-zA-Z0-9]+)/[^/]*/([a-zA-Z0-9]+)/?(?:\?.*)?$'),
            'mobile_user': re.compile(r'^https?://(?:m\.)?reddit\.com/(?:u|user)/([a-zA-Z0-9_-]+)/?(?:\?.*)?$'),
            
            # Reddit短链接
            'short_link': re.compile(r'^https?://redd\.it/([a-zA-Z0-9]+)/?(?:\?.*)?$'),
        }
        
        logger.info("Reddit链接解析器初始化完成")
    
    def parse_url(self, url: str) -> RedditLinkInfo:
        """
        解析Reddit链接
        
        Args:
            url: Reddit链接
            
        Returns:
            RedditLinkInfo: 解析后的链接信息
        """
        logger.info(f"开始解析Reddit链接: {url}")
        
        # 基本验证
        if not url or not isinstance(url, str):
            return RedditLinkInfo(
                link_type=RedditLinkType.UNKNOWN,
                original_url=url,
                is_valid=False,
                error_message="链接为空或格式错误"
            )
        
        # 清理URL
        url = url.strip()
        
        # 检查是否为Reddit域名
        if not self._is_reddit_url(url):
            return RedditLinkInfo(
                link_type=RedditLinkType.UNKNOWN,
                original_url=url,
                is_valid=False,
                error_message="不是Reddit链接"
            )
        
        # 按优先级匹配模式
        try:
            # 1. 评论链接（最具体）
            result = self._match_comment_link(url)
            if result:
                return result
            
            # 2. 帖子链接
            result = self._match_post_link(url)
            if result:
                return result
            
            # 3. 用户链接
            result = self._match_user_link(url)
            if result:
                return result
            
            # 4. 子版块链接
            result = self._match_subreddit_link(url)
            if result:
                return result
            
            # 5. 短链接
            result = self._match_short_link(url)
            if result:
                return result
            
            # 未匹配到任何模式
            return RedditLinkInfo(
                link_type=RedditLinkType.UNKNOWN,
                original_url=url,
                is_valid=False,
                error_message="未知的Reddit链接格式"
            )
            
        except Exception as e:
            logger.error(f"解析Reddit链接时发生错误: {e}")
            return RedditLinkInfo(
                link_type=RedditLinkType.UNKNOWN,
                original_url=url,
                is_valid=False,
                error_message=f"解析错误: {str(e)}"
            )
    
    def _is_reddit_url(self, url: str) -> bool:
        """检查是否为Reddit域名"""
        try:
            parsed = urlparse(url)
            reddit_domains = [
                'reddit.com', 'www.reddit.com', 'old.reddit.com',
                'm.reddit.com', 'redd.it'
            ]
            return parsed.netloc.lower() in reddit_domains
        except Exception:
            return False
    
    def _match_comment_link(self, url: str) -> Optional[RedditLinkInfo]:
        """匹配评论链接"""
        patterns = [
            self.patterns['comment'],
            self.patterns['old_comment'],
            self.patterns['mobile_comment']
        ]
        
        for pattern in patterns:
            match = pattern.match(url)
            if match:
                subreddit, post_id, comment_id = match.groups()
                return RedditLinkInfo(
                    link_type=RedditLinkType.COMMENT,
                    original_url=url,
                    subreddit=subreddit,
                    post_id=post_id,
                    comment_id=comment_id
                )
        return None
    
    def _match_post_link(self, url: str) -> Optional[RedditLinkInfo]:
        """匹配帖子链接"""
        patterns = [
            self.patterns['post'],
            self.patterns['old_post'],
            self.patterns['mobile_post']
        ]
        
        for pattern in patterns:
            match = pattern.match(url)
            if match:
                subreddit, post_id = match.groups()
                return RedditLinkInfo(
                    link_type=RedditLinkType.POST,
                    original_url=url,
                    subreddit=subreddit,
                    post_id=post_id
                )
        return None
    
    def _match_user_link(self, url: str) -> Optional[RedditLinkInfo]:
        """匹配用户链接"""
        patterns = [
            self.patterns['user'],
            self.patterns['old_user'],
            self.patterns['mobile_user']
        ]
        
        for pattern in patterns:
            match = pattern.match(url)
            if match:
                username = match.group(1)
                return RedditLinkInfo(
                    link_type=RedditLinkType.USER,
                    original_url=url,
                    username=username
                )
        return None
    
    def _match_subreddit_link(self, url: str) -> Optional[RedditLinkInfo]:
        """匹配子版块链接"""
        match = self.patterns['subreddit'].match(url)
        if match:
            subreddit = match.group(1)
            return RedditLinkInfo(
                link_type=RedditLinkType.SUBREDDIT,
                original_url=url,
                subreddit=subreddit
            )
        return None
    
    def _match_short_link(self, url: str) -> Optional[RedditLinkInfo]:
        """匹配短链接"""
        match = self.patterns['short_link'].match(url)
        if match:
            post_id = match.group(1)
            return RedditLinkInfo(
                link_type=RedditLinkType.POST,
                original_url=url,
                post_id=post_id
            )
        return None
    
    def extract_username_from_link(self, url: str) -> Optional[str]:
        """
        从Reddit链接中提取用户名
        对于Post和Comment类型的链接，需要额外的API调用来获取作者信息
        
        Args:
            url: Reddit链接
            
        Returns:
            str: 用户名，如果无法提取则返回None
        """
        link_info = self.parse_url(url)
        
        if not link_info.is_valid:
            logger.warning(f"无效的Reddit链接: {link_info.error_message}")
            return None
        
        if link_info.link_type == RedditLinkType.USER:
            return link_info.username
        elif link_info.link_type in [RedditLinkType.POST, RedditLinkType.COMMENT]:
            # 对于Post和Comment，需要通过API获取作者信息
            logger.info(f"需要通过API获取 {link_info.link_type.value} 的作者信息")
            return None
        else:
            logger.warning(f"不支持从 {link_info.link_type.value} 类型链接中提取用户名")
            return None
    
    def validate_reddit_link(self, url: str) -> bool:
        """
        验证Reddit链接是否有效
        
        Args:
            url: Reddit链接
            
        Returns:
            bool: 是否有效
        """
        link_info = self.parse_url(url)
        return link_info.is_valid
    
    def get_link_summary(self, url: str) -> Dict[str, Any]:
        """
        获取链接摘要信息
        
        Args:
            url: Reddit链接
            
        Returns:
            Dict: 链接摘要信息
        """
        link_info = self.parse_url(url)
        
        summary = {
            "url": url,
            "type": link_info.link_type.value,
            "valid": link_info.is_valid,
            "error": link_info.error_message
        }
        
        if link_info.is_valid:
            if link_info.username:
                summary["username"] = link_info.username
            if link_info.subreddit:
                summary["subreddit"] = link_info.subreddit
            if link_info.post_id:
                summary["post_id"] = link_info.post_id
            if link_info.comment_id:
                summary["comment_id"] = link_info.comment_id
        
        return summary

# 全局解析器实例
reddit_parser = RedditUrlParser()

def parse_reddit_url(url: str) -> RedditLinkInfo:
    """便捷函数：解析Reddit链接"""
    return reddit_parser.parse_url(url)

def extract_username(url: str) -> Optional[str]:
    """便捷函数：提取用户名"""
    return reddit_parser.extract_username_from_link(url)

def validate_reddit_url(url: str) -> bool:
    """便捷函数：验证Reddit链接"""
    return reddit_parser.validate_reddit_link(url)

if __name__ == "__main__":
    # 测试代码
    test_urls = [
        "https://reddit.com/u/testuser",
        "https://www.reddit.com/r/python/comments/abc123/some_title/",
        "https://reddit.com/r/python/comments/abc123/some_title/def456/",
        "https://old.reddit.com/r/askreddit/comments/xyz789/",
        "https://redd.it/abc123",
        "https://example.com/not-reddit",
        "invalid-url"
    ]
    
    parser = RedditUrlParser()
    for url in test_urls:
        print(f"\n测试URL: {url}")
        result = parser.parse_url(url)
        print(f"  类型: {result.link_type.value}")
        print(f"  有效: {result.is_valid}")
        if result.username:
            print(f"  用户名: {result.username}")
        if result.subreddit:
            print(f"  子版块: {result.subreddit}")
        if result.post_id:
            print(f"  帖子ID: {result.post_id}")
        if result.comment_id:
            print(f"  评论ID: {result.comment_id}")
        if result.error_message:
            print(f"  错误: {result.error_message}") 