#!/usr/bin/env python3
"""only_profile URL解析器单元测试 - 修复版"""

import unittest
import sys
import os
import logging

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'only_profile'))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入待测试模块
IMPORT_SUCCESS = False
try:
    from url_parser import (
        RedditUrlParser, RedditLinkInfo, RedditLinkType,
        parse_reddit_url, extract_username, validate_reddit_url
    )
    IMPORT_SUCCESS = True
    logger.info("成功导入URL解析器模块")
except ImportError as e:
    logger.error(f"导入URL解析器模块失败: {e}")


class TestRedditUrlParserFixed(unittest.TestCase):
    """Reddit URL解析器测试类 - 修复版"""
    
    def setUp(self):
        """测试设置"""
        if not IMPORT_SUCCESS:
            self.skipTest("无法导入待测试模块")
        
        self.parser = RedditUrlParser()
        
        # 测试用的有效URL - 修复格式以匹配正则表达式
        self.valid_user_urls = [
            "https://reddit.com/u/testuser",
            "https://www.reddit.com/user/testuser",
            "https://old.reddit.com/u/testuser"
        ]
        
        self.valid_post_urls = [
            "https://reddit.com/r/Python/comments/abc123",
            "https://www.reddit.com/r/Python/comments/abc123/title",
            "https://old.reddit.com/r/MachineLearning/comments/def456"
        ]
        
        # 修复评论URL格式 - 确保comment_id只包含字母数字
        self.valid_comment_urls = [
            "https://reddit.com/r/Python/comments/abc123/title/xyz789",
            "https://www.reddit.com/r/Python/comments/abc123/post_title/comment123"
        ]
        
        self.invalid_urls = [
            "",
            None,
            "not_a_url",
            "https://twitter.com/user",
            "https://facebook.com/user"
        ]
    
    def test_parse_user_urls(self):
        """测试用户URL解析"""
        print("测试用户URL解析...")
        
        for url in self.valid_user_urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertIsInstance(result, RedditLinkInfo)
                self.assertEqual(result.link_type, RedditLinkType.USER)
                self.assertTrue(result.is_valid)
                self.assertIsNotNone(result.username)
                self.assertEqual(result.original_url, url)
    
    def test_parse_post_urls(self):
        """测试帖子URL解析"""
        print("测试帖子URL解析...")
        
        for url in self.valid_post_urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertIsInstance(result, RedditLinkInfo)
                self.assertEqual(result.link_type, RedditLinkType.POST)
                self.assertTrue(result.is_valid)
                self.assertIsNotNone(result.subreddit)
                self.assertIsNotNone(result.post_id)
                self.assertEqual(result.original_url, url)
    
    def test_parse_comment_urls(self):
        """测试评论URL解析"""
        print("测试评论URL解析...")
        
        for url in self.valid_comment_urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertIsInstance(result, RedditLinkInfo)
                self.assertEqual(result.link_type, RedditLinkType.COMMENT)
                self.assertTrue(result.is_valid)
                self.assertIsNotNone(result.subreddit)
                self.assertIsNotNone(result.post_id)
                self.assertIsNotNone(result.comment_id)
                self.assertEqual(result.original_url, url)
    
    def test_parse_invalid_urls(self):
        """测试无效URL解析"""
        print("测试无效URL解析...")
        
        for url in self.invalid_urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertIsInstance(result, RedditLinkInfo)
                self.assertEqual(result.link_type, RedditLinkType.UNKNOWN)
                self.assertFalse(result.is_valid)
                self.assertIsNotNone(result.error_message)
    
    def test_validate_reddit_link_valid(self):
        """测试有效链接验证"""
        print("测试有效链接验证...")
        
        valid_urls = self.valid_user_urls + self.valid_post_urls + self.valid_comment_urls
        for url in valid_urls:
            with self.subTest(url=url):
                self.assertTrue(self.parser.validate_reddit_link(url))
    
    def test_validate_reddit_link_invalid(self):
        """测试无效链接验证"""
        print("测试无效链接验证...")
        
        for url in self.invalid_urls:
            with self.subTest(url=url):
                self.assertFalse(self.parser.validate_reddit_link(url))
    
    def test_extract_username_from_link(self):
        """测试从链接提取用户名"""
        print("测试从链接提取用户名...")
        
        # 测试用户链接
        user_url = "https://reddit.com/u/testuser"
        username = self.parser.extract_username_from_link(user_url)
        self.assertEqual(username, "testuser")
        
        # 测试帖子链接（应该返回None，因为需要API调用）
        post_url = "https://reddit.com/r/Python/comments/abc123"
        username = self.parser.extract_username_from_link(post_url)
        self.assertIsNone(username)
        
        # 测试无效链接
        invalid_url = "https://twitter.com/user"
        username = self.parser.extract_username_from_link(invalid_url)
        self.assertIsNone(username)
    
    def test_get_link_summary(self):
        """测试获取链接摘要"""
        print("测试获取链接摘要...")
        
        # 测试用户链接
        user_url = "https://reddit.com/u/testuser"
        summary = self.parser.get_link_summary(user_url)
        self.assertIsInstance(summary, dict)
        self.assertEqual(summary["type"], "user")
        self.assertTrue(summary["valid"])
        self.assertIn("username", summary)
        
        # 测试帖子链接
        post_url = "https://reddit.com/r/Python/comments/abc123"
        summary = self.parser.get_link_summary(post_url)
        self.assertIsInstance(summary, dict)
        self.assertEqual(summary["type"], "post")
        self.assertTrue(summary["valid"])
        self.assertIn("subreddit", summary)
        self.assertIn("post_id", summary)
    
    def test_convenience_functions(self):
        """测试便捷函数"""
        print("测试便捷函数...")
        
        # 测试全局解析函数
        user_url = "https://reddit.com/u/testuser"
        result = parse_reddit_url(user_url)
        self.assertIsInstance(result, RedditLinkInfo)
        self.assertEqual(result.link_type, RedditLinkType.USER)
        
        # 测试全局用户名提取函数
        username = extract_username(user_url)
        self.assertEqual(username, "testuser")
        
        # 测试全局验证函数
        self.assertTrue(validate_reddit_url(user_url))
        self.assertFalse(validate_reddit_url("invalid-url"))
    
    def test_reddit_link_info_attributes(self):
        """测试RedditLinkInfo对象的属性"""
        print("测试RedditLinkInfo对象的属性...")
        
        # 测试用户链接
        user_url = "https://reddit.com/u/testuser"
        result = self.parser.parse_url(user_url)
        
        # 检查所有属性
        self.assertTrue(hasattr(result, 'link_type'))
        self.assertTrue(hasattr(result, 'original_url'))
        self.assertTrue(hasattr(result, 'username'))
        self.assertTrue(hasattr(result, 'subreddit'))
        self.assertTrue(hasattr(result, 'post_id'))
        self.assertTrue(hasattr(result, 'comment_id'))
        self.assertTrue(hasattr(result, 'title'))
        self.assertTrue(hasattr(result, 'is_valid'))
        self.assertTrue(hasattr(result, 'error_message'))
    
    def test_edge_cases(self):
        """测试边界情况"""
        print("测试边界情况...")
        
        # 测试空字符串
        result = self.parser.parse_url("")
        self.assertEqual(result.link_type, RedditLinkType.UNKNOWN)
        self.assertFalse(result.is_valid)
        
        # 测试None
        result = self.parser.parse_url(None)
        self.assertEqual(result.link_type, RedditLinkType.UNKNOWN)
        self.assertFalse(result.is_valid)
        
        # 测试包含空格的URL
        url_with_spaces = "  https://reddit.com/u/testuser  "
        result = self.parser.parse_url(url_with_spaces)
        self.assertEqual(result.link_type, RedditLinkType.USER)
        self.assertTrue(result.is_valid)
    
    def test_different_domains(self):
        """测试不同的Reddit域名"""
        print("测试不同的Reddit域名...")
        
        domains = [
            "https://reddit.com/u/testuser",
            "https://www.reddit.com/u/testuser",
            "https://old.reddit.com/u/testuser",
            "https://m.reddit.com/u/testuser"
        ]
        
        for url in domains:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertEqual(result.link_type, RedditLinkType.USER)
                self.assertTrue(result.is_valid)
                self.assertEqual(result.username, "testuser")
    
    def test_short_links(self):
        """测试短链接"""
        print("测试短链接...")
        
        short_url = "https://redd.it/abc123"
        result = self.parser.parse_url(short_url)
        self.assertEqual(result.link_type, RedditLinkType.POST)
        self.assertTrue(result.is_valid)
        self.assertEqual(result.post_id, "abc123")
    
    def test_url_with_parameters(self):
        """测试带参数的URL"""
        print("测试带参数的URL...")
        
        url_with_params = "https://reddit.com/u/testuser?utm_source=share&utm_medium=web2x"
        result = self.parser.parse_url(url_with_params)
        self.assertEqual(result.link_type, RedditLinkType.USER)
        self.assertTrue(result.is_valid)
        self.assertEqual(result.username, "testuser")
    
    def test_special_characters_in_username(self):
        """测试用户名中的特殊字符"""
        print("测试用户名中的特殊字符...")
        
        special_usernames = [
            "user_123",
            "user-abc",
            "User123",
            "test_user_123"
        ]
        
        for username in special_usernames:
            with self.subTest(username=username):
                url = f"https://reddit.com/u/{username}"
                result = self.parser.parse_url(url)
                self.assertEqual(result.link_type, RedditLinkType.USER)
                self.assertTrue(result.is_valid)
                self.assertEqual(result.username, username)


class TestRedditUrlParserIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试设置"""
        if not IMPORT_SUCCESS:
            self.skipTest("无法导入待测试模块")
        
        self.parser = RedditUrlParser()
    
    def test_workflow_example(self):
        """测试完整工作流程"""
        print("测试完整工作流程...")
        
        # 模拟真实使用场景
        urls = [
            "https://reddit.com/u/tech_enthusiast",
            "https://reddit.com/r/Python/comments/abc123/interesting_post",
            "https://reddit.com/r/Python/comments/abc123/interesting_post/xyz789"
        ]
        
        for url in urls:
            with self.subTest(url=url):
                # 1. 验证链接
                is_valid = self.parser.validate_reddit_link(url)
                self.assertTrue(is_valid)
                
                # 2. 解析链接
                result = self.parser.parse_url(url)
                self.assertTrue(result.is_valid)
                
                # 3. 获取摘要
                summary = self.parser.get_link_summary(url)
                self.assertTrue(summary["valid"])
    
    def test_error_handling(self):
        """测试错误处理"""
        print("测试错误处理...")
        
        # 测试各种错误情况
        error_cases = [
            ("", "空字符串"),
            (None, "None值"),
            ("https://twitter.com/user", "非Reddit链接"),
            ("not_a_url", "无效URL格式")
        ]
        
        for url, description in error_cases:
            with self.subTest(description=description):
                result = self.parser.parse_url(url)
                self.assertEqual(result.link_type, RedditLinkType.UNKNOWN)
                self.assertFalse(result.is_valid)
                self.assertIsNotNone(result.error_message)


def run_url_parser_tests_fixed():
    """运行URL解析器测试"""
    print("=" * 60)
    print("开始运行 only_profile URL解析器单元测试 - 修复版")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_suite.addTest(unittest.makeSuite(TestRedditUrlParserFixed))
    test_suite.addTest(unittest.makeSuite(TestRedditUrlParserIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果统计
    print("\n" + "=" * 60)
    print("测试结果统计:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped)}")
    print("=" * 60)
    
    return result


if __name__ == "__main__":
    run_url_parser_tests_fixed() 