<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resona - 深度共鸣匹配引擎</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .loader {
            border-top-color: #667eea;
            -webkit-animation: spinner 1.5s linear infinite;
            animation: spinner 1.5s linear infinite;
        }
        @-webkit-keyframes spinner {
            0% { -webkit-transform: rotate(0deg); }
            100% { -webkit-transform: rotate(360deg); }
        }
        @keyframes spinner {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white py-6 px-4">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold">Resona</h1>
            <p class="text-purple-100 mt-2">基于深度共鸣的 Reddit 用户匹配引擎</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto px-4 py-8">
        <!-- Input Section -->
        <section id="inputSection" class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4">说出你的困扰</h2>
            <p class="text-gray-600 mb-6">
                输入你当前的心理状态或困扰，系统将为你找到有相似经历的 Reddit 用户
            </p>
            
            <textarea 
                id="queryInput"
                class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                rows="5"
                placeholder="例如：我不知道该不该从大厂辞职，越来越不认识自己了..."
                minlength="10"
            ></textarea>
            
            <div class="mt-4 flex items-center justify-between">
                <div id="queryTags" class="flex flex-wrap gap-2"></div>
                <button 
                    id="searchBtn"
                    onclick="performSearch()"
                    class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200"
                >
                    开始寻找共鸣
                </button>
            </div>
        </section>

        <!-- Loading State -->
        <section id="loadingSection" class="hidden">
            <div class="bg-white rounded-lg shadow-lg p-12 text-center">
                <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-12 w-12 mb-4 mx-auto"></div>
                <h3 class="text-xl font-semibold mb-2">正在分析并寻找共鸣用户...</h3>
                <p class="text-gray-600">这可能需要几秒钟时间</p>
            </div>
        </section>

        <!-- Results Section -->
        <section id="resultsSection" class="hidden">
            <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-semibold text-blue-900 mb-2">你的情绪画像</h3>
                <div id="profileSummary" class="text-blue-800"></div>
            </div>

            <h2 class="text-2xl font-semibold mb-6">为你找到的共鸣伙伴</h2>
            <div id="matchedUsers" class="space-y-6"></div>
        </section>

        <!-- Error State -->
        <section id="errorSection" class="hidden">
            <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                <h3 class="text-xl font-semibold text-red-900 mb-2">出现了一些问题</h3>
                <p id="errorMessage" class="text-red-700"></p>
                <button 
                    onclick="resetSearch()"
                    class="mt-4 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-6 rounded-lg"
                >
                    重新开始
                </button>
            </div>
        </section>
    </main>

    <!-- Private Message Modal -->
    <div id="messageModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">私信建议</h3>
                <textarea 
                    id="messageContent"
                    class="w-full p-3 border border-gray-300 rounded-md resize-none"
                    rows="5"
                ></textarea>
                <div class="mt-4 flex justify-end space-x-3">
                    <button 
                        onclick="closeModal()"
                        class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-md"
                    >
                        关闭
                    </button>
                    <button 
                        onclick="copyMessage()"
                        class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md"
                    >
                        复制内容
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentResult = null;

        async function performSearch() {
            const queryText = document.getElementById('queryInput').value.trim();
            
            if (queryText.length < 10) {
                alert('请输入至少10个字符的描述');
                return;
            }

            // 切换到加载状态
            document.getElementById('inputSection').classList.add('hidden');
            document.getElementById('resultsSection').classList.add('hidden');
            document.getElementById('errorSection').classList.add('hidden');
            document.getElementById('loadingSection').classList.remove('hidden');

            try {
                const response = await axios.post('/api/v3/search', {
                    text: queryText,
                    max_recommendations: 5
                });

                currentResult = response.data;
                displayResults(currentResult);

            } catch (error) {
                console.error('搜索失败:', error);
                showError(error.response?.data?.detail || error.response?.data?.error || '搜索失败，请稍后重试');
            }
        }

        function displayResults(result) {
            // 隐藏加载状态
            document.getElementById('loadingSection').classList.add('hidden');
            document.getElementById('resultsSection').classList.remove('hidden');

            // 显示用户画像摘要
            const completeness = result.completeness_info;
            const profileHtml = `
                <p><strong>输入内容：</strong>${result.user_prompt.substring(0, 100)}...</p>
                <p><strong>完整性分数：</strong>${Math.round(completeness.completeness_score * 100)}%</p>
                ${completeness.missing_aspects.length > 0 ? `
                    <div class="mt-2">
                        <p><strong>建议补充：</strong></p>
                        <div class="flex flex-wrap gap-2 mt-1">
                            ${completeness.missing_aspects.map(aspect => `<span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-sm">${aspect}</span>`).join('')}
                        </div>
                    </div>
                ` : ''}
            `;
            document.getElementById('profileSummary').innerHTML = profileHtml;

            // 显示匹配用户
            const usersHtml = result.recommendations.map(rec => createUserCard(rec)).join('');
            document.getElementById('matchedUsers').innerHTML = usersHtml || '<p class="text-gray-600">暂未找到合适的共鸣用户，请尝试更详细地描述你的困扰。</p>';
        }

        function createUserCard(recommendation) {
            const resonancePercent = Math.round(recommendation.resonance_score * 100);
            
            return `
                <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-lg font-semibold">${recommendation.candidate_id}</h3>
                            <p class="text-sm text-gray-500">共鸣分数: ${resonancePercent}% | 排名: #${recommendation.rank}</p>
                            <p class="text-sm font-medium text-purple-600">${recommendation.recommendation_strength}</p>
                        </div>
                        <div class="flex gap-2">
                            <button 
                                onclick="showMessage('${escapeQuotes(recommendation.conversation_starters.join('\\n\\n'))}')"
                                class="bg-purple-100 hover:bg-purple-200 text-purple-700 px-4 py-2 rounded-md text-sm font-medium"
                            >
                                对话建议
                            </button>
                            <a 
                                href="${recommendation.reddit_profile_url}" 
                                target="_blank"
                                class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                            >
                                查看主页
                            </a>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold mb-2">推荐摘要</h4>
                        <p class="text-gray-700">${recommendation.summary}</p>
                    </div>
                    
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold mb-2">匹配原因</h4>
                        <p class="text-gray-600 text-sm">${recommendation.reasoning}</p>
                    </div>
                    
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="text-xs font-medium text-gray-500">共同话题:</span>
                        ${recommendation.shared_themes.map(theme => `<span class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded">${theme}</span>`).join('')}
                    </div>
                    
                    ${Object.keys(recommendation.matching_details).length > 0 ? `
                        <div class="border-t pt-4">
                            <h4 class="text-sm font-semibold mb-2">匹配详情</h4>
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                ${Object.entries(recommendation.matching_details).map(([key, value]) => `
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">${key}:</span>
                                        <span class="font-medium">${Math.round(value * 100)}%</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        function getEmotionLabel(emotion) {
            const labels = {
                'anxiety': '焦虑',
                'confusion': '困惑',
                'sadness': '悲伤',
                'hope': '希望',
                'frustration': '挫折',
                'loneliness': '孤独',
                'guilt': '内疚',
                'fear': '恐惧',
                'anger': '愤怒',
                'relief': '释然'
            };
            return labels[emotion] || emotion;
        }

        function showMessage(message) {
            document.getElementById('messageContent').value = message;
            document.getElementById('messageModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('messageModal').classList.add('hidden');
        }

        function copyMessage() {
            const textarea = document.getElementById('messageContent');
            textarea.select();
            document.execCommand('copy');
            alert('私信内容已复制到剪贴板');
            closeModal();
        }

        function showError(message) {
            document.getElementById('loadingSection').classList.add('hidden');
            document.getElementById('errorSection').classList.remove('hidden');
            document.getElementById('errorMessage').textContent = message;
        }

        function resetSearch() {
            document.getElementById('inputSection').classList.remove('hidden');
            document.getElementById('resultsSection').classList.add('hidden');
            document.getElementById('errorSection').classList.add('hidden');
            document.getElementById('queryInput').value = '';
        }

        function escapeQuotes(str) {
            return str.replace(/'/g, "\\'").replace(/"/g, '\\"');
        }

        // 自动调整文本框高度
        document.getElementById('queryInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    </script>
</body>
</html> 