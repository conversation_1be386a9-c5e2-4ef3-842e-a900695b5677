"""
Resona 配置管理
"""
import os
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Settings(BaseSettings):
    """应用配置"""
    
    # 预定义的模型信息字典
    MODEL_CONFIGS: Dict[str, Any] = {
        "Qwen/Qwen3-235B-A22B": {
            "context_window": 40960,  # 40.96K 上下文窗口 (DeepInfra 显示的最大值)
            "description": "Qwen3 235B A22B - 超大规模 MoE 推理模型，支持 JSON 模式",
            "supports_json_mode": True,
            "supports_function_calling": True,
            "max_output_tokens": 8192,  # 建议输出限制，约20%的上下文窗口
            "recommended_temperature": 0.6,  # 推荐采样参数（thinking mode）
            "recommended_top_p": 0.95
        },
        "google/gemini-2.5-flash": {
            "context_window": 1048576,  # 匹配DeepInfra文档
            "description": "Google Gemini 2.5 Flash - 快速高效的对话模型",
            "supports_json_mode": False,
            "supports_function_calling": True,
            "max_output_tokens": 8192,
            "recommended_temperature": 0.7,  # DeepInfra默认值
            "recommended_top_p": 1.0,  # DeepInfra默认值
            "recommended_top_k": 40  # 添加以提高稳定性
        },
        "deepseek-ai/DeepSeek-R1-0528-Turbo": {
            "context_window": 32768,  # 32K 上下文窗口
            "description": "DeepSeek R1 Turbo - 高性能推理模型"
        },
        "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {
            "context_window": 32768,  # 32K 上下文窗口
            "description": "DeepSeek R1 Distill - 蒸馏优化模型"
        },
        "Qwen/Qwen2.5-72B-Instruct": {
            "context_window": 32768,  # 32K 上下文窗口
            "description": "Qwen 2.5 - 通用对话模型"
        },
        "meta-llama/Meta-Llama-3.1-70B-Instruct": {
            "context_window": 131072,  # 128K 上下文窗口
            "description": "Llama 3.1 - Meta开源模型"
        },
        "anthropic/claude-3-sonnet": {
            "context_window": 200000,  # 200K 上下文窗口
            "description": "Claude 3 Sonnet - Anthropic模型"
        },
        # 默认配置（用于未知模型）
        "default": {
            "context_window": 32768,
            "description": "默认配置"
        }
    }
    
    # Reddit API
    reddit_client_id: str = os.getenv("REDDIT_CLIENT_ID", "")
    reddit_client_secret: str = os.getenv("REDDIT_CLIENT_SECRET", "")
    reddit_user_agent: str = os.getenv("REDDIT_USER_AGENT", "Resona/1.0")
    
    # DeepInfra API
    deepinfra_api_key: str = os.getenv("DEEPINFRA_API_KEY", "")
    deepinfra_base_url: str = "https://api.deepinfra.com/v1/openai"  # 使用 OpenAI 兼容端点
    
    # 模型配置
    embedding_model: str = "Qwen/Qwen3-Embedding-8B"  # DeepInfra 上的 Qwen3-Embedding-8B
    llm_model: str = "google/gemini-2.5-flash"  # Qwen3 235B A22B - 支持 JSON 模式的超大规模模型
    
    # 数据库
    database_url: str = os.getenv("DATABASE_URL", "sqlite:///./resona.db")
    
    # 应用配置
    debug: bool = os.getenv("DEBUG", "True").lower() == "true"
    debug_step_by_step: bool = os.getenv("DEBUG_STEP_BY_STEP", "True").lower() == "true"  # 步进调试模式
    port: int = int(os.getenv("PORT", "8000"))
    host: str = os.getenv("HOST", "0.0.0.0")
    
    # 向量数据库配置
    faiss_index_path: Path = Path(os.getenv("FAISS_INDEX_PATH", "./data/faiss_index"))
    embedding_cache_path: Path = Path(os.getenv("EMBEDDING_CACHE_PATH", "./data/embeddings_cache"))
    embedding_dimension: int = 4096  # Qwen3-Embedding-8B 最高支持 4096 维度
    
    # Reddit 搜索配置
    reddit_search_limit: int = 50  # 初筛用户数（传统方法用）
    reddit_history_limit: int = 100  # 每个用户获取的历史帖子数
    reddit_subreddits: list[str] = [
        "offmychest", "relationships", "career", "findapath",
        "depression", "anxiety", "selfimprovement", "decidingtobebetter",
        "advice", "relationship_advice", "AskReddit", "mentalhealth",
        "jobs", "careerguidance", "socialskills", "getmotivated"
    ]
    
    # 新匹配流程配置
    post_search_limit: int = 100  # 搜索帖子数量（增加到100）
    post_relevance_threshold: float = 0.65  # 帖子相关性阈值
    comment_min_score: int = 0  # 评论最低分数
    comment_min_length: int = 80  # 评论最短长度
    max_quality_commenters: int = 30  # 最大优质评论者数
    
    # Reddit搜索优化配置
    reddit_search_concurrency: int = 5  # Reddit搜索并发数
    reddit_search_time_filter: str = "all"  # 默认搜索所有时间
    reddit_time_decay_years: float = 3.0  # 时间衰减阈值（年）
    
    # Embedding Rerank配置
    embedding_rerank_count: int = 30  # Embedding重排后保留数量
    llm_final_rank_count: int = 10  # LLM最终排序数量
    
    # 匹配配置
    top_k_matches: int = 5  # 推荐用户数（增加到5个）
    min_similarity_score: float = 0.6  # 最低相似度阈值（适度降低）
    
    # 并发控制配置
    max_concurrent_embeddings: int = 5  # 同时生成embedding的最大数量
    embedding_batch_size: int = 10      # 单个请求的文本批次大小
    
    # OpenAI API 配置
    openai_timeout: int = int(os.getenv("OPENAI_TIMEOUT", "3600"))  # API请求超时时间（秒）
    max_openai_concurrency: int = int(os.getenv("MAX_OPENAI_CONCURRENCY", "5"))  # 最大并发OpenAI请求数
    openai_retry_max_attempts: int = int(os.getenv("OPENAI_RETRY_MAX_ATTEMPTS", "3"))  # 最大重试次数
    openai_retry_base_delay: float = float(os.getenv("OPENAI_RETRY_BASE_DELAY", "1.0"))  # 重试基础延迟（秒）
    openai_retry_max_delay: float = float(os.getenv("OPENAI_RETRY_MAX_DELAY", "60.0"))  # 重试最大延迟（秒）
    
    # LLM Token 预算配置（智能计算）
    llm_output_ratio: float = 0.2       # 输出Token占总窗口的比例
    llm_system_ratio: float = 0.1       # 系统提示Token占总窗口的比例  
    llm_safety_factor: float = 0.9      # 安全系数，防止Token溢出
    
    # 自适应Token预算配置
    adaptive_safety: bool = True        # 是否启用自适应安全系数
    min_safety_factor: float = 0.85     # 最小安全系数（高Token占用时）
    max_safety_factor: float = 0.95     # 最大安全系数（低Token占用时）
    adaptive_node_limit: bool = True    # 是否根据Token占用动态调整节点上限
    min_nodes_per_text: int = 7         # 高Token占用时的最小节点数
    max_nodes_per_text: int = 10        # 正常情况下的最大节点数
    
    # 详细结果管理器配置
    detailed_results_dir: str = os.getenv("DETAILED_RESULTS_DIR", "cursor_test/detailed_results")
    detailed_results_flush_interval: int = int(os.getenv("DETAILED_RESULTS_FLUSH_INTERVAL", "30"))  # 秒
    detailed_results_flush_size: int = int(os.getenv("DETAILED_RESULTS_FLUSH_SIZE", "100"))  # 条目数
    detailed_results_max_debug_entries: int = int(os.getenv("DETAILED_RESULTS_MAX_DEBUG_ENTRIES", "1000"))  # 单类别最大调试条目数
    detailed_results_enable_file_lock: bool = os.getenv("DETAILED_RESULTS_ENABLE_FILE_LOCK", "True").lower() == "true"
    detailed_results_auto_backup: bool = os.getenv("DETAILED_RESULTS_AUTO_BACKUP", "True").lower() == "true"
    detailed_results_compression: bool = os.getenv("DETAILED_RESULTS_COMPRESSION", "False").lower() == "true"
    
    # 模型特定配置（可通过环境变量覆盖）
    llm_context_window: Optional[int] = None  # 如果为None则自动检测
    llm_max_output_tokens: Optional[int] = None  # 如果为None则按比例计算
    llm_system_prompt_tokens: Optional[int] = None  # 如果为None则按比例计算
    llm_batch_max_chunks: int = 50       # 单次Prompt最多合并的文本块数（默认50，减少系统提示开销）
    
    model_config = {
        "env_file": ".env", 
        "case_sensitive": False
    }

    def get_model_context_window(self) -> int:
        """获取当前模型的上下文窗口大小"""
        if self.llm_context_window is not None:
            return self.llm_context_window
        
        # 从预定义配置中查找
        model_config = self.MODEL_CONFIGS.get(self.llm_model, self.MODEL_CONFIGS["default"])
        return model_config["context_window"]
    
    def get_model_description(self) -> str:
        """获取当前模型的描述"""
        model_config = self.MODEL_CONFIGS.get(self.llm_model, self.MODEL_CONFIGS["default"])
        return model_config["description"]
    
    def calculate_token_budget(self) -> dict:
        """智能计算Token预算分配"""
        context_window = self.get_model_context_window()
        
        # 按比例计算各部分Token数
        max_output_tokens = self.llm_max_output_tokens or int(context_window * self.llm_output_ratio)
        system_prompt_tokens = self.llm_system_prompt_tokens or int(context_window * self.llm_system_ratio)
        
        # 计算可用于输入的Token数
        available_tokens = context_window - max_output_tokens - system_prompt_tokens
        
        # 应用安全系数
        effective_budget = int(available_tokens * self.llm_safety_factor)
        
        return {
            "context_window": context_window,
            "max_output_tokens": max_output_tokens,
            "system_prompt_tokens": system_prompt_tokens,
            "available_tokens": available_tokens,
            "effective_budget": effective_budget,
            "safety_factor": self.llm_safety_factor,
            "model_description": self.get_model_description()
        }

    def calculate_adaptive_token_budget(self, batch_contents: list = None, estimated_input_tokens: int = 0) -> dict:
        """自适应计算Token预算分配"""
        context_window = self.get_model_context_window()
        
        # 动态计算输出Token需求
        if batch_contents:
            n_texts = len(batch_contents)
            expected_nodes = n_texts * self.max_nodes_per_text
            expected_edges = expected_nodes  # 粗估1:1比例
            # 每个节点约25 tokens，每条边约20 tokens
            rough_output = expected_nodes * 25 + expected_edges * 20
            max_output_tokens = min(4096, int(rough_output * 1.2))  # 增加20%安全边距
        else:
            max_output_tokens = self.llm_max_output_tokens or int(context_window * self.llm_output_ratio)
        
        # 系统提示Token数（可以根据实际模板长度动态调整）
        system_prompt_tokens = self.llm_system_prompt_tokens or int(context_window * self.llm_system_ratio)
        
        # 计算可用于输入的Token数
        available_tokens = context_window - max_output_tokens - system_prompt_tokens
        
        # 自适应安全系数
        if self.adaptive_safety and estimated_input_tokens > 0:
            input_ratio = estimated_input_tokens / context_window
            if input_ratio < 0.5:
                safety_factor = self.max_safety_factor  # 低占用时更充分利用
            elif input_ratio > 0.8:
                safety_factor = self.min_safety_factor  # 高占用时更保守
            else:
                # 线性插值
                ratio = (input_ratio - 0.5) / 0.3  # 0.5-0.8 映射到 0-1
                safety_factor = self.max_safety_factor - ratio * (self.max_safety_factor - self.min_safety_factor)
        else:
            safety_factor = self.llm_safety_factor
        
        # 计算有效预算
        effective_budget = int(available_tokens * safety_factor)
        
        # 动态节点上限
        adaptive_max_nodes = self.max_nodes_per_text
        if self.adaptive_node_limit and estimated_input_tokens > 0:
            budget_ratio = estimated_input_tokens / effective_budget
            if budget_ratio > 0.6:
                adaptive_max_nodes = self.min_nodes_per_text
        
        return {
            "context_window": context_window,
            "max_output_tokens": max_output_tokens,
            "system_prompt_tokens": system_prompt_tokens,
            "available_tokens": available_tokens,
            "effective_budget": effective_budget,
            "safety_factor": safety_factor,
            "adaptive_max_nodes": adaptive_max_nodes,
            "estimated_input_tokens": estimated_input_tokens,
            "input_ratio": estimated_input_tokens / context_window if estimated_input_tokens > 0 else 0.0,
            "model_description": self.get_model_description()
        }

# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
settings.faiss_index_path.parent.mkdir(parents=True, exist_ok=True)
settings.embedding_cache_path.parent.mkdir(parents=True, exist_ok=True) 