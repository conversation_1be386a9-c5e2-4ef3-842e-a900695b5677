#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SubtaskExecutor 单元测试
测试子任务跳转功能、依赖关系处理、模拟数据生成等核心功能
"""

import unittest
import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
import json

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入测试目标
try:
    # 先导入配置以确保正确初始化
    from resona.config import settings
    from resona.models.user_models import ParsedQuery
    from resona.models.graph_models import UserGraph, GraphNode, NodeType
    from resona.pipeline import RedditResonancePipeline
    
    # 导入测试脚本中的类（需要动态导入）
    import importlib.util
    spec = importlib.util.spec_from_file_location(
        "test_step_by_step_debug", 
        os.path.join(os.path.dirname(__file__), "test_step_by_step_debug.py")
    )
    debug_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(debug_module)
    
    SubtaskExecutor = debug_module.SubtaskExecutor
    FastModeConfig = debug_module.FastModeConfig
    DetailedResultsCapture = debug_module.DetailedResultsCapture
    
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class TestSubtaskExecutor(unittest.TestCase):
    """SubtaskExecutor 单元测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_pipeline = Mock(spec=RedditResonancePipeline)
        
        # 模拟pipeline的异步方法
        self.mock_pipeline._task_a_parse_user_input = AsyncMock()
        self.mock_pipeline._task_b_reddit_search = AsyncMock()
        self.mock_pipeline._task_c_extract_commenters = AsyncMock()
        self.mock_pipeline._task_d_build_candidate_graphs_optimized = AsyncMock()
        self.mock_pipeline._task_e_build_user_graph = AsyncMock()
        self.mock_pipeline._task_f_analyze_completeness = AsyncMock()
        self.mock_pipeline._task_gh_match_and_analyze = AsyncMock()
        self.mock_pipeline._task_i_generate_recommendations = AsyncMock()
        
        # 创建测试用的SubtaskExecutor
        self.executor = SubtaskExecutor(self.mock_pipeline, fast_mode=False)
        self.fast_executor = SubtaskExecutor(self.mock_pipeline, fast_mode=True)
        
        # 测试数据
        self.test_prompt = "我对职业发展很困惑，需要一些建议。"
    
    def test_init_normal_mode(self):
        """测试正常模式初始化"""
        executor = SubtaskExecutor(self.mock_pipeline, fast_mode=False)
        self.assertEqual(executor.pipeline, self.mock_pipeline)
        self.assertFalse(executor.fast_mode)
        self.assertEqual(executor.mock_data, {})
        self.assertEqual(executor.execution_results, {})
    
    def test_init_fast_mode(self):
        """测试快速模式初始化"""
        # 保存原始配置
        original_config = {
            'post_search_limit': settings.post_search_limit,
            'reddit_history_limit': settings.reddit_history_limit,
        }
        
        try:
            executor = SubtaskExecutor(self.mock_pipeline, fast_mode=True)
            self.assertTrue(executor.fast_mode)
            
            # 验证快速模式配置是否应用
            self.assertEqual(settings.post_search_limit, 15)
            self.assertEqual(settings.reddit_history_limit, 20)
            
        finally:
            # 恢复原始配置
            for key, value in original_config.items():
                setattr(settings, key, value)
    
    def test_create_mock_data_normal_mode(self):
        """测试正常模式的模拟数据创建"""
        self.executor.create_mock_data()
        
        # 验证模拟的ParsedQuery
        parsed_query = self.executor.mock_data['parsed_query']
        self.assertIsInstance(parsed_query, ParsedQuery)
        self.assertEqual(parsed_query.original_text, "模拟用户输入：关于职业发展的困扰")
        self.assertIn("career", parsed_query.topics)
        self.assertIn("uncertainty", parsed_query.emotional_state)
        
        # 验证模拟的帖子数据
        relevant_posts = self.executor.mock_data['relevant_posts']
        self.assertEqual(len(relevant_posts), 10)  # 正常模式应该有10个帖子
        for post in relevant_posts:
            self.assertIn('id', post)
            self.assertIn('title', post)
            self.assertIn('subreddit', post)
            self.assertTrue(post['is_mock'])
        
        # 验证模拟的候选用户
        candidate_users = self.executor.mock_data['candidate_users']
        self.assertEqual(len(candidate_users), 5)  # 正常模式应该有5个用户
        for user in candidate_users:
            self.assertTrue(user.startswith('mock_user_'))
    
    def test_create_mock_data_fast_mode(self):
        """测试快速模式的模拟数据创建"""
        self.fast_executor.create_mock_data()
        
        # 验证快速模式的数据量减少
        relevant_posts = self.fast_executor.mock_data['relevant_posts']
        self.assertEqual(len(relevant_posts), 5)  # 快速模式应该有5个帖子
        
        candidate_users = self.fast_executor.mock_data['candidate_users']
        self.assertEqual(len(candidate_users), 3)  # 快速模式应该有3个用户
    
    async def test_ensure_dependencies_basic(self):
        """测试基本依赖确保功能"""
        # 创建mock的DetailedResultsCapture
        mock_capture = Mock()
        mock_capture.capture_task_a = Mock()
        mock_capture.capture_task_b = Mock()
        mock_capture.capture_task_c = Mock()
        
        # 测试确保parsed_query依赖
        await self.executor._ensure_dependencies(['parsed_query'], mock_capture)
        
        # 验证模拟数据已创建并添加到execution_results
        self.assertIn('parsed_query', self.executor.execution_results)
        self.assertIsInstance(self.executor.execution_results['parsed_query'], ParsedQuery)
        
        # 验证capture方法被调用
        mock_capture.capture_task_a.assert_called_once()
    
    async def test_ensure_dependencies_multiple(self):
        """测试多重依赖确保功能"""
        mock_capture = Mock()
        mock_capture.capture_task_a = Mock()
        mock_capture.capture_task_b = Mock()
        mock_capture.capture_task_c = Mock()
        
        # 测试确保多个依赖
        await self.executor._ensure_dependencies(
            ['parsed_query', 'relevant_posts', 'candidate_users'], 
            mock_capture
        )
        
        # 验证所有依赖都已添加
        self.assertIn('parsed_query', self.executor.execution_results)
        self.assertIn('relevant_posts', self.executor.execution_results)
        self.assertIn('candidate_users', self.executor.execution_results)
        
        # 验证所有capture方法都被调用
        mock_capture.capture_task_a.assert_called_once()
        mock_capture.capture_task_b.assert_called_once()
        mock_capture.capture_task_c.assert_called_once()
    
    async def test_ensure_dependencies_with_user_graph(self):
        """测试包含用户图谱的依赖确保"""
        mock_capture = Mock()
        mock_capture.capture_task_d = Mock()
        mock_capture.capture_task_e = Mock()
        
        # 测试确保图谱相关依赖
        await self.executor._ensure_dependencies(
            ['candidate_graphs', 'user_graph'], 
            mock_capture, 
            include_mock_user_graph=True
        )
        
        # 验证图谱数据已创建
        self.assertIn('candidate_graphs', self.executor.execution_results)
        self.assertIn('user_graph', self.executor.execution_results)
        
        # 验证用户图谱是UserGraph实例
        user_graph = self.executor.execution_results['user_graph']
        self.assertIsInstance(user_graph, UserGraph)
        self.assertEqual(user_graph.user_id, "mock_user")
        
        # 验证候选图谱结构
        candidate_graphs = self.executor.execution_results['candidate_graphs']
        self.assertIsInstance(candidate_graphs, list)
        for graph_data in candidate_graphs:
            self.assertIn('user_id', graph_data)
            self.assertIn('graph', graph_data)
            self.assertIn('nodes', graph_data['graph'])
            self.assertIn('edges', graph_data['graph'])
    
    async def test_execute_from_subtask_invalid(self):
        """测试无效子任务的处理"""
        result = await self.executor.execute_from_subtask('X', self.test_prompt)
        self.assertFalse(result)
    
    def test_subtask_validation(self):
        """测试子任务标识符验证"""
        valid_tasks = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
        invalid_tasks = ['X', 'Y', 'Z', '1', '!']
        
        for task in valid_tasks:
            # 这些应该对应有效的方法名
            method_name = f"_execute_from_{task.lower()}"
            self.assertTrue(hasattr(self.executor, method_name))
        
        for task in invalid_tasks:
            method_name = f"_execute_from_{task.lower()}"
            self.assertFalse(hasattr(self.executor, method_name))

class TestFastModeConfig(unittest.TestCase):
    """FastModeConfig 单元测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 保存原始配置
        self.original_config = {
            'reddit_search_limit': settings.reddit_search_limit,
            'reddit_history_limit': settings.reddit_history_limit,
            'post_search_limit': settings.post_search_limit,
            'max_quality_commenters': settings.max_quality_commenters,
            'top_k_matches': settings.top_k_matches,
            'embedding_rerank_count': settings.embedding_rerank_count,
            'llm_final_rank_count': settings.llm_final_rank_count,
        }
    
    def tearDown(self):
        """测试后清理"""
        # 恢复原始配置
        for key, value in self.original_config.items():
            setattr(settings, key, value)
    
    def test_apply_fast_config(self):
        """测试快速模式配置应用"""
        FastModeConfig.apply_fast_config()
        
        # 验证快速模式配置是否正确应用
        self.assertEqual(settings.reddit_search_limit, 10)
        self.assertEqual(settings.reddit_history_limit, 20)
        self.assertEqual(settings.post_search_limit, 15)
        self.assertEqual(settings.max_quality_commenters, 5)
        self.assertEqual(settings.top_k_matches, 2)
        self.assertEqual(settings.embedding_rerank_count, 8)
        self.assertEqual(settings.llm_final_rank_count, 4)
        
        # 验证原始配置已保存
        self.assertTrue(hasattr(FastModeConfig, 'original_config'))
        self.assertEqual(FastModeConfig.original_config['post_search_limit'], self.original_config['post_search_limit'])
    
    def test_restore_original_config(self):
        """测试原始配置恢复"""
        # 先应用快速配置
        FastModeConfig.apply_fast_config()
        
        # 验证快速配置已应用
        self.assertEqual(settings.post_search_limit, 15)
        
        # 恢复原始配置
        FastModeConfig.restore_original_config()
        
        # 验证原始配置已恢复
        self.assertEqual(settings.post_search_limit, self.original_config['post_search_limit'])
        self.assertEqual(settings.reddit_history_limit, self.original_config['reddit_history_limit'])
    
    def test_config_reduction_percentages(self):
        """测试配置减少百分比是否符合预期"""
        FastModeConfig.apply_fast_config()
        
        # 验证减少百分比
        reddit_reduction = (self.original_config['reddit_search_limit'] - settings.reddit_search_limit) / self.original_config['reddit_search_limit']
        history_reduction = (self.original_config['reddit_history_limit'] - settings.reddit_history_limit) / self.original_config['reddit_history_limit']
        post_reduction = (self.original_config['post_search_limit'] - settings.post_search_limit) / self.original_config['post_search_limit']
        
        # 验证减少幅度在合理范围内（至少50%减少）
        self.assertGreater(reddit_reduction, 0.5, "Reddit搜索限制应至少减少50%")
        self.assertGreater(history_reduction, 0.5, "Reddit历史限制应至少减少50%")
        self.assertGreater(post_reduction, 0.5, "帖子搜索限制应至少减少50%")

class TestDetailedResultsCapture(unittest.TestCase):
    """DetailedResultsCapture 单元测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.capture = DetailedResultsCapture()
        self.test_prompt = "测试用户输入"
        self.test_session_id = "test_session_123"
    
    def test_init(self):
        """测试初始化"""
        self.assertIsInstance(self.capture.results, dict)
        self.assertIn('session_info', self.capture.results)
        self.assertIn('task_a_details', self.capture.results)
        self.assertIsInstance(self.capture.start_time, datetime)
    
    def test_set_session_info(self):
        """测试会话信息设置"""
        self.capture.set_session_info(self.test_prompt, self.test_session_id)
        
        session_info = self.capture.results['session_info']
        self.assertEqual(session_info['user_prompt'], self.test_prompt)
        self.assertEqual(session_info['session_id'], self.test_session_id)
        self.assertEqual(session_info['test_mode'], 'step_by_step_debug')
        self.assertIn('start_time', session_info)
    
    def test_capture_task_a(self):
        """测试子任务A结果捕获"""
        # 创建模拟的ParsedQuery
        mock_query = Mock()
        mock_query.search_keywords = ['test', 'keyword']
        mock_query.topics = ['career']
        mock_query.emotional_state = {'anxiety': 0.7}
        mock_query.values_info = {'stability': 0.8}
        mock_query.confidence = 0.75
        mock_query.core_concerns = ['职业发展']
        mock_query.decision_points = ['考研vs工作']
        mock_query.life_domains = ['职业']
        mock_query.support_needs = ['建议']
        mock_query.original_text = self.test_prompt
        
        # 使用getattr模拟属性访问
        def mock_getattr(obj, name, default=None):
            if hasattr(mock_query, name):
                return getattr(mock_query, name)
            return default
        
        with patch('builtins.getattr', side_effect=mock_getattr):
            self.capture.capture_task_a(mock_query)
        
        task_a = self.capture.results['task_a_details']
        self.assertEqual(task_a['task_name'], '用户输入语义解析')
        self.assertIn('execution_time', task_a)
        self.assertEqual(task_a['search_keywords'], ['test', 'keyword'])
        self.assertEqual(task_a['topics'], ['career'])
    
    def test_capture_task_b(self):
        """测试子任务B结果捕获"""
        relevant_posts = [
            {
                'id': 'post1',
                'title': '测试帖子1',
                'subreddit': 'test',
                'score': 10,
                'quality_score': 0.8,
                'embedding_similarity': 0.9,
                'llm_rank_score': 0.85,
                'relevance_reason': '高度相关'
            },
            {
                'id': 'post2', 
                'title': '测试帖子2',
                'subreddit': 'test2',
                'score': 15,
                'quality_score': 0.7
            }
        ]
        search_keywords = ['test', 'career']
        
        self.capture.capture_task_b(relevant_posts, search_keywords)
        
        task_b = self.capture.results['task_b_details']
        self.assertEqual(task_b['task_name'], 'Reddit搜索与候选人提取（多阶段）')
        self.assertEqual(task_b['search_keywords'], search_keywords)
        self.assertEqual(task_b['total_posts_found'], 2)
        self.assertIn('stage_1_keyword_search', task_b)
        self.assertIn('stage_2_embedding_rerank', task_b)
        self.assertIn('stage_3_llm_final_rank', task_b)
    
    def test_finalize_results(self):
        """测试结果完成处理"""
        final_result = {
            'success': True,
            'stats': {'posts': 10, 'users': 5},
            'error': None
        }
        
        self.capture.finalize_results(final_result)
        
        final_summary = self.capture.results['final_summary']
        self.assertTrue(final_summary['success'])
        self.assertIn('total_execution_time', final_summary)
        self.assertIn('end_time', final_summary)
        self.assertEqual(final_summary['final_stats'], {'posts': 10, 'users': 5})

class TestDatetimeSerializationFix(unittest.TestCase):
    """datetime序列化修复单元测试"""
    
    def test_json_serial_default_datetime(self):
        """测试datetime对象序列化"""
        from resona.utils.detailed_results_manager import json_serial_default
        
        test_datetime = datetime(2025, 6, 30, 16, 30, 45, 123456)
        result = json_serial_default(test_datetime)
        
        self.assertEqual(result, "2025-06-30T16:30:45.123456")
        self.assertIsInstance(result, str)
    
    def test_json_serial_default_date(self):
        """测试date对象序列化"""
        from resona.utils.detailed_results_manager import json_serial_default
        from datetime import date
        
        test_date = date(2025, 6, 30)
        result = json_serial_default(test_date)
        
        self.assertEqual(result, "2025-06-30")
        self.assertIsInstance(result, str)
    
    def test_json_serial_default_invalid_type(self):
        """测试无效类型的处理"""
        from resona.utils.detailed_results_manager import json_serial_default
        
        with self.assertRaises(TypeError) as context:
            json_serial_default(object())
        
        self.assertIn("is not JSON serializable", str(context.exception))
    
    def test_json_dumps_with_datetime(self):
        """测试包含datetime的JSON序列化"""
        from resona.utils.detailed_results_manager import json_serial_default
        
        test_data = {
            "timestamp": datetime.now(),
            "nested": {
                "created_at": datetime(2025, 6, 30, 12, 0, 0),
                "metadata": {
                    "last_updated": datetime.now()
                }
            },
            "items": [
                {"date": datetime.now(), "value": 123},
                {"date": datetime.now(), "value": 456}
            ],
            "simple_field": "test"
        }
        
        # 这应该不会抛出异常
        json_str = json.dumps(test_data, default=json_serial_default, ensure_ascii=False, indent=2)
        
        # 验证可以反序列化
        parsed_data = json.loads(json_str)
        
        # 验证结构保持不变
        self.assertIn("timestamp", parsed_data)
        self.assertIn("nested", parsed_data)
        self.assertIn("items", parsed_data)
        self.assertEqual(parsed_data["simple_field"], "test")
        
        # 验证datetime已转换为字符串
        self.assertIsInstance(parsed_data["timestamp"], str)
        self.assertIsInstance(parsed_data["nested"]["created_at"], str)

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    async def test_subtask_executor_integration(self):
        """测试SubtaskExecutor集成功能"""
        # 创建真实的pipeline mock
        mock_pipeline = Mock(spec=RedditResonancePipeline)
        
        # 设置返回值
        mock_parsed_query = Mock(spec=ParsedQuery)
        mock_parsed_query.get_search_keywords.return_value = ['test', 'career']
        mock_pipeline._task_a_parse_user_input = AsyncMock(return_value=mock_parsed_query)
        
        mock_posts = [{"id": "test1", "title": "Test Post"}]
        mock_pipeline._task_b_reddit_search = AsyncMock(return_value=mock_posts)
        
        mock_users = ["user1", "user2"]
        mock_pipeline._task_c_extract_commenters = AsyncMock(return_value=mock_users)
        
        # 创建executor并测试
        executor = SubtaskExecutor(mock_pipeline, fast_mode=True)
        
        # 测试依赖确保功能
        mock_capture = Mock()
        mock_capture.capture_task_a = Mock()
        mock_capture.capture_task_b = Mock()
        
        await executor._ensure_dependencies(['parsed_query', 'relevant_posts'], mock_capture)
        
        # 验证模拟数据已正确创建
        self.assertIn('parsed_query', executor.execution_results)
        self.assertIn('relevant_posts', executor.execution_results)
        
        # 验证capture方法被调用
        mock_capture.capture_task_a.assert_called_once()
        mock_capture.capture_task_b.assert_called_once()

def run_test_suite():
    """运行完整的测试套件"""
    print("🧪 开始运行SubtaskExecutor单元测试套件...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestSubtaskExecutor,
        TestFastModeConfig,
        TestDetailedResultsCapture,
        TestDatetimeSerializationFix,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    # 输出结果摘要
    print("\n" + "=" * 60)
    print("🎯 测试结果摘要:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n⚠️  错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Error:')[-1].strip()}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n✅ 测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    # 由于包含异步测试，需要特殊处理
    def run_async_tests():
        """运行包含异步方法的测试"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            success = run_test_suite()
            return success
        finally:
            loop.close()
    
    success = run_async_tests()
    sys.exit(0 if success else 1) 