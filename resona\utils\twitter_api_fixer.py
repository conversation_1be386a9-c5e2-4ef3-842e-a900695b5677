"""
Twitter API Schema ID 自动修复工具
检测snscrape的schema ID是否失效，如果失效则自动获取新的ID并替换
"""
import os
import re
import requests
import logging
from pathlib import Path
from typing import Optional, Dict
import json

logger = logging.getLogger(__name__)

class TwitterAPIFixer:
    """Twitter API Schema ID 修复器"""
    
    def __init__(self):
        self.snscrape_twitter_path = self._find_snscrape_twitter_file()
        self.current_schema_id = None
        self.new_schema_id = None
        
    def _find_snscrape_twitter_file(self) -> Optional[Path]:
        """查找snscrape的twitter.py文件"""
        try:
            import snscrape
            snscrape_dir = Path(snscrape.__file__).parent
            twitter_file = snscrape_dir / 'modules' / 'twitter.py'
            
            if twitter_file.exists():
                return twitter_file
            else:
                logger.error(f"未找到twitter.py文件: {twitter_file}")
                return None
                
        except ImportError:
            logger.error("snscrape未安装")
            return None
    
    def extract_current_schema_id(self) -> Optional[str]:
        """从twitter.py文件中提取当前的schema ID"""
        if not self.snscrape_twitter_path:
            return None
            
        try:
            with open(self.snscrape_twitter_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 搜索SearchTimeline URL中的schema ID
            pattern = r'https://twitter\.com/i/api/graphql/([A-Za-z0-9_-]+)/SearchTimeline'
            match = re.search(pattern, content)
            
            if match:
                self.current_schema_id = match.group(1)
                logger.info(f"发现当前schema ID: {self.current_schema_id}")
                return self.current_schema_id
            else:
                logger.error("未找到SearchTimeline schema ID")
                return None
                
        except Exception as e:
            logger.error(f"读取twitter.py文件失败: {e}")
            return None
    
    def test_schema_id(self, schema_id: str) -> bool:
        """测试schema ID是否可用"""
        try:
            # 构建测试URL
            test_url = f"https://twitter.com/i/api/graphql/{schema_id}/SearchTimeline"
            
            # 配置代理（如果需要）
            proxies = self._get_proxy_config()
            
            # 模拟基本的Twitter请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': '*/*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Origin': 'https://twitter.com',
                'Referer': 'https://twitter.com/',
            }
            
            # 发送HEAD请求测试端点是否存在
            response = requests.head(
                test_url,
                headers=headers,
                proxies=proxies,
                timeout=10,
                params={
                    'variables': '{"rawQuery":"test","count":1,"product":"Latest"}',
                    'features': '{}'
                }
            )
            
            # 如果返回200、401或429，说明端点存在（只是缺少认证）
            # 如果返回404，说明端点不存在（schema ID过期）
            if response.status_code in [200, 401, 429]:
                logger.info(f"Schema ID {schema_id} 可用 (状态码: {response.status_code})")
                return True
            elif response.status_code == 404:
                logger.warning(f"Schema ID {schema_id} 已过期 (404)")
                return False
            else:
                logger.warning(f"Schema ID {schema_id} 测试结果不确定 (状态码: {response.status_code})")
                return False
                
        except Exception as e:
            logger.error(f"测试schema ID失败: {e}")
            return False
    
    def fetch_new_schema_id(self) -> Optional[str]:
        """从Twitter网页中获取新的schema ID"""
        try:
            proxies = self._get_proxy_config()
            
            # 访问Twitter搜索页面
            response = requests.get(
                'https://twitter.com/search?q=test&src=typed_query',
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                proxies=proxies,
                timeout=15
            )
            
            if response.status_code == 200:
                content = response.text
                
                # 搜索JavaScript中的schema ID
                # Twitter通常在JS代码中定义这些ID
                patterns = [
                    r'"SearchTimeline"[^}]*?"queryId":"([A-Za-z0-9_-]+)"',
                    r'SearchTimeline[^}]*?queryId[^}]*?([A-Za-z0-9_-]{22})',
                    r'graphql/([A-Za-z0-9_-]{22})/SearchTimeline',
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if len(match) == 22:  # Twitter schema IDs are typically 22 chars
                            logger.info(f"发现候选schema ID: {match}")
                            if self.test_schema_id(match):
                                self.new_schema_id = match
                                return match
                
                logger.warning("未在页面中找到有效的schema ID")
                return None
            else:
                logger.error(f"无法访问Twitter页面: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取新schema ID失败: {e}")
            return None
    
    def _get_proxy_config(self) -> Dict:
        """获取代理配置"""
        import os
        
        # 检查环境变量
        http_proxy = os.getenv('HTTP_PROXY') or os.getenv('http_proxy')
        https_proxy = os.getenv('HTTPS_PROXY') or os.getenv('https_proxy')
        
        if http_proxy or https_proxy:
            return {
                'http': http_proxy or 'http://127.0.0.1:7890',
                'https': https_proxy or 'http://127.0.0.1:7890'
            }
        else:
            # 默认使用Clash代理
            return {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }
    
    def replace_schema_id(self, old_id: str, new_id: str) -> bool:
        """替换twitter.py文件中的schema ID"""
        if not self.snscrape_twitter_path:
            return False
            
        try:
            # 备份原文件
            backup_path = self.snscrape_twitter_path.with_suffix('.py.backup')
            with open(self.snscrape_twitter_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            logger.info(f"已备份原文件到: {backup_path}")
            
            # 替换schema ID
            new_content = original_content.replace(old_id, new_id)
            
            # 验证替换是否成功
            if old_id in new_content:
                logger.error("Schema ID替换失败")
                return False
            
            if new_id not in new_content:
                logger.error("新schema ID未正确插入")
                return False
            
            # 写入新文件
            with open(self.snscrape_twitter_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.info(f"成功将schema ID从 {old_id} 替换为 {new_id}")
            return True
            
        except Exception as e:
            logger.error(f"替换schema ID失败: {e}")
            return False
    
    def fix_twitter_api(self) -> bool:
        """执行完整的Twitter API修复流程"""
        logger.info("开始Twitter API自动修复...")
        
        # 1. 提取当前schema ID
        current_id = self.extract_current_schema_id()
        if not current_id:
            logger.error("无法提取当前schema ID")
            return False
        
        # 2. 测试当前schema ID是否可用
        if self.test_schema_id(current_id):
            logger.info("当前schema ID仍然可用，无需修复")
            return True
        
        logger.warning("当前schema ID已失效，尝试获取新的ID...")
        
        # 3. 获取新的schema ID
        new_id = self.fetch_new_schema_id()
        if not new_id:
            logger.error("无法获取新的schema ID")
            return False
        
        # 4. 替换schema ID
        if self.replace_schema_id(current_id, new_id):
            logger.info("Twitter API修复完成！")
            return True
        else:
            logger.error("Twitter API修复失败")
            return False

def auto_fix_twitter_api() -> bool:
    """自动修复Twitter API的便捷函数"""
    fixer = TwitterAPIFixer()
    return fixer.fix_twitter_api()

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 执行修复
    success = auto_fix_twitter_api()
    exit(0 if success else 1) 