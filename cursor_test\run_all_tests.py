#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试套件运行器
运行所有专业单元测试，提供完整的测试报告
"""

import unittest
import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_all_tests():
    """运行所有测试套件"""
    print("🚀 开始运行CogBridges项目完整测试套件")
    print("=" * 80)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    start_time = time.time()
    
    # 测试套件列表
    test_modules = [
        {
            'name': '组件基础功能测试',
            'module': 'cursor_test.test_components',
            'description': '测试datetime序列化、配置系统、模型导入等核心功能'
        },
        {
            'name': '模拟数据生成测试', 
            'module': 'cursor_test.test_mock_data_generation',
            'description': '测试ParsedQuery、Reddit帖子、图谱等模拟数据生成'
        }
    ]
    
    total_tests = 0
    total_failures = 0
    total_errors = 0
    all_results = []
    
    # 运行每个测试模块
    for i, test_info in enumerate(test_modules, 1):
        print(f"\n📋 [{i}/{len(test_modules)}] 运行 {test_info['name']}")
        print(f"📝 描述: {test_info['description']}")
        print("-" * 60)
        
        try:
            # 动态导入测试模块
            module = __import__(test_info['module'], fromlist=[''])
            
            # 创建测试套件
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromModule(module)
            
            # 运行测试
            runner = unittest.TextTestRunner(verbosity=1, stream=sys.stdout)
            result = runner.run(suite)
            
            # 记录结果
            module_result = {
                'name': test_info['name'],
                'tests_run': result.testsRun,
                'failures': len(result.failures),
                'errors': len(result.errors),
                'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100 if result.testsRun > 0 else 0,
                'details': result
            }
            all_results.append(module_result)
            
            total_tests += result.testsRun
            total_failures += len(result.failures)
            total_errors += len(result.errors)
            
            # 输出模块结果
            print(f"\n✅ {test_info['name']} 完成:")
            print(f"   测试数: {result.testsRun}")
            print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
            print(f"   失败: {len(result.failures)}")
            print(f"   错误: {len(result.errors)}")
            print(f"   成功率: {module_result['success_rate']:.1f}%")
            
        except Exception as e:
            print(f"❌ 运行 {test_info['name']} 时出错: {e}")
            module_result = {
                'name': test_info['name'],
                'tests_run': 0,
                'failures': 0,
                'errors': 1,
                'success_rate': 0.0,
                'error_msg': str(e)
            }
            all_results.append(module_result)
            total_errors += 1
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    # 生成综合报告
    print("\n" + "=" * 80)
    print("📊 测试套件综合报告")
    print("=" * 80)
    
    print(f"🕒 总执行时间: {execution_time:.2f} 秒")
    print(f"📈 总测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   成功: {total_tests - total_failures - total_errors}")
    print(f"   失败: {total_failures}")
    print(f"   错误: {total_errors}")
    
    overall_success_rate = (total_tests - total_failures - total_errors) / total_tests * 100 if total_tests > 0 else 0
    print(f"   总成功率: {overall_success_rate:.1f}%")
    
    print(f"\n📋 各模块详细结果:")
    for result in all_results:
        status = "✅" if result['success_rate'] == 100.0 else "⚠️" if result['success_rate'] >= 80.0 else "❌"
        print(f"   {status} {result['name']}: {result['success_rate']:.1f}% ({result['tests_run']} 测试)")
    
    # 失败和错误详情
    if total_failures > 0 or total_errors > 0:
        print(f"\n⚠️ 详细问题报告:")
        for result in all_results:
            if 'details' in result and result['details']:
                details = result['details']
                if details.failures:
                    print(f"\n❌ {result['name']} - 失败的测试:")
                    for test, traceback in details.failures:
                        print(f"   - {test}")
                        # 提取失败原因
                        if 'AssertionError:' in traceback:
                            reason = traceback.split('AssertionError:')[-1].strip()
                            print(f"     原因: {reason}")
                
                if details.errors:
                    print(f"\n⚠️ {result['name']} - 错误的测试:")
                    for test, traceback in details.errors:
                        print(f"   - {test}")
                        # 提取错误信息
                        lines = traceback.strip().split('\n')
                        error_line = lines[-1] if lines else "未知错误"
                        print(f"     错误: {error_line}")
            
            elif 'error_msg' in result:
                print(f"   ❌ {result['name']}: {result['error_msg']}")
    
    # 建议和总结
    print(f"\n💡 测试建议:")
    if overall_success_rate == 100.0:
        print("   🎉 所有测试都通过了！系统功能完整且稳定。")
        print("   ✨ 可以安全地运行主要功能和部署系统。")
    elif overall_success_rate >= 90.0:
        print("   👍 测试通过率很高，系统基本稳定。")
        print("   🔧 建议修复少量失败的测试以提高系统可靠性。")
    elif overall_success_rate >= 70.0:
        print("   ⚠️ 测试通过率中等，需要关注失败的测试。")
        print("   🛠️ 建议优先修复核心功能相关的失败测试。")
    else:
        print("   ❌ 测试通过率较低，系统可能存在严重问题。")
        print("   🚨 强烈建议在使用前修复大部分失败的测试。")
    
    print(f"\n📝 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 返回是否所有测试都通过
    return total_failures == 0 and total_errors == 0

if __name__ == "__main__":
    success = run_all_tests()
    
    # 根据测试结果设置退出码
    exit_code = 0 if success else 1
    
    if success:
        print("🎊 恭喜！所有测试都成功通过！")
    else:
        print("❌ 有测试失败，请查看上述报告进行修复。")
    
    sys.exit(exit_code) 