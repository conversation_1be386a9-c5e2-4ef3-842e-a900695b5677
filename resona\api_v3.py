"""
Reddit 共鸣用户搜索推荐系统 API v3.0
按照业务架构图实现的全新API接口
"""
import logging
import traceback
from contextlib import asynccontextmanager
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
from datetime import datetime
import os
from pathlib import Path

from .pipeline import RedditResonancePipeline
from .config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO if settings.debug else logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 全局流水线实例
pipeline = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global pipeline
    
    # 启动时初始化
    logger.info("初始化 Reddit 共鸣推荐系统 v3.0...")
    try:
        pipeline = RedditResonancePipeline()
        logger.info("流水线初始化完成")
    except Exception as e:
        logger.error(f"流水线初始化失败: {e}")
        pipeline = None
    
    yield
    
    # 关闭时清理
    logger.info("关闭 Reddit 共鸣推荐系统...")
    if pipeline:
        try:
            await pipeline.close()
            logger.info("流水线清理完成")
        except Exception as e:
            logger.error(f"流水线清理失败: {e}")

# 创建 FastAPI 应用
app = FastAPI(
    title="Reddit 共鸣用户搜索推荐系统",
    description="基于结构化图谱的Reddit用户共鸣匹配与推荐平台",
    version="3.0.0",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置静态文件服务
frontend_path = Path(__file__).parent / "frontend"
if frontend_path.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_path)), name="static")

# 请求模型
class RedditResonanceRequest(BaseModel):
    """Reddit共鸣搜索请求"""
    text: str = Field(..., min_length=10, max_length=2000, description="用户困扰描述（10-2000字符）")
    additional_contents: Optional[List[str]] = Field(default=None, description="用户额外提供的内容")
    max_recommendations: Optional[int] = Field(default=5, ge=1, le=10, description="最大推荐数量（1-10）")
    
    class Config:
        json_schema_extra = {
            "example": {
                "text": "我最近在纠结是否辞职去读研，内心很不安。工作了3年，虽然薪资还可以，但感觉没有太大成长空间。家里人希望我继续工作稳定一些，但我自己很想深造提升。",
                "additional_contents": ["我是计算机专业毕业的，目前在一家中等规模的公司做开发"],
                "max_recommendations": 5
            }
        }

class GraphCompletenessRequest(BaseModel):
    """图谱完整性分析请求"""
    text: str = Field(..., min_length=10, description="用户输入文本")
    additional_contents: Optional[List[str]] = Field(default=None, description="额外内容")

# 响应模型
class RecommendationItem(BaseModel):
    """推荐项"""
    rank: int = Field(..., description="推荐排名")
    candidate_id: str = Field(..., description="候选者Reddit用户名")
    resonance_score: float = Field(..., description="共鸣分数（0-1）")
    recommendation_strength: str = Field(..., description="推荐强度")
    summary: str = Field(..., description="推荐摘要")
    reasoning: str = Field(..., description="匹配原因分析")
    shared_themes: List[str] = Field(..., description="共同话题")
    conversation_starters: List[str] = Field(..., description="对话开场语建议")
    reddit_profile_url: str = Field(..., description="Reddit个人页面链接")
    content_stats: Dict[str, Any] = Field(..., description="内容统计信息")
    matching_details: Dict[str, float] = Field(..., description="匹配详情评分")

class CompletenessInfo(BaseModel):
    """图谱完整性信息"""
    completeness_score: float = Field(..., description="完整性分数（0-1）")
    is_complete: bool = Field(..., description="是否完整")
    follow_up_questions: List[str] = Field(..., description="追问问题")
    missing_aspects: List[str] = Field(..., description="缺失方面")

class PipelineStats(BaseModel):
    """流水线统计信息"""
    relevant_posts_found: int = Field(..., description="找到的相关帖子数")
    candidate_users_found: int = Field(..., description="找到的候选用户数")
    successful_graphs: int = Field(..., description="成功构建的图谱数")
    final_recommendations: int = Field(..., description="最终推荐数")

class RedditResonanceResponse(BaseModel):
    """Reddit共鸣搜索响应"""
    success: bool = Field(..., description="执行是否成功")
    session_id: str = Field(..., description="会话ID")
    user_prompt: str = Field(..., description="用户原始输入")
    processing_time: float = Field(..., description="处理时间（秒）")
    recommendations: List[RecommendationItem] = Field(..., description="推荐列表")
    completeness_info: CompletenessInfo = Field(..., description="图谱完整性信息")
    stats: PipelineStats = Field(..., description="流水线统计")
    error: Optional[str] = Field(default=None, description="错误信息（如果有）")

class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="服务版本")
    timestamp: datetime = Field(..., description="检查时间")
    pipeline_ready: bool = Field(..., description="流水线是否就绪")

# API 路由
@app.get("/", summary="服务根路径")
async def root():
    """服务根路径 - 提供前端页面或服务信息"""
    from fastapi.responses import FileResponse
    
    # 尝试提供前端页面
    frontend_file = Path(__file__).parent / "frontend" / "index.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    
    # 如果前端文件不存在，返回API信息
    return {
        "service": "Reddit 共鸣用户搜索推荐系统",
        "version": "3.0.0",
        "description": "基于结构化图谱的Reddit用户共鸣匹配与推荐平台",
        "endpoints": {
            "搜索推荐": "/api/v3/search",
            "完整性分析": "/api/v3/completeness",
            "健康检查": "/health",
            "API文档": "/docs",
            "OpenAPI规范": "/openapi.json"
        }
    }

@app.get("/health", response_model=HealthResponse, summary="健康检查")
async def health_check():
    """健康检查端点"""
    return HealthResponse(
        status="healthy",
        service="reddit-resonance-system",
        version="3.0.0",
        timestamp=datetime.now(),
        pipeline_ready=pipeline is not None
    )

@app.post("/api/v3/search", response_model=RedditResonanceResponse, summary="Reddit共鸣用户搜索")
async def search_resonant_users(request: RedditResonanceRequest):
    """
    执行Reddit共鸣用户搜索和推荐
    
    ## 功能说明
    
    根据用户的困扰描述，执行以下9个子任务的完整流水线：
    
    1. **语义解析** - 将自然语言拆分为搜索关键词和价值观信息
    2. **Reddit搜索** - 在相关subreddit中搜索语义相关帖子
    3. **评论者提取** - 从帖子中提取优质活跃评论者
    4. **语料收集** - 获取候选者的历史发帖和评论内容
    5. **图谱构建** - 为每个候选者构建结构化认知图谱
    6. **用户画像** - 构建用户自己的三观图谱
    7. **完整性分析** - 识别图谱缺失，生成针对性追问
    8. **共鸣匹配** - 多维度计算用户与候选者的共鸣度
    9. **推荐展示** - 生成个性化推荐和对话建议
    
    ## 返回结果
    
    - 推荐的Reddit用户列表（按共鸣分数排序）
    - 每个推荐的详细匹配原因和对话建议
    - 用户图谱完整性分析和追问建议
    - 详细的流水线执行统计信息
    """
    
    if not pipeline:
        raise HTTPException(
            status_code=503, 
            detail="推荐系统尚未初始化完成，请稍后重试"
        )
    
    start_time = datetime.now()
    
    try:
        logger.info(f"收到Reddit共鸣搜索请求: {request.text[:100]}...")
        
        # 执行完整流水线
        result = await pipeline.execute_full_pipeline(
            user_prompt=request.text,
            additional_contents=request.additional_contents,
            max_recommendations=request.max_recommendations
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=f"流水线执行失败: {result.get('error', '未知错误')}"
            )
        
        # 转换为响应格式
        recommendations = []
        for rec in result.get("recommendations", []):
            recommendations.append(RecommendationItem(**rec))
        
        completeness_info = CompletenessInfo(**result.get("completeness_info", {
            "completeness_score": 0.7,
            "is_complete": True,
            "follow_up_questions": [],
            "missing_aspects": []
        }))
        
        stats = PipelineStats(**result.get("stats", {
            "relevant_posts_found": 0,
            "candidate_users_found": 0,
            "successful_graphs": 0,
            "final_recommendations": 0
        }))
        
        response = RedditResonanceResponse(
            success=True,
            session_id=result.get("session_id", "unknown"),
            user_prompt=request.text,
            processing_time=result.get("processing_time", 0.0),
            recommendations=recommendations,
            completeness_info=completeness_info,
            stats=stats
        )
        
        logger.info(f"请求处理完成，耗时: {response.processing_time:.2f}秒，推荐数: {len(recommendations)}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"处理请求时发生未预期错误: {e}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        
        # 返回错误响应
        return RedditResonanceResponse(
            success=False,
            session_id=f"error_{int(start_time.timestamp())}",
            user_prompt=request.text,
            processing_time=processing_time,
            recommendations=[],
            completeness_info=CompletenessInfo(
                completeness_score=0.0,
                is_complete=False,
                follow_up_questions=[],
                missing_aspects=[]
            ),
            stats=PipelineStats(
                relevant_posts_found=0,
                candidate_users_found=0,
                successful_graphs=0,
                final_recommendations=0
            ),
            error=str(e)
        )

@app.post("/api/v3/completeness", summary="图谱完整性分析")
async def analyze_completeness(request: GraphCompletenessRequest):
    """
    分析用户输入的图谱完整性
    
    ## 功能说明
    
    仅执行用户图谱构建和完整性分析，不进行完整的推荐流程。
    适用于：
    - 快速评估用户输入的信息完整度
    - 生成针对性追问问题
    - 预览图谱构建质量
    
    ## 返回结果
    
    - 图谱完整性分数
    - 识别的缺失方面
    - 建议的追问问题
    - 用户图谱基本统计
    """
    
    if not pipeline:
        raise HTTPException(
            status_code=503, 
            detail="分析系统尚未初始化完成，请稍后重试"
        )
    
    try:
        logger.info(f"收到图谱完整性分析请求: {request.text[:100]}...")
        
        # 构建用户图谱
        all_contents = [request.text]
        if request.additional_contents:
            all_contents.extend(request.additional_contents)
        
        user_graph = await pipeline.graph_builder.build_user_graph(
            contents=all_contents,
            user_context="用户画像分析"
        )
        
        # 分析完整性（使用空的候选图谱列表）
        completeness_result = await pipeline.user_profiler.analyze_completeness(
            user_graph=user_graph,
            reference_graphs=[]
        )
        
        # 添加图谱统计信息
        completeness_result['graph_stats'] = {
            'total_nodes': len(user_graph.nodes),
            'total_edges': len(user_graph.edges),
            'node_types': {},
            'creation_time': user_graph.created_at.isoformat() if user_graph.created_at else None
        }
        
        # 统计节点类型
        for node in user_graph.nodes:
            node_type = node.node_type.value if hasattr(node.node_type, 'value') else str(node.node_type)
            completeness_result['graph_stats']['node_types'][node_type] = \
                completeness_result['graph_stats']['node_types'].get(node_type, 0) + 1
        
        logger.info(f"完整性分析完成，分数: {completeness_result.get('completeness_score', 0):.2f}")
        return completeness_result
        
    except Exception as e:
        logger.error(f"完整性分析失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"完整性分析失败: {str(e)}"
        )

@app.get("/api/v3/stats", summary="系统统计信息")
async def get_system_stats():
    """
    获取系统运行统计信息
    
    返回各个组件的状态和统计数据
    """
    
    if not pipeline:
        raise HTTPException(
            status_code=503, 
            detail="系统尚未初始化完成"
        )
    
    try:
        stats = {
            "service_info": {
                "name": "Reddit 共鸣用户搜索推荐系统",
                "version": "3.0.0",
                "uptime": datetime.now().isoformat()
            },
            "pipeline_components": {
                "ai_service": "已初始化",
                "reddit_service": "已初始化", 
                "semantic_analyzer": "已初始化",
                "graph_builder": "已初始化",
                "user_profiler": "已初始化",
                "resonance_matcher": "已初始化"
            },
            "configuration": {
                "embedding_model": getattr(settings, 'embedding_model', 'unknown'),
                "llm_model": getattr(settings, 'llm_model', 'unknown'),
                "reddit_subreddits": getattr(settings, 'reddit_subreddits', []),
                "debug_mode": settings.debug
            }
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取统计信息失败: {str(e)}"
        )

@app.get("/api/v3/pipeline/status", summary="流水线状态检查")
async def get_pipeline_status():
    """
    检查流水线各个组件的详细状态
    """
    
    if not pipeline:
        return {
            "pipeline_initialized": False,
            "error": "流水线尚未初始化"
        }
    
    try:
        # 测试各个组件
        component_status = {}
        
        # 测试AI服务
        try:
            ai_connection = await pipeline.ai_service.test_connection()
            component_status['ai_service'] = {
                "status": "healthy" if ai_connection else "unhealthy",
                "connection_test": ai_connection
            }
        except Exception as e:
            component_status['ai_service'] = {
                "status": "error",
                "error": str(e)
            }
        
        # 测试Reddit服务
        try:
            reddit_connection = await pipeline.reddit_service.test_connection()
            component_status['reddit_service'] = {
                "status": "healthy" if reddit_connection else "unhealthy",
                "connection_test": reddit_connection
            }
        except Exception as e:
            component_status['reddit_service'] = {
                "status": "error", 
                "error": str(e)
            }
        
        # 其他组件状态
        component_status['semantic_analyzer'] = {"status": "ready"}
        component_status['graph_builder'] = {"status": "ready"}
        component_status['user_profiler'] = {"status": "ready"}
        component_status['resonance_matcher'] = {"status": "ready"}
        
        return {
            "pipeline_initialized": True,
            "overall_status": "healthy",
            "component_status": component_status,
            "check_time": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"流水线状态检查失败: {e}")
        return {
            "pipeline_initialized": True,
            "overall_status": "error",
            "error": str(e),
            "check_time": datetime.now().isoformat()
        }

# 错误处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    logger.error(f"异常堆栈: {traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "服务器内部错误",
            "detail": str(exc) if settings.debug else "请联系管理员",
            "timestamp": datetime.now().isoformat()
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api_v3:app",
        host=settings.host,
        port=settings.port + 1,  # 使用不同端口避免冲突
        reload=settings.debug,
        log_level="info" if settings.debug else "warning"
    ) 