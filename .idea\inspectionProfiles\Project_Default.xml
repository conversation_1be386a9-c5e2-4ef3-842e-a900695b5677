<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="8">
            <item index="0" class="java.lang.String" itemvalue="optuna" />
            <item index="1" class="java.lang.String" itemvalue="tensorboard" />
            <item index="2" class="java.lang.String" itemvalue="mediapipe" />
            <item index="3" class="java.lang.String" itemvalue="opencv-python" />
            <item index="4" class="java.lang.String" itemvalue="librosa" />
            <item index="5" class="java.lang.String" itemvalue="torch" />
            <item index="6" class="java.lang.String" itemvalue="face-alignment" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>