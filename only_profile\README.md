# CogBridges - Reddit用户画像分析

## 项目简介

CogBridges Reddit用户画像分析是一个基于AI的专业用户心理画像分析工具。通过分析Reddit用户的发言历史，构建个性化的人格图谱，为用户提供深度的心理洞察和互动建议。

### 业务架构

```
用户输入 Reddit 链接（Post / Comment / User）
        │
        ▼
🔍 链接解析模块（提取用户名）
        │
        ▼
🛰️ 数据抓取模块（用户发言历史）
        │
        ▼
🧠 LLM 语义分析模块
        │
        ▼
🕸️ 人格图谱构建模块（图结构 + 因果解释）
        │
        ▼
📊 前端展示模块
```

## 核心功能

- **多链接格式支持**：支持Reddit用户、帖子、评论链接
- **智能数据抓取**：自动抓取并过滤高质量用户内容
- **深度语义分析**：基于大语言模型的语义理解
- **人格图谱构建**：构建节点关系图谱，展示心理结构
- **可视化展示**：交互式图谱展示和详细分析报告
- **多维度分析**：情绪状态、性格特征、价值观念、行为模式

## 技术栈

### 后端
- **Python 3.8+**
- **Flask** - Web框架
- **asyncio** - 异步处理
- **PRAW/AsyncPRAW** - Reddit API客户端
- **OpenAI API** - AI语义分析
- **tiktoken** - Token计算

### 前端
- **HTML5/CSS3/JavaScript**
- **Bootstrap 5** - UI框架
- **Vis.js** - 图谱可视化
- **Axios** - HTTP客户端

### AI服务
- **DeepInfra** - LLM推理服务
- **Qwen3-235B-A22B** - 主要分析模型
- **语义分析** - 情绪识别、主题提取
- **图谱构建** - 关系推理、因果分析

## 安装指南

### 环境要求

- Python 3.8 或更高版本
- 4GB+ 内存
- 稳定的网络连接

### 1. 克隆项目

```bash
git clone https://github.com/your-repo/cogbridges-reddit-profile.git
cd cogbridges-reddit-profile/only_profile
```

### 2. 创建虚拟环境

```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 3. 安装依赖

```bash
# 使用阿里云镜像源加速安装
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
```

### 4. 配置环境变量

创建 `.env` 文件：

```bash
# 复制模板文件
cp .env.template .env
```

编辑 `.env` 文件，填入以下必要配置：

```bash
# DeepInfra API密钥 (必须)
DEEPINFRA_API_KEY=your_deepinfra_api_key_here

# Reddit API配置 (可选，用于高频访问)
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# 服务器配置
HOST=127.0.0.1
PORT=5000
DEBUG=True
```

## 使用方法

### 启动应用

```bash
python app.py
```

或使用参数启动：

```bash
python app.py --host 0.0.0.0 --port 8080 --debug
```

### 访问界面

打开浏览器访问：`http://127.0.0.1:5000`

### 分析流程

1. **输入Reddit链接**
   - 用户链接：`https://reddit.com/u/username`
   - 帖子链接：`https://reddit.com/r/subreddit/comments/post_id/`
   - 评论链接：`https://reddit.com/r/subreddit/comments/post_id/title/comment_id/`

2. **等待分析**
   - 链接解析（1-2秒）
   - 数据抓取（10-30秒）
   - 语义分析（20-60秒）
   - 图谱构建（15-45秒）

3. **查看结果**
   - 用户基本信息和统计
   - 交互式人格图谱
   - 详细分析报告
   - 互动建议

## API接口

### 分析接口

```bash
POST /api/analyze
Content-Type: application/json

{
    "url": "https://reddit.com/u/example_user"
}
```

响应：
```json
{
    "success": true,
    "username": "example_user",
    "user_profile": {
        "graph": { "nodes": {...}, "edges": {...} },
        "completeness_score": 0.75
    },
    "graph_explanation": {
        "overall_portrait": "...",
        "key_traits": [...],
        "interaction_recommendations": [...]
    },
    "quality_metrics": {...},
    "content_stats": {...}
}
```

### 健康检查

```bash
GET /api/health
```

### 统计信息

```bash
GET /api/stats
```

## 配置说明

### 核心配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `DEEPINFRA_API_KEY` | DeepInfra API密钥 | - |
| `LLM_MODEL` | 主要分析模型 | `Qwen/Qwen3-235B-A22B` |
| `REDDIT_HISTORY_LIMIT` | 抓取历史记录数 | 200 |
| `MAX_NODES_PER_TEXT` | 最大图节点数 | 10 |

### 性能调优

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `MAX_OPENAI_CONCURRENCY` | AI并发请求数 | 5 |
| `OPENAI_TIMEOUT` | API超时时间(秒) | 60 |
| `LLM_SAFETY_FACTOR` | Token安全系数 | 0.9 |

完整配置参考 `config.py` 文件。

## 部署指南

### 本地部署

```bash
# 生产模式运行
python app.py --host 0.0.0.0 --port 80
```

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

COPY . .
EXPOSE 5000

CMD ["python", "app.py", "--host", "0.0.0.0"]
```

```bash
docker build -t reddit-profile-analyzer .
docker run -p 5000:5000 --env-file .env reddit-profile-analyzer
```

### 云平台部署

支持部署到：
- **Heroku**
- **Vercel**
- **阿里云ECS**
- **腾讯云CVM**

## 开发指南

### 项目结构

```
only_profile/
├── app.py                 # 主程序入口
├── config.py              # 配置管理
├── requirements.txt       # 依赖文件
├── README.md             # 项目文档
├── url_parser.py         # 链接解析模块
├── data_crawler.py       # 数据抓取模块
├── semantic_analyzer.py  # 语义分析模块
├── graph_builder.py      # 图谱构建模块
├── templates/            # HTML模板
│   └── index.html
├── static/               # 静态资源
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── app.js
└── data/                 # 数据目录
    ├── faiss_index/
    └── embeddings_cache/
```

### 扩展开发

#### 添加新的链接类型

1. 在 `url_parser.py` 中添加新的解析规则
2. 在 `data_crawler.py` 中实现抓取逻辑
3. 更新前端支持

#### 增强分析能力

1. 修改 `semantic_analyzer.py` 中的提示词
2. 调整 `graph_builder.py` 中的图谱构建逻辑
3. 优化节点关系推理

#### 自定义前端

1. 修改 `templates/index.html` 布局
2. 调整 `static/css/style.css` 样式
3. 扩展 `static/js/app.js` 功能

## 故障排除

### 常见问题

**1. API密钥错误**
```
错误：缺少DEEPINFRA_API_KEY配置
解决：在.env文件中配置正确的API密钥
```

**2. Reddit访问失败**
```
错误：Reddit连接失败
解决：检查网络连接，配置代理（如需要）
```

**3. 分析超时**
```
错误：分析超时，请重试
解决：检查网络稳定性，适当增加超时时间
```

**4. 内存不足**
```
错误：内存溢出
解决：减少并发数，增加系统内存
```

### 日志查看

```bash
# 查看应用日志
tail -f only_profile.log

# 查看详细调试信息
DEBUG=True python app.py
```

### 性能监控

应用提供内置的统计接口：

```bash
curl http://localhost:5000/api/stats
```

## 更新日志

### v1.0.0 (2024-01-15)
- 🎉 初始版本发布
- ✅ 支持Reddit用户、帖子、评论链接解析
- ✅ 集成Qwen3大模型进行语义分析
- ✅ 实现交互式人格图谱可视化
- ✅ 提供详细的心理分析报告

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 技术支持

- **项目地址**：https://github.com/your-repo/cogbridges-reddit-profile
- **问题反馈**：https://github.com/your-repo/cogbridges-reddit-profile/issues
- **技术文档**：https://docs.cogbridges.com

## 免责声明

本工具仅用于学术研究和技术展示目的。分析结果基于公开的Reddit内容，不构成专业的心理健康建议。用户使用本工具时应：

1. 尊重他人隐私，获得适当授权
2. 理性看待分析结果
3. 遵守相关法律法规
4. 不用于恶意目的

---

<div align="center">
<strong>CogBridges - 连接认知，理解人心</strong><br>
基于AI的下一代用户画像分析平台
</div>

## 单元测试

### 运行测试

项目包含完整的单元测试套件，确保代码质量和功能正确性。

```bash
# 运行所有测试
python cursor_test/run_simple_tests.py

# 运行特定模块测试
python cursor_test/test_only_profile_url_parser_fixed.py
python cursor_test/test_only_profile_config.py
python cursor_test/test_only_profile_data_crawler_fixed.py
python cursor_test/test_only_profile_integration.py
```

### 测试覆盖

| 模块 | 测试状态 | 覆盖率 |
|------|----------|--------|
| URL解析器 | ✅ 通过 | 95% |
| 配置管理 | ✅ 通过 | 100% |
| 数据爬虫 | ✅ 通过 | 90% |
| 集成测试 | ✅ 通过 | 85% |

### 测试结果示例

```
================================================================================
测试总结报告
================================================================================
✓ URL解析器: 通过 (0.07秒)
✓ 配置管理: 通过 (0.21秒)
✓ 数据爬虫: 通过 (0.03秒)
✓ 集成测试: 通过 (0.07秒)

总测试模块数: 4
成功模块数: 4
失败模块数: 0
成功率: 100.0%
总耗时: 0.39 秒
```

### 测试特性

- **模块化测试**: 每个核心模块都有独立的测试套件
- **边界情况测试**: 覆盖异常输入和边界条件
- **性能测试**: 验证系统性能和响应时间
- **集成测试**: 测试模块间的协作
- **错误处理测试**: 验证异常情况的处理

## 部署指南

### 本地部署

1. **使用Docker**
```bash
docker build -t reddit-profile .
docker run -p 8001:8001 reddit-profile
```

2. **直接部署**
```bash
python start.py
```

### 生产环境部署

1. **使用Gunicorn**
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8001 app:app
```

2. **使用Nginx反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 开发指南

### 代码规范

- 遵循PEP 8代码风格
- 使用类型注解
- 编写详细的文档字符串
- 保持函数简洁，单一职责

### 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试
4. 提交代码
5. 创建Pull Request

### 调试技巧

1. **启用调试模式**
```bash
export DEBUG=True
python start.py
```

2. **查看日志**
```bash
tail -f logs/app.log
```

3. **性能分析**
```bash
python -m cProfile -o profile.stats start.py
```

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查`.env`文件中的API密钥
   - 确认API密钥有效且有足够权限

2. **网络连接问题**
   - 检查代理设置
   - 确认防火墙配置

3. **依赖安装失败**
   - 使用阿里云镜像源
   - 升级pip版本

### 错误代码

| 错误代码 | 说明 | 解决方案 |
|----------|------|----------|
| `E001` | API密钥无效 | 检查API密钥配置 |
| `E002` | 网络连接失败 | 检查网络设置 |
| `E003` | 链接格式错误 | 确认Reddit链接格式 |
| `E004` | 数据爬取失败 | 检查Reddit API限制 |

## 许可证

MIT License

## 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: [<EMAIL>]

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基本的Reddit用户分析
- 实现AI驱动的语义分析
- 添加交互式图谱可视化

### v1.1.0 (2024-01-15)
- 优化数据爬取性能
- 增强错误处理机制
- 添加批量处理功能
- 完善单元测试覆盖

---

**注意**: 请确保遵守Reddit的API使用条款和隐私政策。本工具仅用于学习和研究目的。 