"""
快速调试测试脚本
专门为快速测试设计，大幅减少数据量以提高运行速度
适合开发和调试时使用
"""
import asyncio
import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 配置日志输出
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

from resona.pipeline import RedditResonancePipeline
from resona.config import settings

class FastModeConfig:
    """快速模式配置 - 大幅减少数据量以提高运行速度"""
    
    @classmethod
    def apply_fast_config(cls):
        """应用快速模式配置到全局settings"""
        print("⚡ 应用快速模式配置...")
        
        # 保存原始配置
        cls.original_config = {
            'reddit_search_limit': settings.reddit_search_limit,
            'reddit_history_limit': settings.reddit_history_limit,
            'post_search_limit': settings.post_search_limit,
            'max_quality_commenters': settings.max_quality_commenters,
            'top_k_matches': settings.top_k_matches,
            'embedding_rerank_count': settings.embedding_rerank_count,
            'llm_final_rank_count': settings.llm_final_rank_count,
        }
        
        # 应用快速配置（大幅减少数据量）
        settings.reddit_search_limit = 10          # 原始：50，减少80%
        settings.reddit_history_limit = 20         # 原始：100，减少80%
        settings.post_search_limit = 15             # 原始：100，减少85%
        settings.max_quality_commenters = 5        # 原始：30，减少83%
        settings.top_k_matches = 2                 # 原始：5，减少60%
        settings.embedding_rerank_count = 8        # 原始：30，减少73%
        settings.llm_final_rank_count = 4          # 原始：10，减少60%
        
        print("📊 快速模式配置已应用:")
        print(f"   - Reddit搜索帖子: {settings.post_search_limit} (原始: {cls.original_config['post_search_limit']})")
        print(f"   - 每用户历史数据: {settings.reddit_history_limit} (原始: {cls.original_config['reddit_history_limit']})")
        print(f"   - 最大候选用户: {settings.max_quality_commenters} (原始: {cls.original_config['max_quality_commenters']})")
        print(f"   - 推荐数量: {settings.top_k_matches} (原始: {cls.original_config['top_k_matches']})")
        print()
    
    @classmethod
    def restore_original_config(cls):
        """恢复原始配置"""
        if hasattr(cls, 'original_config'):
            for key, value in cls.original_config.items():
                setattr(settings, key, value)

def display_menu():
    """显示主菜单"""
    print("⚡ Reddit共鸣推荐系统 - 快速调试模式")
    print("=" * 60)
    print("🚀 专为快速测试设计，大幅减少数据量以提高运行速度")
    print("📊 数据量对比:")
    print("   - 搜索帖子: 15 (原始: 100) ↓85%")
    print("   - 候选用户: 5 (原始: 30) ↓83%")  
    print("   - 用户历史: 20 (原始: 100) ↓80%")
    print("   - 推荐数量: 2 (原始: 5) ↓60%")
    print()
    print("请选择执行模式：")
    print("1. 快速完整流程（约1-2分钟）")
    print("2. 查看已有缓存")
    print("3. 使用缓存快速跳转（约30秒）")
    print("4. 极速模式（进一步减少，约20秒）")
    print("5. 清理所有缓存")
    print("6. 退出")
    print("-" * 60)

async def run_fast_pipeline(test_prompt: str):
    """运行快速流水线"""
    print(f"📝 测试输入: {test_prompt}")
    print()
    
    # 应用快速模式配置
    FastModeConfig.apply_fast_config()
    
    try:
        print("🚀 初始化流水线...")
        pipeline = RedditResonancePipeline()
        print("✅ 流水线初始化完成")
        
        start_time = datetime.now()
        print("⚡ 开始执行快速流水线...")
        print("-" * 40)
        
        # 执行流水线
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            additional_contents=None,
            max_recommendations=2,
            use_cache=True
        )
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        # 显示结果摘要
        print()
        print("=" * 50)
        print("📊 快速模式执行结果:")
        
        if result.get("success"):
            print("✅ 流水线执行成功")
            print(f"⏱️  总耗时: {total_time:.2f}秒")
            print(f"📈 推荐数量: {len(result.get('recommendations', []))}")
            
            stats = result.get('stats', {})
            print(f"📋 找到相关帖子: {stats.get('relevant_posts_found', 0)}")
            print(f"👥 候选用户数: {stats.get('candidate_users_found', 0)}")
            print(f"🔗 成功构建图谱: {stats.get('successful_graphs', 0)}")
            
            # 显示推荐用户
            recommendations = result.get('recommendations', [])
            if recommendations:
                print("\n🎯 推荐用户:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"  {i}. {rec.get('candidate_id', 'unknown')} (分数: {rec.get('resonance_score', 0):.3f})")
            
            # 显示缓存信息
            cache_key = result.get('pipeline_results', {}).get('cache_key')
            if cache_key:
                print(f"\n💾 ABC结果已缓存，键: {cache_key[:12]}...")
        else:
            print("❌ 流水线执行失败")
            print(f"错误: {result.get('error', '未知错误')}")
        
        await pipeline.close()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
    finally:
        # 恢复原始配置
        FastModeConfig.restore_original_config()

async def run_ultra_fast_mode(test_prompt: str):
    """运行极速模式测试"""
    print(f"📝 测试输入: {test_prompt}")
    print()
    
    # 极速模式配置（更激进的减少）
    print("🚀 应用极速模式配置...")
    original_config = {
        'reddit_search_limit': settings.reddit_search_limit,
        'reddit_history_limit': settings.reddit_history_limit,
        'post_search_limit': settings.post_search_limit,
        'max_quality_commenters': settings.max_quality_commenters,
        'top_k_matches': settings.top_k_matches,
    }
    
    # 极速配置
    settings.reddit_search_limit = 5
    settings.reddit_history_limit = 10
    settings.post_search_limit = 8
    settings.max_quality_commenters = 3
    settings.top_k_matches = 1
    
    print("📊 极速模式配置: 搜索8个帖子, 3个候选用户, 10条用户历史, 1个推荐")
    print()
    
    try:
        pipeline = RedditResonancePipeline()
        
        start_time = datetime.now()
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            additional_contents=None,
            max_recommendations=1,
            use_cache=True
        )
        end_time = datetime.now()
        
        print(f"🏃‍♂️ 极速模式完成，耗时: {(end_time - start_time).total_seconds():.2f}秒")
        
        if result.get("success"):
            stats = result.get('stats', {})
            print(f"📊 结果: {stats.get('relevant_posts_found', 0)}帖子, {stats.get('candidate_users_found', 0)}用户, {len(result.get('recommendations', []))}推荐")
            
            recommendations = result.get('recommendations', [])
            if recommendations:
                rec = recommendations[0]
                print(f"🎯 推荐: {rec.get('candidate_id', 'unknown')} (分数: {rec.get('resonance_score', 0):.3f})")
        
        await pipeline.close()
        
    except Exception as e:
        print(f"❌ 极速模式出错: {e}")
    finally:
        # 恢复原始配置
        for key, value in original_config.items():
            setattr(settings, key, value)

async def show_cache_list():
    """显示缓存列表"""
    try:
        pipeline = RedditResonancePipeline()
        cached_sessions = await pipeline.list_cached_sessions()
        
        if not cached_sessions:
            print("❌ 没有找到已缓存的ABC结果")
            await pipeline.close()
            return None
        
        print(f"📋 找到 {len(cached_sessions)} 个缓存:")
        for i, session in enumerate(cached_sessions, 1):
            cache_key = session.get('cache_key', 'unknown')
            user_prompt = session.get('user_prompt', 'N/A')[:40] + "..." if len(session.get('user_prompt', '')) > 40 else session.get('user_prompt', 'N/A')
            print(f"  {i}. {cache_key[:12]}... - {user_prompt}")
        
        await pipeline.close()
        return cached_sessions
    except Exception as e:
        print(f"❌ 获取缓存失败: {e}")
        return None

async def run_from_cache(cache_key: str, test_prompt: str):
    """使用缓存快速跳转"""
    print(f"🚀 使用缓存 {cache_key[:12]}... 快速跳转...")
    
    FastModeConfig.apply_fast_config()
    
    try:
        pipeline = RedditResonancePipeline()
        
        start_time = datetime.now()
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            cache_key=cache_key,
            max_recommendations=2,
            use_cache=True
        )
        end_time = datetime.now()
        
        print(f"⏱️  缓存跳转耗时: {(end_time - start_time).total_seconds():.2f}秒")
        print(f"📈 推荐数量: {len(result.get('recommendations', []))}")
        
        await pipeline.close()
        
    except Exception as e:
        print(f"❌ 缓存跳转失败: {e}")
    finally:
        FastModeConfig.restore_original_config()

async def clear_cache():
    """清理缓存"""
    try:
        pipeline = RedditResonancePipeline()
        await pipeline.clear_all_cache()
        await pipeline.close()
        print("✅ 缓存已清理")
    except Exception as e:
        print(f"❌ 清理缓存失败: {e}")

async def main():
    """主程序入口"""
    print("⚡ Reddit共鸣推荐系统 - 快速调试模式")
    print("💡 大幅减少数据量，显著提高运行速度，适合开发调试")
    print()
    
    # 预设测试查询
    test_queries = [
        "我坚持创业7年了，但一直都没什么成绩，每天都在学习反思，和别人聊天的时候总能意识到自己懂得很多东西，但是到自己做了又总是失败。我还应该继续坚持吗？",
        "我是一个计算机专业的学生，对未来的职业发展很迷茫，不知道应该考研还是直接工作。",
        "最近工作压力很大，总是加班到很晚，感觉生活完全没有平衡。"
    ]
    
    while True:
        display_menu()
        try:
            choice = input("请选择 (1-6): ").strip()
            
            if choice == "1":
                print("\n请选择测试查询:")
                for i, query in enumerate(test_queries, 1):
                    print(f"  {i}. {query[:40]}...")
                print(f"  {len(test_queries)+1}. 自定义输入")
                
                query_choice = input(f"请选择 (1-{len(test_queries)+1}): ").strip()
                
                if query_choice.isdigit() and 1 <= int(query_choice) <= len(test_queries):
                    test_prompt = test_queries[int(query_choice)-1]
                elif query_choice == str(len(test_queries)+1):
                    test_prompt = input("请输入您的测试查询: ").strip()
                    if not test_prompt:
                        print("❌ 输入不能为空")
                        continue
                else:
                    print("❌ 无效选择")
                    continue
                
                await run_fast_pipeline(test_prompt)
                
            elif choice == "2":
                await show_cache_list()
                
            elif choice == "3":
                cached_sessions = await show_cache_list()
                if cached_sessions:
                    try:
                        cache_idx = int(input("请选择要使用的缓存编号: ").strip()) - 1
                        if 0 <= cache_idx < len(cached_sessions):
                            cache_key = cached_sessions[cache_idx]['cache_key']
                            test_prompt = cached_sessions[cache_idx].get('user_prompt', '测试查询')
                            await run_from_cache(cache_key, test_prompt)
                        else:
                            print("❌ 无效编号")
                    except ValueError:
                        print("❌ 请输入有效数字")
                
            elif choice == "4":
                print("\n🏃‍♂️ 极速模式 - 进一步减少数据量")
                query_choice = input("使用默认查询？(Y/n): ").strip().lower()
                if query_choice in ['', 'y', 'yes']:
                    test_prompt = test_queries[0]
                else:
                    test_prompt = input("请输入测试查询: ").strip()
                    if not test_prompt:
                        test_prompt = test_queries[0]
                
                await run_ultra_fast_mode(test_prompt)
                
            elif choice == "5":
                confirm = input("确认清理所有缓存？(y/N): ").strip().lower()
                if confirm == 'y':
                    await clear_cache()
                
            elif choice == "6":
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请重新输入")
            
            print("\n" + "="*50)
            input("按回车键继续...")
            print()
            
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序出错: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 