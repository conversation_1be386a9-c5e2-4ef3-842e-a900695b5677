#!/usr/bin/env python3
"""
快速测试启动脚本
整合缓存和并行优化功能，提供用户友好的测试入口
"""
import asyncio
import sys
from pathlib import Path
import argparse
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from resona.pipeline import RedditResonancePipeline
from resona.config import settings

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickTestRunner:
    """快速测试运行器"""
    
    def __init__(self):
        self.pipeline = None
    
    async def setup(self):
        """初始化"""
        logger.info("🚀 初始化快速测试环境...")
        self.pipeline = RedditResonancePipeline()
        logger.info("✅ 初始化完成")
    
    async def run_performance_test(self):
        """运行性能测试"""
        logger.info("🧪 开始性能测试...")
        
        test_prompt = "我是计算机专业的学生，对未来职业发展很迷茫，不知道该考研还是工作。"
        
        try:
            # 关闭步进调试以测试真实性能
            settings.debug_step_by_step = False
            
            result = await self.pipeline.execute_full_pipeline(
                user_prompt=test_prompt,
                max_recommendations=3,
                use_cache=True
            )
            
            if result.get('success'):
                logger.info("✅ 性能测试完成")
                logger.info(f"   处理时间: {result.get('processing_time', 0):.2f}秒")
                logger.info(f"   推荐数量: {len(result.get('recommendations', []))}")
                
                # 保存缓存键供后续使用
                cache_key = result.get('pipeline_results', {}).get('cache_key')
                if cache_key:
                    logger.info(f"   缓存键: {cache_key}")
                    
                    # 写入文件供快速跳转使用
                    with open("last_cache_key.txt", "w") as f:
                        f.write(cache_key)
                    logger.info("💾 缓存键已保存到 last_cache_key.txt")
                
                return True
            else:
                logger.error(f"❌ 性能测试失败: {result.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 性能测试异常: {e}")
            return False
    
    async def run_cache_jump_test(self, cache_key: str = None):
        """运行缓存跳转测试"""
        logger.info("🚀 开始缓存跳转测试...")
        
        if not cache_key:
            # 尝试从文件读取
            cache_file = Path("last_cache_key.txt")
            if cache_file.exists():
                cache_key = cache_file.read_text().strip()
                logger.info(f"📖 从文件读取缓存键: {cache_key}")
            else:
                logger.error("❌ 未提供缓存键且未找到缓存文件")
                return False
        
        try:
            # 关闭步进调试
            settings.debug_step_by_step = False
            
            result = await self.pipeline.execute_full_pipeline(
                user_prompt="缓存跳转测试",
                max_recommendations=3,
                use_cache=True,
                cache_key=cache_key
            )
            
            if result.get('success'):
                logger.info("✅ 缓存跳转测试完成")
                logger.info(f"   处理时间: {result.get('processing_time', 0):.2f}秒")
                logger.info(f"   推荐数量: {len(result.get('recommendations', []))}")
                return True
            else:
                logger.error(f"❌ 缓存跳转测试失败: {result.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 缓存跳转测试异常: {e}")
            return False
    
    async def show_cache_info(self):
        """显示缓存信息"""
        logger.info("📊 获取缓存信息...")
        
        try:
            # 列出缓存会话
            sessions = await self.pipeline.list_cached_sessions()
            print(f"\n📋 缓存会话列表 ({len(sessions)} 个):")
            print("="*80)
            
            for i, session in enumerate(sessions, 1):
                print(f"{i}. 缓存键: {session['cache_key']}")
                print(f"   用户输入: {session['user_prompt'][:60]}...")
                print(f"   候选用户: {session['candidate_count']}, 帖子: {session['posts_count']}")
                print(f"   缓存时间: {session['cached_at']}")
                print("-" * 80)
            
            # 显示统计信息
            stats = await self.pipeline.get_cache_stats()
            print(f"\n📊 缓存统计:")
            print(f"   总缓存会话: {stats['total_cached_sessions']}")
            print(f"   缓存目录: {stats['cache_directory']}")
            print(f"   总文件数: {stats['total_cache_files']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 获取缓存信息失败: {e}")
            return False
    
    async def interactive_mode(self):
        """交互模式"""
        print("\n🎮 CogBridges 快速测试工具")
        print("="*50)
        print("支持的优化功能:")
        print("• 子任务ABC结果缓存")
        print("• 直接跳转到子任务D")
        print("• 优化并行处理")
        print("• 智能内容质量选择")
        print("="*50)
        
        await self.setup()
        
        while True:
            try:
                print("\n📋 可用操作:")
                print("1. 运行性能测试（生成缓存）")
                print("2. 运行缓存跳转测试")
                print("3. 显示缓存信息")
                print("4. 清理过期缓存")
                print("5. 自定义测试")
                print("0. 退出")
                
                choice = input("\n请选择操作 (0-5): ").strip()
                
                if choice == '0':
                    break
                elif choice == '1':
                    await self.run_performance_test()
                elif choice == '2':
                    cache_key = input("输入缓存键（留空使用最后保存的）: ").strip()
                    await self.run_cache_jump_test(cache_key if cache_key else None)
                elif choice == '3':
                    await self.show_cache_info()
                elif choice == '4':
                    cleaned = await self.pipeline.cache_manager.clean_expired_cache()
                    print(f"✅ 清理了 {cleaned} 个过期缓存")
                elif choice == '5':
                    await self.custom_test()
                else:
                    print("❌ 无效选择")
                    
            except KeyboardInterrupt:
                print("\n🛑 操作被中断")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")
    
    async def custom_test(self):
        """自定义测试"""
        print("\n📝 自定义测试")
        
        prompt = input("请输入测试提示（留空使用默认）: ").strip()
        if not prompt:
            prompt = "我对未来的职业规划很困惑，希望能找到志同道合的人交流。"
        
        max_rec = input("最大推荐数量（默认3）: ").strip()
        try:
            max_rec = int(max_rec) if max_rec else 3
        except ValueError:
            max_rec = 3
        
        use_cache = input("是否使用缓存 (Y/n): ").strip().lower() != 'n'
        
        # 设置调试模式
        debug = input("是否启用步进调试 (y/N): ").strip().lower() == 'y'
        settings.debug_step_by_step = debug
        
        try:
            logger.info("🚀 开始自定义测试...")
            
            result = await self.pipeline.execute_full_pipeline(
                user_prompt=prompt,
                max_recommendations=max_rec,
                use_cache=use_cache
            )
            
            if result.get('success'):
                print("✅ 自定义测试完成")
                print(f"   处理时间: {result.get('processing_time', 0):.2f}秒")
                print(f"   推荐数量: {len(result.get('recommendations', []))}")
                
                # 显示推荐结果
                for i, rec in enumerate(result.get('recommendations', [])[:3], 1):
                    print(f"\n{i}. 推荐用户: {rec['candidate_id']}")
                    print(f"   共鸣分数: {rec['resonance_score']:.3f}")
                    print(f"   推荐理由: {rec['reasoning'][:100]}...")
            else:
                print(f"❌ 自定义测试失败: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ 自定义测试异常: {e}")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.pipeline:
                await self.pipeline.close()
            logger.info("🧹 资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CogBridges 快速测试工具")
    parser.add_argument('--mode', choices=['performance', 'cache-jump', 'info', 'interactive'], 
                       default='interactive', help='运行模式')
    parser.add_argument('--cache-key', help='缓存键（用于跳转测试）')
    parser.add_argument('--no-debug', action='store_true', help='禁用步进调试')
    
    args = parser.parse_args()
    
    # 设置调试模式
    if args.no_debug:
        settings.debug_step_by_step = False
    
    runner = QuickTestRunner()
    
    try:
        if args.mode == 'performance':
            await runner.setup()
            success = await runner.run_performance_test()
            print("✅ 性能测试完成" if success else "❌ 性能测试失败")
            
        elif args.mode == 'cache-jump':
            await runner.setup()
            success = await runner.run_cache_jump_test(args.cache_key)
            print("✅ 缓存跳转测试完成" if success else "❌ 缓存跳转测试失败")
            
        elif args.mode == 'info':
            await runner.setup()
            await runner.show_cache_info()
            
        elif args.mode == 'interactive':
            await runner.interactive_mode()
            
    except KeyboardInterrupt:
        print("\n🛑 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        logger.error(f"程序异常: {e}", exc_info=True)
    finally:
        await runner.cleanup()
        print("\n👋 感谢使用 CogBridges 快速测试工具!")

if __name__ == "__main__":
    print("🚀 CogBridges 快速测试工具")
    print("💡 使用说明:")
    print("   python start_quick_test.py --mode performance  # 性能测试")
    print("   python start_quick_test.py --mode cache-jump   # 缓存跳转测试")
    print("   python start_quick_test.py --mode info         # 显示缓存信息")
    print("   python start_quick_test.py --mode interactive  # 交互模式（默认）")
    print("   python start_quick_test.py --no-debug         # 禁用步进调试")
    print()
    
    asyncio.run(main()) 