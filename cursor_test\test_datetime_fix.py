#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 datetime 序列化修复
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.utils.detailed_results_manager import DetailedResultsManager
from resona.utils.detailed_results_manager import json_serial_default
import json

def test_datetime_serialization():
    """测试 datetime 序列化功能"""
    print("🧪 测试 datetime 序列化修复...")
    
    # 测试 json_serial_default 函数
    test_datetime = datetime.now()
    serialized = json_serial_default(test_datetime)
    print(f"✅ datetime 对象 {test_datetime} 成功序列化为: {serialized}")
    
    # 测试带有 datetime 的复杂数据结构
    test_data = {
        "timestamp": datetime.now(),
        "nested": {
            "created_at": datetime.now(),
            "metadata": {
                "last_updated": datetime.now()
            }
        },
        "items": [
            {"date": datetime.now(), "value": 123},
            {"date": datetime.now(), "value": 456}
        ]
    }
    
    # 测试 JSON 序列化
    json_str = json.dumps(test_data, ensure_ascii=False, indent=2, default=json_serial_default)
    print(f"✅ 复杂数据结构成功序列化为 JSON，长度: {len(json_str)} 字符")
    
    # 测试反序列化
    parsed_data = json.loads(json_str)
    print(f"✅ JSON 成功反序列化，包含 {len(parsed_data)} 个顶级字段")
    
    return True

def test_detailed_results_manager():
    """测试 DetailedResultsManager 的 datetime 处理"""
    print("\n🧪 测试 DetailedResultsManager...")
    
    # 创建测试管理器
    manager = DetailedResultsManager("cursor_test/test_results")
    
    # 开始会话
    session_id = manager.start_session("测试 datetime 序列化修复")
    print(f"✅ 会话已启动: {session_id}")
    
    # 保存包含 datetime 的测试数据
    test_task_data = {
        "execution_start": datetime.now(),
        "test_results": [
            {"timestamp": datetime.now(), "result": "success"},
            {"timestamp": datetime.now(), "result": "warning"}
        ],
        "metadata": {
            "created_at": datetime.now(),
            "version": "test_1.0"
        }
    }
    
    # 保存子任务结果
    manager.save_subtask_result(
        "测试任务", 
        test_task_data, 
        execution_time=1.234,
        force_flush=True
    )
    print("✅ 子任务结果已保存（包含 datetime 对象）")
    
    # 完成会话
    final_results = {
        "success": True,
        "completion_time": datetime.now(),
        "summary": "datetime 序列化测试成功完成"
    }
    
    file_path = manager.complete_session(final_results)
    print(f"✅ 会话已完成，结果保存到: {file_path}")
    
    # 验证文件是否可以正常读取
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        print(f"✅ 结果文件成功加载，包含 {len(loaded_data)} 个顶级字段")
        
        # 检查 datetime 是否被正确序列化为字符串
        session_info = loaded_data.get('session_info', {})
        if 'start_time' in session_info:
            print(f"✅ 开始时间已序列化为: {session_info['start_time']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 开始测试 datetime 序列化修复...")
    
    try:
        # 测试基础序列化功能
        test_datetime_serialization()
        
        # 测试详细结果管理器
        test_detailed_results_manager()
        
        print("\n🎉 所有测试通过！datetime 序列化问题已修复。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 