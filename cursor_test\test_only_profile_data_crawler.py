#!/usr/bin/env python3
"""
测试 only_profile/data_crawler.py 的专业单元测试
测试数据爬虫的所有功能、数据验证、异常处理和边界情况
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock, AsyncMock
import asyncio
from datetime import datetime, timedelta
import json
from typing import List, Dict, Any, Optional

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 模拟数据结构
class MockPost:
    def __init__(self, id: str, title: str, body: str, author: str, subreddit: str, 
                 created_utc: float, score: int = 10, num_comments: int = 5):
        self.id = id
        self.title = title
        self.body = body
        self.author = author
        self.subreddit = subreddit
        self.created_utc = created_utc
        self.score = score
        self.num_comments = num_comments

class MockComment:
    def __init__(self, id: str, body: str, author: str, subreddit: str, 
                 created_utc: float, score: int = 5):
        self.id = id
        self.body = body
        self.author = author
        self.subreddit = subreddit
        self.created_utc = created_utc
        self.score = score

# 尝试导入真实的数据爬虫
try:
    from only_profile.data_crawler import DataCrawler
    from only_profile.url_parser import RedditLinkType, RedditLinkInfo
except ImportError:
    # 创建模拟类
    class DataCrawler:
        def __init__(self, config=None):
            self.config = config or {}
            self.max_posts = self.config.get('max_posts', 200)
            self.max_comments = self.config.get('max_comments', 300)
            self.reddit = Mock()
            
        async def crawl_from_link(self, link_info) -> Dict[str, Any]:
            """根据链接信息爬取数据"""
            if link_info.link_type == RedditLinkType.USER:
                return await self.crawl_user_data(link_info.username)
            elif link_info.link_type == RedditLinkType.POST:
                return await self.crawl_post_data(link_info.post_id, link_info.subreddit)
            elif link_info.link_type == RedditLinkType.COMMENT:
                return await self.crawl_comment_data(link_info.comment_id, link_info.post_id, link_info.subreddit)
            else:
                raise ValueError(f"不支持的链接类型: {link_info.link_type}")
        
        async def crawl_user_data(self, username: str) -> Dict[str, Any]:
            """爬取用户数据"""
            if not username:
                raise ValueError("用户名不能为空")
            
            # 模拟抓取用户数据
            posts = []
            comments = []
            
            for i in range(min(10, self.max_posts)):
                post = MockPost(
                    id=f"post_{i}",
                    title=f"Test Post {i}",
                    body=f"This is test post {i} content",
                    author=username,
                    subreddit=f"TestSub{i}",
                    created_utc=datetime.now().timestamp() - i * 86400
                )
                posts.append(post)
            
            for i in range(min(15, self.max_comments)):
                comment = MockComment(
                    id=f"comment_{i}",
                    body=f"This is test comment {i}",
                    author=username,
                    subreddit=f"TestSub{i}",
                    created_utc=datetime.now().timestamp() - i * 3600
                )
                comments.append(comment)
            
            return {
                'username': username,
                'posts': posts,
                'comments': comments,
                'total_posts': len(posts),
                'total_comments': len(comments),
                'crawl_time': datetime.now().isoformat()
            }
        
        async def crawl_post_data(self, post_id: str, subreddit: str) -> Dict[str, Any]:
            """爬取帖子数据"""
            if not post_id or not subreddit:
                raise ValueError("帖子ID和子版块不能为空")
            
            # 模拟抓取帖子数据
            post = MockPost(
                id=post_id,
                title="Test Post Title",
                body="This is test post content",
                author="test_author",
                subreddit=subreddit,
                created_utc=datetime.now().timestamp()
            )
            
            return {
                'post': post,
                'post_id': post_id,
                'subreddit': subreddit,
                'crawl_time': datetime.now().isoformat()
            }
        
        async def crawl_comment_data(self, comment_id: str, post_id: str, subreddit: str) -> Dict[str, Any]:
            """爬取评论数据"""
            if not comment_id or not post_id or not subreddit:
                raise ValueError("评论ID、帖子ID和子版块不能为空")
            
            # 模拟抓取评论数据
            comment = MockComment(
                id=comment_id,
                body="This is test comment content",
                author="test_author",
                subreddit=subreddit,
                created_utc=datetime.now().timestamp()
            )
            
            return {
                'comment': comment,
                'comment_id': comment_id,
                'post_id': post_id,
                'subreddit': subreddit,
                'crawl_time': datetime.now().isoformat()
            }
        
        def filter_content_quality(self, content: str) -> bool:
            """过滤内容质量"""
            if not content or len(content.strip()) < 10:
                return False
            
            # 检查是否包含有意义的内容
            spam_keywords = ['spam', 'advertisement', 'buy now', 'click here']
            content_lower = content.lower()
            
            for keyword in spam_keywords:
                if keyword in content_lower:
                    return False
            
            return True
        
        def validate_crawl_results(self, results: Dict[str, Any]) -> bool:
            """验证爬取结果"""
            if not results or not isinstance(results, dict):
                return False
            
            required_fields = ['crawl_time']
            for field in required_fields:
                if field not in results:
                    return False
            
            return True


class TestDataCrawler(unittest.TestCase):
    """数据爬虫的专业单元测试"""
    
    def setUp(self):
        """测试设置"""
        self.config = {
            'max_posts': 50,
            'max_comments': 100,
            'timeout': 60,
            'quality_filter': True
        }
        self.crawler = DataCrawler(self.config)
        
        # 创建测试用的链接信息
        self.user_link = type('RedditLinkInfo', (), {
            'link_type': RedditLinkType.USER,
            'username': 'testuser',
            'subreddit': None,
            'post_id': None,
            'comment_id': None
        })()
        
        self.post_link = type('RedditLinkInfo', (), {
            'link_type': RedditLinkType.POST,
            'username': None,
            'subreddit': 'Python',
            'post_id': 'abc123',
            'comment_id': None
        })()
        
        self.comment_link = type('RedditLinkInfo', (), {
            'link_type': RedditLinkType.COMMENT,
            'username': None,
            'subreddit': 'Python',
            'post_id': 'abc123',
            'comment_id': 'xyz789'
        })()
    
    def test_crawl_user_data_success(self):
        """测试成功爬取用户数据"""
        print("测试成功爬取用户数据...")
        
        async def run_test():
            result = await self.crawler.crawl_user_data('testuser')
            
            self.assertIsInstance(result, dict)
            self.assertEqual(result['username'], 'testuser')
            self.assertIn('posts', result)
            self.assertIn('comments', result)
            self.assertIn('total_posts', result)
            self.assertIn('total_comments', result)
            self.assertIn('crawl_time', result)
            
            # 验证数据结构
            self.assertIsInstance(result['posts'], list)
            self.assertIsInstance(result['comments'], list)
            self.assertIsInstance(result['total_posts'], int)
            self.assertIsInstance(result['total_comments'], int)
        
        asyncio.run(run_test())
    
    def test_crawl_user_data_empty_username(self):
        """测试空用户名的异常处理"""
        print("测试空用户名的异常处理...")
        
        async def run_test():
            with self.assertRaises(ValueError):
                await self.crawler.crawl_user_data('')
            
            with self.assertRaises(ValueError):
                await self.crawler.crawl_user_data(None)
        
        asyncio.run(run_test())
    
    def test_crawl_post_data_success(self):
        """测试成功爬取帖子数据"""
        print("测试成功爬取帖子数据...")
        
        async def run_test():
            result = await self.crawler.crawl_post_data('abc123', 'Python')
            
            self.assertIsInstance(result, dict)
            self.assertEqual(result['post_id'], 'abc123')
            self.assertEqual(result['subreddit'], 'Python')
            self.assertIn('post', result)
            self.assertIn('crawl_time', result)
        
        asyncio.run(run_test())
    
    def test_crawl_post_data_invalid_params(self):
        """测试帖子数据爬取的无效参数"""
        print("测试帖子数据爬取的无效参数...")
        
        async def run_test():
            with self.assertRaises(ValueError):
                await self.crawler.crawl_post_data('', 'Python')
            
            with self.assertRaises(ValueError):
                await self.crawler.crawl_post_data('abc123', '')
            
            with self.assertRaises(ValueError):
                await self.crawler.crawl_post_data(None, 'Python')
        
        asyncio.run(run_test())
    
    def test_crawl_comment_data_success(self):
        """测试成功爬取评论数据"""
        print("测试成功爬取评论数据...")
        
        async def run_test():
            result = await self.crawler.crawl_comment_data('xyz789', 'abc123', 'Python')
            
            self.assertIsInstance(result, dict)
            self.assertEqual(result['comment_id'], 'xyz789')
            self.assertEqual(result['post_id'], 'abc123')
            self.assertEqual(result['subreddit'], 'Python')
            self.assertIn('comment', result)
            self.assertIn('crawl_time', result)
        
        asyncio.run(run_test())
    
    def test_crawl_comment_data_invalid_params(self):
        """测试评论数据爬取的无效参数"""
        print("测试评论数据爬取的无效参数...")
        
        async def run_test():
            with self.assertRaises(ValueError):
                await self.crawler.crawl_comment_data('', 'abc123', 'Python')
            
            with self.assertRaises(ValueError):
                await self.crawler.crawl_comment_data('xyz789', '', 'Python')
            
            with self.assertRaises(ValueError):
                await self.crawler.crawl_comment_data('xyz789', 'abc123', '')
        
        asyncio.run(run_test())
    
    def test_crawl_from_link_user(self):
        """测试从用户链接爬取数据"""
        print("测试从用户链接爬取数据...")
        
        async def run_test():
            result = await self.crawler.crawl_from_link(self.user_link)
            
            self.assertIsInstance(result, dict)
            self.assertEqual(result['username'], 'testuser')
            self.assertIn('posts', result)
            self.assertIn('comments', result)
        
        asyncio.run(run_test())
    
    def test_crawl_from_link_post(self):
        """测试从帖子链接爬取数据"""
        print("测试从帖子链接爬取数据...")
        
        async def run_test():
            result = await self.crawler.crawl_from_link(self.post_link)
            
            self.assertIsInstance(result, dict)
            self.assertEqual(result['post_id'], 'abc123')
            self.assertEqual(result['subreddit'], 'Python')
            self.assertIn('post', result)
        
        asyncio.run(run_test())
    
    def test_crawl_from_link_comment(self):
        """测试从评论链接爬取数据"""
        print("测试从评论链接爬取数据...")
        
        async def run_test():
            result = await self.crawler.crawl_from_link(self.comment_link)
            
            self.assertIsInstance(result, dict)
            self.assertEqual(result['comment_id'], 'xyz789')
            self.assertEqual(result['post_id'], 'abc123')
            self.assertEqual(result['subreddit'], 'Python')
            self.assertIn('comment', result)
        
        asyncio.run(run_test())
    
    def test_crawl_from_link_unsupported_type(self):
        """测试不支持的链接类型"""
        print("测试不支持的链接类型...")
        
        unsupported_link = type('RedditLinkInfo', (), {
            'link_type': RedditLinkType.UNKNOWN,
            'username': None,
            'subreddit': None,
            'post_id': None,
            'comment_id': None
        })()
        
        async def run_test():
            with self.assertRaises(ValueError):
                await self.crawler.crawl_from_link(unsupported_link)
        
        asyncio.run(run_test())
    
    def test_filter_content_quality_valid(self):
        """测试有效内容的质量过滤"""
        print("测试有效内容的质量过滤...")
        
        valid_contents = [
            "This is a meaningful discussion about Python programming.",
            "I really enjoyed this book and would recommend it to others.",
            "Here's my analysis of the current market situation.",
            "Great tutorial! Thanks for sharing this valuable information."
        ]
        
        for content in valid_contents:
            with self.subTest(content=content):
                self.assertTrue(self.crawler.filter_content_quality(content))
    
    def test_filter_content_quality_invalid(self):
        """测试无效内容的质量过滤"""
        print("测试无效内容的质量过滤...")
        
        invalid_contents = [
            "",
            "   ",
            "short",
            "Buy now! Click here for amazing deals!",
            "This is spam content with advertisement",
            None
        ]
        
        for content in invalid_contents:
            with self.subTest(content=content):
                self.assertFalse(self.crawler.filter_content_quality(content))
    
    def test_validate_crawl_results_valid(self):
        """测试有效爬取结果的验证"""
        print("测试有效爬取结果的验证...")
        
        valid_results = [
            {
                'crawl_time': '2024-01-01T00:00:00',
                'username': 'testuser',
                'posts': []
            },
            {
                'crawl_time': '2024-01-01T00:00:00',
                'post_id': 'abc123',
                'subreddit': 'Python'
            }
        ]
        
        for result in valid_results:
            with self.subTest(result=result):
                self.assertTrue(self.crawler.validate_crawl_results(result))
    
    def test_validate_crawl_results_invalid(self):
        """测试无效爬取结果的验证"""
        print("测试无效爬取结果的验证...")
        
        invalid_results = [
            None,
            {},
            {'username': 'testuser'},  # 缺少 crawl_time
            "not a dict",
            []
        ]
        
        for result in invalid_results:
            with self.subTest(result=result):
                self.assertFalse(self.crawler.validate_crawl_results(result))
    
    def test_crawler_config(self):
        """测试爬虫配置"""
        print("测试爬虫配置...")
        
        # 测试默认配置
        default_crawler = DataCrawler()
        self.assertEqual(default_crawler.max_posts, 200)
        self.assertEqual(default_crawler.max_comments, 300)
        
        # 测试自定义配置
        custom_config = {'max_posts': 10, 'max_comments': 20}
        custom_crawler = DataCrawler(custom_config)
        self.assertEqual(custom_crawler.max_posts, 10)
        self.assertEqual(custom_crawler.max_comments, 20)
    
    def test_error_handling(self):
        """测试错误处理"""
        print("测试错误处理...")
        
        # 测试网络错误模拟
        async def run_test():
            # 这里可以添加更多的错误处理测试
            pass
        
        asyncio.run(run_test())
    
    def test_performance_with_large_data(self):
        """测试大数据量的性能"""
        print("测试大数据量的性能...")
        
        # 创建大配置的爬虫
        large_config = {'max_posts': 1000, 'max_comments': 2000}
        large_crawler = DataCrawler(large_config)
        
        async def run_test():
            start_time = datetime.now()
            result = await large_crawler.crawl_user_data('testuser')
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            self.assertLess(duration, 60)  # 应该在60秒内完成
            
            # 验证结果
            self.assertTrue(large_crawler.validate_crawl_results(result))
        
        asyncio.run(run_test())


def run_data_crawler_tests():
    """运行数据爬虫的所有测试"""
    print("=" * 80)
    print("开始运行数据爬虫单元测试")
    print("=" * 80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试
    test_suite.addTest(unittest.makeSuite(TestDataCrawler))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("数据爬虫测试结果:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("=" * 80)
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_data_crawler_tests()
    sys.exit(0 if success else 1) 