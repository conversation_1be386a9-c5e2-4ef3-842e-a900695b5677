"""
图谱数据模型 - 用于构建和表示用户三观图谱
"""
from datetime import datetime
from typing import List, Dict, Optional, Any, Set
from pydantic import BaseModel, Field
from enum import Enum
import networkx as nx
import json


class NodeType(str, Enum):
    """图谱节点类型"""
    EXPERIENCE = "experience"  # 经验节点：具体经历、事件
    BELIEF = "belief"         # 信念节点：价值观、原则
    EMOTION = "emotion"       # 情绪节点：情感状态、反应
    TOPIC = "topic"          # 话题节点：讨论主题、领域


class RelationType(str, Enum):
    """图谱关系类型"""
    CAUSES = "causes"           # 因果关系：A导致B
    INFLUENCES = "influences"   # 影响关系：A影响B
    CONTRADICTS = "contradicts" # 冲突关系：A与B矛盾
    SUPPORTS = "supports"       # 支持关系：A支持B
    LEADS_TO = "leads_to"      # 发展关系：A发展为B
    SIMILAR_TO = "similar_to"   # 相似关系：A与B相似


class GraphNode(BaseModel):
    """图谱节点"""
    node_id: str = Field(..., description="节点唯一标识符")
    node_type: NodeType = Field(..., description="节点类型")
    content: str = Field(..., description="节点内容描述")
    weight: float = Field(default=1.0, description="节点重要性权重")
    timestamp: Optional[datetime] = Field(default=None, description="节点创建时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="附加元数据")
    
    # 语义属性
    semantic_embedding: Optional[List[float]] = Field(default=None, description="语义向量")
    keywords: List[str] = Field(default_factory=list, description="关键词标签")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "node_id": self.node_id,
            "node_type": self.node_type.value,
            "content": self.content,
            "weight": self.weight,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "metadata": self.metadata,
            "keywords": self.keywords
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GraphNode':
        """从字典创建节点"""
        if data.get("timestamp"):
            data["timestamp"] = datetime.fromisoformat(data["timestamp"])
        return cls(**data)


class GraphEdge(BaseModel):
    """图谱边"""
    source_id: str = Field(..., description="源节点ID")
    target_id: str = Field(..., description="目标节点ID")
    relation_type: RelationType = Field(..., description="关系类型")
    weight: float = Field(default=1.0, description="关系强度权重")
    confidence: float = Field(default=1.0, description="关系置信度")
    evidence: Optional[str] = Field(default=None, description="关系证据文本")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="附加元数据")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "source_id": self.source_id,
            "target_id": self.target_id,
            "relation_type": self.relation_type.value,
            "weight": self.weight,
            "confidence": self.confidence,
            "evidence": self.evidence,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GraphEdge':
        """从字典创建边"""
        return cls(**data)


class UserGraph(BaseModel):
    """用户三观图谱"""
    user_id: str = Field(..., description="用户标识")
    nodes: Dict[str, GraphNode] = Field(default_factory=dict, description="节点集合")
    edges: List[GraphEdge] = Field(default_factory=list, description="边集合")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="图谱元数据")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    def add_node(self, node: GraphNode) -> None:
        """添加节点"""
        self.nodes[node.node_id] = node
        self.updated_at = datetime.utcnow()
    
    def add_edge(self, edge: GraphEdge) -> None:
        """添加边"""
        # 验证节点存在
        if edge.source_id not in self.nodes:
            raise ValueError(f"源节点 {edge.source_id} 不存在")
        if edge.target_id not in self.nodes:
            raise ValueError(f"目标节点 {edge.target_id} 不存在")
        
        self.edges.append(edge)
        self.updated_at = datetime.utcnow()
    
    def get_node(self, node_id: str) -> Optional[GraphNode]:
        """获取节点"""
        return self.nodes.get(node_id)
    
    def get_edges_from_node(self, node_id: str) -> List[GraphEdge]:
        """获取从指定节点出发的所有边"""
        return [edge for edge in self.edges if edge.source_id == node_id]
    
    def get_edges_to_node(self, node_id: str) -> List[GraphEdge]:
        """获取指向指定节点的所有边"""
        return [edge for edge in self.edges if edge.target_id == node_id]
    
    def get_nodes_by_type(self, node_type: NodeType) -> List[GraphNode]:
        """按类型获取节点"""
        return [node for node in self.nodes.values() if node.node_type == node_type]
    
    def get_subgraph(self, node_types: List[NodeType]) -> 'UserGraph':
        """获取指定类型节点的子图"""
        subgraph = UserGraph(user_id=self.user_id)
        
        # 添加指定类型的节点
        for node in self.nodes.values():
            if node.node_type in node_types:
                subgraph.add_node(node)
        
        # 添加相关的边
        for edge in self.edges:
            if (edge.source_id in subgraph.nodes and 
                edge.target_id in subgraph.nodes):
                subgraph.edges.append(edge)
        
        return subgraph
    
    def to_networkx(self) -> nx.DiGraph:
        """转换为NetworkX图"""
        G = nx.DiGraph()
        
        # 添加节点
        for node in self.nodes.values():
            G.add_node(node.node_id, **node.to_dict())
        
        # 添加边
        for edge in self.edges:
            G.add_edge(
                edge.source_id, 
                edge.target_id, 
                **edge.to_dict()
            )
        
        return G
    
    @classmethod
    def from_networkx(cls, G: nx.DiGraph, user_id: str) -> 'UserGraph':
        """从NetworkX图创建"""
        graph = cls(user_id=user_id)
        
        # 添加节点
        for node_id, node_data in G.nodes(data=True):
            node = GraphNode.from_dict({
                "node_id": node_id,
                **node_data
            })
            graph.add_node(node)
        
        # 添加边
        for source, target, edge_data in G.edges(data=True):
            edge = GraphEdge.from_dict({
                "source_id": source,
                "target_id": target,
                **edge_data
            })
            graph.edges.append(edge)
        
        return graph
    
    def get_node_count_by_type(self) -> Dict[NodeType, int]:
        """统计各类型节点数量"""
        counts = {node_type: 0 for node_type in NodeType}
        for node in self.nodes.values():
            counts[node.node_type] += 1
        return counts
    
    def get_edge_count_by_type(self) -> Dict[RelationType, int]:
        """统计各类型边数量"""
        counts = {relation_type: 0 for relation_type in RelationType}
        for edge in self.edges:
            counts[edge.relation_type] += 1
        return counts
    
    def find_paths_between_nodes(self, source_id: str, target_id: str, 
                                max_length: int = 3) -> List[List[str]]:
        """查找两个节点间的路径"""
        if source_id not in self.nodes or target_id not in self.nodes:
            return []
        
        G = self.to_networkx()
        try:
            # 使用NetworkX查找所有简单路径
            paths = list(nx.all_simple_paths(
                G, source_id, target_id, cutoff=max_length
            ))
            return paths
        except nx.NetworkXNoPath:
            return []
    
    def calculate_node_centrality(self) -> Dict[str, float]:
        """计算节点中心性"""
        G = self.to_networkx()
        if len(G.nodes) == 0:
            return {}
        
        # 使用度中心性
        centrality = nx.degree_centrality(G)
        return centrality
    
    def merge_with(self, other_graph: 'UserGraph') -> 'UserGraph':
        """与另一个图谱合并"""
        merged = UserGraph(user_id=f"{self.user_id}_merged")
        
        # 合并节点（避免重复）
        all_nodes = {}
        all_nodes.update(self.nodes)
        
        # 处理重复节点：如果内容相似，则合并
        for node_id, node in other_graph.nodes.items():
            if node_id not in all_nodes:
                all_nodes[node_id] = node
            else:
                # 简单合并策略：保留权重更高的节点
                if node.weight > all_nodes[node_id].weight:
                    all_nodes[node_id] = node
        
        for node in all_nodes.values():
            merged.add_node(node)
        
        # 合并边
        all_edges = self.edges + other_graph.edges
        
        # 去重边
        edge_signatures = set()
        for edge in all_edges:
            signature = (edge.source_id, edge.target_id, edge.relation_type)
            if signature not in edge_signatures:
                if (edge.source_id in merged.nodes and 
                    edge.target_id in merged.nodes):
                    merged.edges.append(edge)
                    edge_signatures.add(signature)
        
        return merged
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            "user_id": self.user_id,
            "nodes": {k: v.to_dict() for k, v in self.nodes.items()},
            "edges": [edge.to_dict() for edge in self.edges],
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    def to_json(self) -> str:
        """序列化为JSON"""
        data = self.to_dict()
        return json.dumps(data, ensure_ascii=False, indent=2)

    @classmethod
    def from_json(cls, json_str: str) -> 'UserGraph':
        """从JSON反序列化"""
        data = json.loads(json_str)
        
        # 创建基础图谱
        graph = cls(
            user_id=data["user_id"],
            metadata=data.get("metadata", {}),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )
        
        # 添加节点
        for node_id, node_data in data["nodes"].items():
            node = GraphNode.from_dict(node_data)
            graph.nodes[node_id] = node
        
        # 添加边
        for edge_data in data["edges"]:
            edge = GraphEdge.from_dict(edge_data)
            graph.edges.append(edge)
        
        return graph


class GraphElements(BaseModel):
    """从文本提取的图谱元素"""
    experience_nodes: List[GraphNode] = Field(default_factory=list, description="经验节点")
    belief_nodes: List[GraphNode] = Field(default_factory=list, description="信念节点")
    emotion_nodes: List[GraphNode] = Field(default_factory=list, description="情绪节点")
    topic_nodes: List[GraphNode] = Field(default_factory=list, description="话题节点")
    causal_edges: List[GraphEdge] = Field(default_factory=list, description="因果关系边")
    
    def get_all_nodes(self) -> List[GraphNode]:
        """获取所有节点"""
        return (self.experience_nodes + self.belief_nodes + 
                self.emotion_nodes + self.topic_nodes)
    
    def get_all_edges(self) -> List[GraphEdge]:
        """获取所有边"""
        return self.causal_edges 