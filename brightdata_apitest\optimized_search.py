#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BrightData 优化搜索工具
实现搜索推文按1 record计费，而不是按推文数量计费

用法示例：
    python optimized_search.py "openai gpt" --max_users 20
    python optimized_search.py --users "elonmusk,Bill<PERSON><PERSON>,openai" --posts_per_user 100
"""

import argparse
import json
import os
import urllib.parse
import requests
from datetime import datetime

# API配置
API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
PROFILES_DATASET_ID = "gd_lwxmeb2u1cniijd7t4"

class OptimizedTwitterSearch:
    """优化的Twitter搜索工具"""
    
    def __init__(self, api_token=API_TOKEN):
        self.api_token = api_token
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }
    
    def search_users_by_keyword(self, keyword, max_posts_per_user=50):
        """
        方法1：按关键词搜索用户（推荐）
        
        优势：
        - 直接按关键词搜索
        - 按用户数计费（不是按推文数）
        - 每个用户可获得历史推文
        """
        print(f"🔍 按关键词搜索用户: '{keyword}'")
        
        # 构造Twitter用户搜索URL
        encoded_query = urllib.parse.quote_plus(keyword)
        search_url = f"https://x.com/search?q={encoded_query}&f=user"
        
        data = [{
            "url": search_url,
            "max_number_of_posts": max_posts_per_user
        }]
        
        print(f"📍 搜索URL: {search_url}")
        print(f"📊 每用户推文数: {max_posts_per_user}")
        
        return self._trigger_collection(data, f"关键词搜索_{keyword}")
    
    def batch_users(self, user_list, max_posts_per_user=200):
        """
        方法2：批量抓取指定用户
        
        优势：
        - 按用户数计费（每用户=1 record）
        - 每用户可获得大量历史推文（最多3200）
        - 适合已知目标用户的场景
        """
        print(f"👥 批量抓取用户: {len(user_list)} 个用户")
        
        data = []
        for username in user_list:
            # 标准化用户名
            if not username.startswith("https://"):
                username = f"https://x.com/{username.replace('@', '')}"
            
            data.append({
                "url": username,
                "max_number_of_posts": max_posts_per_user
            })
        
        print(f"📊 每用户推文数: {max_posts_per_user}")
        print(f"💰 总计费: {len(user_list)} records")
        
        return self._trigger_collection(data, f"批量用户_{len(user_list)}个")
    
    def _trigger_collection(self, data, task_name):
        """触发数据采集"""
        url = "https://api.brightdata.com/datasets/v3/trigger"
        params = {
            "dataset_id": PROFILES_DATASET_ID,
            "include_errors": "true",
        }
        
        try:
            print("🚀 正在触发数据采集...")
            response = requests.post(url, headers=self.headers, params=params, json=data, timeout=30)
            result = response.json()
            
            if response.status_code == 200:
                snapshot_id = result.get("snapshot_id")
                print(f"✅ 采集任务已启动！")
                print(f"📊 快照ID: {snapshot_id}")
                print(f"💰 计费方式: 按用户Profile计费")
                
                # 保存任务信息
                task_info = {
                    "task_name": task_name,
                    "snapshot_id": snapshot_id,
                    "timestamp": datetime.now().isoformat(),
                    "data_submitted": data,
                    "billing_note": "按用户Profile计费，不是按推文数"
                }
                
                # 保存到文件
                save_path = f"brightdata_apitest/data/task_{snapshot_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                with open(save_path, "w", encoding="utf-8") as f:
                    json.dump(task_info, f, indent=2, ensure_ascii=False)
                
                print(f"📁 任务信息已保存: {save_path}")
                return {"success": True, "snapshot_id": snapshot_id, "task_info": task_info}
                
            else:
                print(f"❌ 采集失败，状态码: {response.status_code}")
                print(f"错误详情: {result}")
                return {"success": False, "error": result}
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return {"success": False, "error": str(e)}

def main():
    parser = argparse.ArgumentParser(description="BrightData优化搜索工具")
    
    # 两种使用方式
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("keyword", nargs="?", help="搜索关键词")
    group.add_argument("--users", help="用户列表，逗号分隔（如：elonmusk,BillGates）")
    
    parser.add_argument("--max_users", type=int, default=30, help="搜索模式：最多处理的用户数")
    parser.add_argument("--posts_per_user", type=int, default=100, help="每用户获取的推文数量")
    
    args = parser.parse_args()
    
    searcher = OptimizedTwitterSearch()
    
    print("🎯 BrightData优化搜索工具")
    print("💡 特点：搜索按1 record计费，不是按推文数计费")
    print("=" * 60)
    
    if args.keyword:
        # 方法1：关键词搜索
        result = searcher.search_users_by_keyword(args.keyword, args.posts_per_user)
        
    elif args.users:
        # 方法2：批量用户
        user_list = [u.strip() for u in args.users.split(",")]
        result = searcher.batch_users(user_list, args.posts_per_user)
    
    print("\n" + "=" * 60)
    if result.get("success"):
        print("🎉 任务提交成功！")
        print("⏳ 请等待几分钟后检查结果")
        print(f"🔍 可用此命令检查: python brightdata_apitest/get_user_posts.py --list-snapshots")
    else:
        print("😞 任务提交失败")
        
    print("\n💡 计费说明:")
    print("• 使用Profile Scraper = 按用户数计费")
    print("• 避免了Posts Scraper的按推文计费")
    print("• 每个用户最多可获得3200条历史推文")

if __name__ == "__main__":
    main() 