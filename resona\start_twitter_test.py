"""
Twitter 平台快速测试启动脚本
演示如何使用Twitter功能
"""
import asyncio
import logging
from pipeline import RedditResonancePipeline

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_twitter_pipeline():
    """Twitter平台流水线测试"""
    print("🚀 开始Twitter共鸣推荐流水线测试...")
    
    try:
        # 创建Twitter平台的Pipeline
        pipeline = RedditResonancePipeline(platform="twitter")
        
        # 测试用户输入
        user_prompt = "我最近在纠结是否辞职去读研，内心很不安，不知道该如何决定"
        
        print(f"\n📝 用户输入: {user_prompt}")
        print(f"🔄 使用平台: {pipeline.platform.title()}")
        
        # 执行流水线（使用较小的参数进行快速测试）
        result = await pipeline.execute_full_pipeline(
            user_prompt=user_prompt,
            max_recommendations=3,  # 只要3个推荐
            use_cache=False  # 不使用缓存，确保从Twitter获取新数据
        )
        
        # 显示结果
        if result.get('success'):
            recommendations = result.get('recommendations', [])
            
            print(f"\n✅ 找到 {len(recommendations)} 个推荐用户:")
            
            for i, rec in enumerate(recommendations, 1):
                print(f"\n{i}. @{rec['candidate_id']}")
                print(f"   共鸣分数: {rec['resonance_score']:.3f}")
                print(f"   推荐强度: {rec['recommendation_strength']}")
                print(f"   推荐理由: {rec['summary'][:100]}...")
                
                if rec.get('conversation_starters'):
                    print(f"   开场语: {rec['conversation_starters'][0]}")
        else:
            print(f"❌ 流水线执行失败: {result.get('error')}")
        
        # 显示处理统计
        stats = result.get('stats', {})
        print(f"\n📊 处理统计:")
        print(f"   - 相关推文: {stats.get('relevant_posts_found', 0)} 条")
        print(f"   - 候选用户: {stats.get('candidate_users_found', 0)} 个") 
        print(f"   - 成功构建图谱: {stats.get('successful_graphs', 0)} 个")
        print(f"   - 总处理时间: {result.get('processing_time', 0):.2f} 秒")
        
        # 清理资源
        await pipeline.close()
        
        print("\n🎉 Twitter平台测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        print(f"\n❌ 测试失败: {e}")
        print("\n💡 可能的原因:")
        print("   - 缺少snscrape依赖，请运行: pip install snscrape")
        print("   - 网络连接问题")
        print("   - Twitter访问限制")

async def test_platform_comparison():
    """比较Reddit和Twitter平台的基本功能"""
    print("\n🔄 平台功能对比测试...")
    
    try:
        # 测试关键词
        test_keywords = ["生活困惑"]
        
        # Reddit测试
        print("\n📺 Reddit平台测试:")
        reddit_pipeline = RedditResonancePipeline(platform="reddit")
        
        try:
            reddit_results = await reddit_pipeline.social_service.search_posts_by_keywords_enhanced(
                keywords=test_keywords,
                limit=3,
                time_filter="week"
            )
            print(f"   找到 {len(reddit_results)} 条Reddit帖子")
        except Exception as e:
            print(f"   Reddit搜索失败: {e}")
        finally:
            await reddit_pipeline.close()
        
        # Twitter测试
        print("\n🐦 Twitter平台测试:")
        twitter_pipeline = RedditResonancePipeline(platform="twitter")
        
        try:
            twitter_results = await twitter_pipeline.social_service.search_posts_by_keywords_enhanced(
                keywords=test_keywords,
                limit=3,
                time_filter="week"
            )
            print(f"   找到 {len(twitter_results)} 条Twitter推文")
        except Exception as e:
            print(f"   Twitter搜索失败: {e}")
        finally:
            await twitter_pipeline.close()
        
        print("\n✅ 平台对比测试完成")
        
    except Exception as e:
        print(f"❌ 平台对比测试失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 CogBridges Twitter 平台测试")
    print("=" * 60)
    
    # 选择测试模式
    print("\n请选择测试模式:")
    print("1. 完整Twitter流水线测试 (需要较长时间)")
    print("2. 平台功能对比测试 (快速)")
    print("3. 两个都测试")
    
    choice = input("\n请输入选择 (1/2/3，默认2): ").strip() or "2"
    
    if choice == "1":
        asyncio.run(test_twitter_pipeline())
    elif choice == "2":
        asyncio.run(test_platform_comparison())
    elif choice == "3":
        asyncio.run(test_platform_comparison())
        asyncio.run(test_twitter_pipeline())
    else:
        print("❌ 无效选择，执行快速测试")
        asyncio.run(test_platform_comparison())

if __name__ == "__main__":
    main() 