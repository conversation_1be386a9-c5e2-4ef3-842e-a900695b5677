#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速调试测试脚本 - 精确定位pipeline问题
"""

import sys
import os
import traceback
from datetime import datetime

# 添加项目根目录到path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_imports():
    """测试基本导入"""
    print("🔍 测试1: 基本导入...")
    try:
        from resona.models.graph_models import UserGraph, NodeType, RelationType
        from resona.utils.detailed_results_manager import DetailedResultsManager
        print("✅ 基本导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_enum_values():
    """测试枚举值是否匹配"""
    print("\n🔍 测试2: 检查枚举值...")
    try:
        from resona.models.graph_models import RelationType
        print(f"RelationType成员: {list(RelationType)}")
        
        # 检查实际可用的枚举值
        available_values = [item.name for item in RelationType]
        print(f"可用的RelationType值: {available_values}")
        
        # 检查pipeline中实际使用的枚举值是否存在
        required_values = ['CAUSES', 'INFLUENCES', 'CONTRADICTS', 'SUPPORTS']
        missing = [v for v in required_values if v not in available_values]
        if missing:
            print(f"⚠️ Pipeline需要但缺失的枚举值: {missing}")
        else:
            print("✅ Pipeline需要的枚举值都存在")
        return True
    except Exception as e:
        print(f"❌ 枚举检查失败: {e}")
        traceback.print_exc()
        return False

def test_user_graph_access():
    """测试UserGraph对象访问方式"""
    print("\n🔍 测试3: UserGraph对象访问...")
    try:
        from resona.models.graph_models import UserGraph
        
        # 正确创建UserGraph对象（提供必需的user_id）
        user_graph = UserGraph(user_id="test_user")
        
        # 测试正确的访问方式
        print(f"UserGraph类型: {type(user_graph)}")
        print(f"UserGraph有graph属性: {hasattr(user_graph, 'graph')}")
        print(f"UserGraph有nodes属性: {hasattr(user_graph, 'nodes')}")
        print(f"UserGraph有edges属性: {hasattr(user_graph, 'edges')}")
        
        # 测试直接访问节点和边
        nodes_count = len(user_graph.nodes)
        edges_count = len(user_graph.edges)
        print(f"直接访问 - 节点数: {nodes_count}, 边数: {edges_count}")
        
        # 测试模拟pipeline中的candidate_graphs结构
        candidate_graph_item = {
            'username': 'test_user',
            'graph': user_graph,
            'build_time': 1.5
        }
        
        # 测试pipeline中的访问方式
        g = candidate_graph_item
        if g and g.get('graph'):
            graph_obj = g.get('graph')
            nodes_count = len(graph_obj.nodes)
            edges_count = len(graph_obj.edges)
            print(f"Pipeline风格访问 - 节点数: {nodes_count}, 边数: {edges_count}")
        
        print("✅ UserGraph访问测试通过")
        return True
    except Exception as e:
        print(f"❌ UserGraph访问失败: {e}")
        traceback.print_exc()
        return False

def test_pipeline_critical_lines():
    """测试pipeline中的关键代码行模拟"""
    print("\n🔍 测试4: 模拟pipeline关键代码...")
    try:
        from resona.models.graph_models import UserGraph, NodeType, RelationType
        
        # 模拟candidate_graphs结构
        user_graph = UserGraph(user_id="test_user")
        candidate_graphs = [
            {
                'username': 'user1',
                'graph': user_graph,
                'user_data': {'posts_count': 10, 'comments_count': 20},
                'build_time': 1.5,
                'content_stats': {'total_length': 1000}
            }
        ]
        
        # 模拟pipeline第594-616行的代码
        print("模拟pipeline统计代码:")
        successful_graphs = len([g for g in candidate_graphs if g and g.get('graph')])
        print(f"成功图谱数: {successful_graphs}")
        
        for g in candidate_graphs:
            success = bool(g and g.get('graph'))
            graph_nodes = len(g.get('graph').nodes) if g and g.get('graph') else 0
            graph_edges = len(g.get('graph').edges) if g and g.get('graph') else 0
            print(f"用户 {g.get('username')}: 成功={success}, 节点={graph_nodes}, 边={graph_edges}")
            
            # 测试节点类型统计
            if g and g.get('graph'):
                graph_obj = g.get('graph')
                node_type_stats = {
                    node_type.value: len([n for n in graph_obj.nodes.values() if n.node_type == node_type])
                    for node_type in [NodeType.EXPERIENCE, NodeType.BELIEF, NodeType.EMOTION, NodeType.TOPIC]
                }
                print(f"节点类型统计: {node_type_stats}")
                
                # 测试边类型统计
                edge_type_stats = {
                    rel_type.value: len([e for e in graph_obj.edges if e.relation_type == rel_type])
                    for rel_type in [RelationType.CAUSES, RelationType.INFLUENCES, RelationType.CONTRADICTS, RelationType.SUPPORTS]
                }
                print(f"边类型统计: {edge_type_stats}")
        
        print("✅ Pipeline关键代码模拟测试通过")
        return True
    except Exception as e:
        print(f"❌ Pipeline代码模拟失败: {e}")
        traceback.print_exc()
        return False

def test_detailed_results_save():
    """测试详细结果保存功能"""
    print("\n🔍 测试5: 详细结果保存...")
    try:
        from resona.utils.detailed_results_manager import DetailedResultsManager
        
        manager = DetailedResultsManager()
        
        # 测试开始会话
        session_id = manager.start_session("测试用户查询")
        print(f"开始会话: {session_id}")
        
        # 测试保存一个简单的子任务结果
        test_data = {
            'task': 'test_task',
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'data': {'test': 'value'}
        }
        
        manager.save_subtask_result('test_subtask', test_data, 1.5)
        print("子任务结果已保存")
        
        # 测试完成会话
        final_results = {'success': True, 'total_items': 1}
        result_file = manager.complete_session(final_results)
        print(f"会话完成，保存到: {result_file}")
        
        print("✅ 详细结果保存测试通过")
        return True
    except Exception as e:
        print(f"❌ 详细结果保存失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始快速调试测试...")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_enum_values, 
        test_user_graph_access,
        test_pipeline_critical_lines,
        test_detailed_results_save
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed < total:
        print("🔧 需要修复的问题已定位，请查看上述错误信息")
    else:
        print("✅ 所有快速测试都通过了")

if __name__ == "__main__":
    main() 