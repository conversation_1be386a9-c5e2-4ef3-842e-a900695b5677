"""
子任务D优化测试
测试Token预算批量分析、多维过滤、并发控制等优化效果
"""
import asyncio
import time
import sys
import os
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.core.graph_builder import GraphBuilder
from resona.services.reddit_service import RedditService
from resona.config import settings

async def test_token_budget_calculation():
    """测试智能Token预算计算"""
    print("🧮 测试智能Token预算计算...")
    
    graph_builder = GraphBuilder()
    budget_info = settings.calculate_token_budget()
    
    print(f"  - 模型: {settings.llm_model}")
    print(f"  - 描述: {budget_info['model_description']}")
    print(f"  - 上下文窗口: {budget_info['context_window']:,}")
    print(f"  - 预留输出: {budget_info['max_output_tokens']:,} ({settings.llm_output_ratio:.1%})")
    print(f"  - 系统提示: {budget_info['system_prompt_tokens']:,} ({settings.llm_system_ratio:.1%})")
    print(f"  - 安全系数: {budget_info['safety_factor']:.1%}")
    print(f"  - 有效预算: {graph_builder.token_budget:,} tokens")
    print(f"  - 预算利用率: {graph_builder.token_budget/budget_info['context_window']:.1%}")
    
    # 测试Token计数
    test_text = "这是一段测试文本，用来验证Token计数功能是否正常工作。包含中文和English混合内容。"
    token_count = graph_builder._count_tokens(test_text)
    print(f"  - 测试文本Token数: {token_count}")
    print(f"  - 文本长度: {len(test_text)} 字符")
    print(f"  - Token/字符比例: {token_count/len(test_text):.2f}")
    
    # 测试不同模型的配置
    print(f"\n  📋 支持的模型配置:")
    from resona.config import MODEL_CONFIGS
    for model_name, config in MODEL_CONFIGS.items():
        if model_name != "default":
            print(f"    - {model_name}: {config['context_window']:,} tokens - {config['description']}")

async def test_content_batching():
    """测试内容批量打包"""
    print("\n📦 测试内容批量打包...")
    
    graph_builder = GraphBuilder()
    
    # 创建测试内容
    test_contents = [
        "我是一名大学生，正在考虑是否要考研。感觉很迷茫，不知道该怎么选择。",
        "最近工作压力很大，每天加班到很晚，感觉身体和心理都很疲惫。",
        "和男朋友分手了，心情很低落，不知道如何走出这段感情的阴霾。",
        "刚毕业找工作，投了很多简历都没有回音，开始怀疑自己的能力。",
        "家里人催我结婚，但我觉得还没有准备好，感觉压力很大。",
        "换了新工作，同事都很友好，但工作内容和预期不太一样，有些失望。",
        "最近开始学习新技能，虽然很困难但很有成就感，希望能坚持下去。",
        "和朋友发生了争执，现在关系很尴尬，不知道该如何修复友谊。"
    ]
    
    print(f"  - 原始内容数量: {len(test_contents)}")
    
    # 计算每段内容的Token数
    for i, content in enumerate(test_contents):
        tokens = graph_builder._count_tokens(content)
        print(f"    内容 {i+1}: {tokens} tokens - {content[:30]}...")
    
    # 测试批量打包
    batches = graph_builder._pack_contents_by_token(test_contents)
    
    print(f"  - 打包后批次数: {len(batches)}")
    total_tokens = 0
    for i, batch in enumerate(batches):
        batch_tokens = sum(graph_builder._count_tokens(content) for content in batch)
        total_tokens += batch_tokens
        print(f"    批次 {i+1}: {len(batch)} 段内容, {batch_tokens} tokens")
    
    print(f"  - 总Token数: {total_tokens}")
    print(f"  - Token预算: {graph_builder.token_budget}")
    print(f"  - 预算利用率: {total_tokens/graph_builder.token_budget:.1%}")

async def test_batch_analysis():
    """测试批量分析功能"""
    print("\n🔬 测试批量分析功能...")
    
    graph_builder = GraphBuilder()
    
    test_contents = [
        "我是计算机专业的学生，最近在准备毕业设计，感觉压力很大。",
        "找工作面试了几家公司，有些紧张但也很期待能找到合适的工作。",
        "和室友关系不太好，经常因为小事争吵，宿舍氛围很压抑。"
    ]
    
    start_time = time.time()
    
    try:
        # 测试批量分析
        result = await graph_builder.analyze_contents_batch(test_contents)
        
        analysis_time = time.time() - start_time
        
        print(f"  ✅ 批量分析成功")
        print(f"  - 分析耗时: {analysis_time:.2f}秒")
        print(f"  - 经历节点: {len(result.experience_nodes)}")
        print(f"  - 信念节点: {len(result.belief_nodes)}")
        print(f"  - 情绪节点: {len(result.emotion_nodes)}")
        print(f"  - 话题节点: {len(result.topic_nodes)}")
        print(f"  - 关系边: {len(result.causal_edges)}")
        
        # 显示部分节点内容
        all_nodes = result.get_all_nodes()
        if all_nodes:
            print(f"  - 示例节点:")
            for i, node in enumerate(all_nodes[:3]):
                print(f"    {i+1}. [{node.node_type.value}] {node.content}")
    
    except Exception as e:
        print(f"  ❌ 批量分析失败: {e}")

async def test_user_filtering():
    """测试用户多维过滤"""
    print("\n🚫 测试用户多维过滤...")
    
    reddit_service = RedditService()
    
    # 模拟测试用户（实际使用时需要真实用户名）
    test_usernames = ["test_user_1", "test_user_2", "test_user_3"]
    
    print(f"  - 测试用户数量: {len(test_usernames)}")
    print(f"  - 过滤条件: 账号年龄 ≥30天, 总Karma ≥200")
    
    filtered_count = 0
    for username in test_usernames:
        try:
            # 注意：这会尝试访问真实的Reddit API
            # 在实际测试中可能会失败，这是正常的
            user_data = await reddit_service.get_user_comprehensive_history(username)
            if user_data is None:
                filtered_count += 1
                print(f"    ❌ {username}: 被过滤")
            else:
                print(f"    ✅ {username}: 通过过滤")
        except Exception as e:
            print(f"    ⚠️  {username}: API调用失败 - {e}")
    
    print(f"  - 过滤结果: {filtered_count}/{len(test_usernames)} 被过滤")
    
    await reddit_service.close()

async def test_concurrent_optimization():
    """测试并发优化"""
    print("\n⚡ 测试并发优化...")
    
    import os
    cpu_cores = os.cpu_count() or 4
    test_usernames = [f"user_{i}" for i in range(15)]  # 15个测试用户
    
    # 计算并发参数
    max_concurrent = min(cpu_cores * 2, 16, len(test_usernames))
    
    print(f"  - CPU核心数: {cpu_cores}")
    print(f"  - 测试用户数: {len(test_usernames)}")
    print(f"  - 计算并发数: {max_concurrent}")
    print(f"  - 并发策略: min(CPU核心数×2, 16, 用户数)")

async def test_integration():
    """集成测试：完整的优化流程"""
    print("\n🔄 集成测试：完整优化流程...")
    
    # 模拟完整的子任务D流程
    test_contents = [
        "我是一名软件工程师，最近在考虑跳槽到大厂，但担心工作压力会更大。",
        "刚买了房子，每月房贷压力很大，但也有了自己的小窝，心情复杂。",
        "和女朋友谈了三年，她催我求婚，但我觉得经济条件还不够稳定。",
        "最近开始健身，虽然很累但精神状态明显好了很多，准备坚持下去。",
        "父母年纪大了，想让他们搬到城里和我一起住，但他们不太愿意。"
    ]
    
    print(f"  - 模拟用户内容数量: {len(test_contents)}")
    
    start_time = time.time()
    
    try:
        graph_builder = GraphBuilder()
        
        # 测试完整的图谱构建流程
        user_graph = await graph_builder.build_user_graph(
            contents=test_contents,
            user_context="测试用户"
        )
        
        total_time = time.time() - start_time
        
        print(f"  ✅ 集成测试成功")
        print(f"  - 总耗时: {total_time:.2f}秒")
        print(f"  - 图谱节点: {len(user_graph.nodes)}")
        print(f"  - 图谱边: {len(user_graph.edges)}")
        print(f"  - 平均每段内容耗时: {total_time/len(test_contents):.2f}秒")
        
        # 验证图谱结构
        is_valid = graph_builder.validate_graph_structure(user_graph)
        print(f"  - 图谱结构验证: {'✅ 通过' if is_valid else '❌ 失败'}")
        
    except Exception as e:
        print(f"  ❌ 集成测试失败: {e}")

async def main():
    """主测试函数"""
    print("🚀 子任务D优化测试开始")
    print("=" * 50)
    
    try:
        # 运行所有测试
        await test_token_budget_calculation()
        await test_content_batching()
        await test_batch_analysis()
        await test_user_filtering()
        await test_concurrent_optimization()
        await test_integration()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 