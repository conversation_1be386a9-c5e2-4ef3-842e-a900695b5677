"""
Prompt 模板管理模块 - 集中管理所有 AI 相关的提示词模板
"""

# 语义分析提示词模板
SEMANTIC_ANALYSIS_PROMPT_TEMPLATE = """请对以下用户输入进行深度语义分析，提取关键信息。

用户输入："{query_text}"

请以JSON格式返回以下信息：
{{
    "search_keywords": ["english keyword 1", "english keyword 2"],
    "topics": ["主题1", "主题2"],
    "emotional_state": {{
        "anxiety": 0.0,
        "confusion": 0.0,  
        "hope": 0.0,
        "frustration": 0.0,
        "sadness": 0.0,
        "fear": 0.0
    }},
    "values_info": {{
        "decision_style": "unknown",
        "priority_focus": "unknown",
        "risk_tolerance": "unknown"
    }},
    "core_concerns": ["核心关切1", "核心关切2"],
    "decision_points": ["决策点1", "决策点2"],
    "life_domains": ["生活领域1", "生活领域2"],
    "support_needs": ["支持需求1", "支持需求2"],
    "confidence": 0.0
}}

【重要】分析要求：
1. **search_keywords必须是英文**：无论用户输入什么语言，search_keywords都必须是适合在Reddit上搜索的英文关键词或短语
2. emotional_state中的数值应该基于文本内容客观评估（0-1）
3. values_info中的分类从给定选项中选择：
   - decision_style: analytical/emotional/avoidant/seeking
   - priority_focus: career/relationship/personal_growth/stability
   - risk_tolerance: high/medium/low
4. core_concerns要抓住用户最关心的核心问题
5. **search_keywords条数要求**：请输出**不少于5个且不超过8个**高质量英文关键词
6. **定量评估置信度**: confidence字段应该是0.0到1.0之间的浮点数

{format_instructions}
"""

# 图谱构建提示词模板
GRAPH_ANALYSIS_PROMPT_TEMPLATE = """请仔细分析以下文本内容，提取用户的心理认知图谱元素。

分析文本：
{content}

{context_info}

请按照以下JSON格式输出分析结果：

{{
    "nodes": [
        {{
            "id": "node_1",
            "type": "experience|belief|emotion|topic",
            "content": "节点内容描述",
            "weight": 0.8,
            "metadata": {{
                "confidence": 0.85,
                "keywords": ["关键词1", "关键词2"],
                "intensity": "high|medium|low"
            }}
        }}
    ],
    "edges": [
        {{
            "source": "node_1",
            "target": "node_2", 
            "relation": "causes|influences|conflicts|supports|triggers|correlates",
            "weight": 0.7,
            "evidence": "支撑这种关系的文本证据"
        }}
    ]
}}

【重要】节点类型详细定义：

**experience (经历体验)** - 用户的具体经历、事件、身份、行为：
- 身份状态："我是计算机专业的学生"、"我在一家公司工作"
- 具体经历："参加了面试"、"和朋友吵架了"
- 行为表现："我选择了这个专业"、"我经常加班"

**belief (信念价值观)** - 用户的价值观、原则、观点：
- 价值判断："实际工作能力比学历更重要"
- 人生原则："做人要踏实"、"要为自己的选择负责"

**emotion (情绪感受)** - 用户的情绪状态、心理感受：
- 情绪状态："很焦虑"、"很纠结"、"感到压力"

**topic (话题主题)** - 讨论的具体主题、领域：
- 讨论主题："考研问题"、"职业选择"

【质量要求】：
1. 节点内容具体明确，权重在0-1之间
2. 关系有明确的文本证据支撑
3. 最多输出10个节点

{format_instructions}
"""

# 追问问题生成提示词模板
CLARIFYING_QUESTIONS_PROMPT_TEMPLATE = """基于以下用户画像信息，生成 {num_questions} 个个性化的追问问题，帮助更好地了解用户的价值观和需求。

用户画像摘要：
- 原始查询：{original_query}
- 完整性评分：{completeness_score}
- 节点统计：{node_counts}
- 关键信念：{key_beliefs}
- 情绪模式：{emotional_patterns}
- 缺失维度：{missing_dimensions}

请生成针对性的问题，帮助补充缺失信息。每个问题都应该：
1. 针对具体的缺失维度
2. 易于理解和回答
3. 能够揭示深层的价值观或经历

{format_instructions}
"""

# 共鸣摘要提示词模板
RESONANCE_SUMMARY_PROMPT_TEMPLATE = """基于以下信息，生成一段共鸣摘要，说明为什么推荐这个用户：

用户困扰：{query_text}
用户主要情绪：{emotions}

推荐用户：{username}
该用户的相关帖子：
{posts_text}

请用1-2句话说明这个用户为什么会与查询用户产生共鸣，重点突出相似的经历或感受。

{format_instructions}
"""

# 私信生成提示词模板
PRIVATE_MESSAGE_PROMPT_TEMPLATE = """基于以下信息，生成一条真诚的私信开场白：

我的困扰：{query_text}
对方用户：{username}
共鸣点：{resonance_summary}

要求：
1. 语气真诚、自然，不要太正式
2. 简短提及自己的困扰
3. 说明为什么觉得对方可能理解
4. 表达想要交流的愿望
5. 控制在50-100字

{format_instructions}
"""

# 翻译提示词模板
TRANSLATION_PROMPT_TEMPLATE = """请将以下中文文本翻译成英文，保持原意和语气：

{text}

{format_instructions}
""" 