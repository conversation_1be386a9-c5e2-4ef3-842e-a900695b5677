#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定搜索内容的脚本
搜索: "I've been persevering in entrepreneurship for 7 years"
目标: 获取前10个帖子和对应作者
"""

import requests
import json
import urllib.parse
import time
from datetime import datetime

# API配置
API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
PROFILES_DATASET_ID = "gd_lwxmeb2u1cniijd7t4"  # Profile Scraper
POSTS_DATASET_ID = "gd_lwxkxvnf1cynvib9co"      # Posts Scraper

def test_profile_search_method(search_query):
    """尝试用Profile Scraper搜索（之前发现可行的方法）"""
    print("🔍 方法1：Profile Scraper搜索测试")
    
    # 构造搜索URL
    encoded_query = urllib.parse.quote_plus(search_query)
    search_url = f"https://x.com/search?q={encoded_query}"
    
    data = [{
        "url": search_url,
        "max_number_of_posts": 10  # 只要10条
    }]
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json",
    }
    
    params = {
        "dataset_id": PROFILES_DATASET_ID,
        "include_errors": "true",
    }
    
    print(f"搜索内容: {search_query}")
    print(f"搜索URL: {search_url}")
    print("触发搜索...")
    
    try:
        response = requests.post("https://api.brightdata.com/datasets/v3/trigger",
                               headers=headers, params=params, json=data, timeout=30)
        result = response.json()
        
        if response.status_code == 200:
            snapshot_id = result.get("snapshot_id")
            print(f"✅ 搜索成功！快照ID: {snapshot_id}")
            return {
                "method": "Profile搜索",
                "success": True, 
                "snapshot_id": snapshot_id,
                "search_url": search_url
            }
        else:
            print(f"❌ 搜索失败: {result}")
            return {"method": "Profile搜索", "success": False, "error": result}
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return {"method": "Profile搜索", "success": False, "error": str(e)}

def test_posts_search_method(search_query):
    """尝试用Posts Scraper搜索（可能不支持搜索URL）"""
    print("\n🔍 方法2：Posts Scraper搜索测试")
    
    encoded_query = urllib.parse.quote_plus(search_query)
    search_url = f"https://x.com/search?q={encoded_query}"
    
    data = [{"url": search_url}]
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json",
    }
    
    params = {
        "dataset_id": POSTS_DATASET_ID,
        "include_errors": "true",
    }
    
    print(f"搜索URL: {search_url}")
    print("触发搜索...")
    
    try:
        response = requests.post("https://api.brightdata.com/datasets/v3/trigger",
                               headers=headers, params=params, json=data, timeout=30)
        result = response.json()
        
        if response.status_code == 200:
            snapshot_id = result.get("snapshot_id")
            print(f"✅ 搜索成功！快照ID: {snapshot_id}")
            return {
                "method": "Posts搜索",
                "success": True, 
                "snapshot_id": snapshot_id,
                "search_url": search_url
            }
        else:
            print(f"❌ 搜索失败: {result}")
            return {"method": "Posts搜索", "success": False, "error": result}
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return {"method": "Posts搜索", "success": False, "error": str(e)}

def wait_and_download_results(snapshot_id, method_name, max_wait_minutes=10):
    """等待并下载搜索结果"""
    print(f"\n⏳ 等待 {method_name} 结果...")
    
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    
    # 检查状态
    for i in range(max_wait_minutes * 2):  # 每30秒检查一次
        try:
            # 检查快照状态
            response = requests.get("https://api.brightdata.com/datasets/v3/snapshots",
                                  headers=headers, 
                                  params={"dataset_id": PROFILES_DATASET_ID, "status": "ready"},
                                  timeout=30)
            
            if response.status_code == 200:
                snapshots = response.json()
                for snapshot in snapshots:
                    if snapshot.get("id") == snapshot_id:
                        print(f"✅ {method_name} 完成！数据大小: {snapshot.get('dataset_size')} 条")
                        
                        # 下载数据
                        return download_snapshot_data(snapshot_id, method_name)
            
            print(f"⏳ 等待中... ({i+1}/{max_wait_minutes*2})")
            time.sleep(30)
            
        except Exception as e:
            print(f"❌ 检查状态异常: {e}")
            break
    
    print(f"⏰ 等待超时，快照ID: {snapshot_id}")
    return None

def download_snapshot_data(snapshot_id, method_name):
    """下载快照数据"""
    print("📥 下载数据...")
    
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    
    try:
        response = requests.get(f"https://api.brightdata.com/datasets/v3/snapshot/{snapshot_id}",
                              headers=headers, params={"format": "json"}, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"brightdata_apitest/data/search_test_{method_name}_{timestamp}.json"
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 数据已保存: {filename}")
            
            # 分析结果
            analyze_search_results(data, method_name)
            
            return {"filename": filename, "data": data}
        else:
            print(f"❌ 下载失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 下载异常: {e}")
        return None

def analyze_search_results(data, method_name):
    """分析搜索结果"""
    print(f"\n📊 {method_name} 结果分析:")
    
    if isinstance(data, list) and data:
        print(f"总记录数: {len(data)}")
        
        # 提取帖子和作者信息
        posts_and_authors = []
        
        for i, item in enumerate(data[:10], 1):  # 只看前10条
            if "posts" in item and item["posts"]:
                # Profile Scraper结果格式
                user_name = item.get("profile_name", "Unknown")
                user_id = item.get("id", "Unknown")
                
                for j, post in enumerate(item["posts"][:3], 1):  # 每用户前3条帖子
                    post_info = {
                        "index": f"{i}.{j}",
                        "author": user_name,
                        "author_id": user_id,
                        "content": post.get("description", "")[:100] + "...",
                        "date": post.get("date_posted", ""),
                        "post_id": post.get("id", "")
                    }
                    posts_and_authors.append(post_info)
                    
            elif "description" in item:
                # Posts Scraper结果格式
                post_info = {
                    "index": i,
                    "author": item.get("user_posted", "Unknown"),
                    "content": item.get("description", "")[:100] + "...",
                    "date": item.get("date_posted", ""),
                    "post_id": item.get("id", "")
                }
                posts_and_authors.append(post_info)
        
        # 显示前10条
        print("\n📝 前10条帖子和作者:")
        for post in posts_and_authors[:10]:
            print(f"{post['index']}. @{post['author']}: {post['content']}")
        
        return posts_and_authors
    else:
        print("❌ 数据格式不符合预期")
        return []

def main():
    search_query = "I've been persevering in entrepreneurship for 7 years, but I haven't achieved much success"
    
    print("🧪 测试搜索内容")
    print(f"搜索关键词: {search_query}")
    print("=" * 80)
    
    results = {}
    
    # 方法1：Profile搜索
    result1 = test_profile_search_method(search_query)
    results["profile_search"] = result1
    
    if result1.get("success"):
        # 等待并下载结果
        data1 = wait_and_download_results(result1["snapshot_id"], "Profile搜索")
        if data1:
            results["profile_search"]["downloaded_data"] = data1
    
    # 小延迟
    time.sleep(5)
    
    # 方法2：Posts搜索（可能失败）
    result2 = test_posts_search_method(search_query)
    results["posts_search"] = result2
    
    if result2.get("success"):
        data2 = wait_and_download_results(result2["snapshot_id"], "Posts搜索")
        if data2:
            results["posts_search"]["downloaded_data"] = data2
    
    # 保存测试总结
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = f"brightdata_apitest/data/search_test_summary_{timestamp}.json"
    
    with open(summary_file, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 测试总结已保存: {summary_file}")
    
    # 结论
    print("\n🏆 测试结论:")
    if results["profile_search"].get("success"):
        print("✅ Profile搜索方法可行")
    else:
        print("❌ Profile搜索方法失败")
        
    if results["posts_search"].get("success"):
        print("✅ Posts搜索方法可行")
    else:
        print("❌ Posts搜索方法失败（预期结果）")

if __name__ == "__main__":
    main() 