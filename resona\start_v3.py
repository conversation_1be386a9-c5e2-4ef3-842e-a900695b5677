"""
Reddit 共鸣用户搜索推荐系统 v3.0 启动脚本
"""
import uvicorn
import logging
import sys
import os
from pathlib import Path

# 将项目根目录添加到Python路径，以支持模块化导入
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))
# 添加当前目录到Python路径
# current_dir = Path(__file__).parent
# sys.path.insert(0, str(current_dir))

from resona.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

def main():
    """启动Reddit共鸣推荐系统v3.0"""
    
    print("=" * 60)
    print("🚀 Reddit 共鸣用户搜索推荐系统 v3.0")
    print("=" * 60)
    print("📝 基于结构化图谱的Reddit用户共鸣匹配与推荐平台")
    print()
    
    # 环境检查
    print("🔍 环境检查:")
    print(f"  - Python版本: {sys.version}")
    print(f"  - 工作目录: {os.getcwd()}")
    print(f"  - 配置文件: {project_root / 'resona' / '.env'}")
    print(f"  - Debug模式: {settings.debug}")
    print()
    
    # API服务配置
    host = settings.host
    port = settings.port + 1  # 使用v3端口
    
    print("⚙️  服务配置:")
    print(f"  - 服务地址: http://{host}:{port}")
    print(f"  - 前端访问: http://localhost:{port}")
    print(f"  - API文档: http://localhost:{port}/docs")
    print(f"  - 健康检查: http://localhost:{port}/health")
    print(f"  - 重载模式: {settings.debug}")
    print()
    
    print("🎯 主要功能:")
    print("  - 🔍 Reddit用户搜索和候选者提取")
    print("  - 🧠 AI驱动的语义分析和图谱构建")
    print("  - 📊 多维度共鸣匹配算法")
    print("  - 💬 个性化推荐和对话建议")
    print("  - 🔗 完整的9步业务流水线")
    print()
    
    print("📡 API端点:")
    print(f"  - POST /api/v3/search         - Reddit共鸣用户搜索")
    print(f"  - POST /api/v3/completeness   - 图谱完整性分析")
    print(f"  - GET  /api/v3/stats         - 系统统计信息")
    print(f"  - GET  /health               - 健康检查")
    print()
    
    try:
        logger.info("启动Reddit共鸣推荐系统v3.0...")
        
        # 启动FastAPI服务
        uvicorn.run(
            "resona.api_v3:app",
            host=host,
            port=port,
            reload=settings.debug,
            log_level="info" if settings.debug else "warning",
            access_log=settings.debug,
            reload_dirs=['resona'] if settings.debug else None
        )
        
    except KeyboardInterrupt:
        logger.info("服务被用户停止")
        print("\n👋 服务已停止，感谢使用！")
    except Exception as e:
        logger.error(f"启动服务时发生错误: {e}")
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 