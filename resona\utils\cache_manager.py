"""
缓存管理器 - 保存和加载流水线中间结果
支持子任务ABC结果缓存，实现快速跳转到子任务D
"""
import json
import pickle
import hashlib
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
import asyncio
import aiofiles

logger = logging.getLogger(__name__)

class PipelineCacheManager:
    """
    流水线缓存管理器
    
    功能：
    1. 缓存子任务ABC的结果
    2. 支持按查询内容智能匹配缓存
    3. 定期清理过期缓存
    4. 提供快速加载接口
    """
    
    def __init__(self, cache_dir: Union[str, Path] = "./data/pipeline_cache"):
        """初始化缓存管理器"""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 缓存配置
        self.default_ttl = timedelta(hours=24)  # 默认24小时过期
        self.max_cache_size = 100  # 最大缓存数量
        
        # 缓存索引文件
        self.index_file = self.cache_dir / "cache_index.json"
        self.cache_index = self._load_cache_index()
        
        logger.info(f"流水线缓存管理器初始化完成，缓存目录: {self.cache_dir}")
    
    def _load_cache_index(self) -> Dict[str, Any]:
        """加载缓存索引"""
        if self.index_file.exists():
            try:
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载缓存索引失败: {e}")
        return {}
    
    def _save_cache_index(self) -> None:
        """保存缓存索引"""
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存缓存索引失败: {e}")
    
    def _generate_cache_key(self, user_prompt: str, keywords: List[str] = None) -> str:
        """生成缓存键"""
        content = user_prompt
        if keywords:
            content += "|" + "|".join(sorted(keywords))
        
        # 使用SHA256生成唯一键
        return hashlib.sha256(content.encode('utf-8')).hexdigest()[:16]
    
    async def save_abc_results(self, 
                             user_prompt: str,
                             parsed_query: Any,
                             relevant_posts: List[Dict[str, Any]],
                             candidate_users: List[str],
                             metadata: Dict[str, Any] = None) -> str:
        """
        保存子任务ABC的结果
        
        Args:
            user_prompt: 用户输入
            parsed_query: 解析结果
            relevant_posts: 相关帖子
            candidate_users: 候选用户
            metadata: 额外元数据
            
        Returns:
            cache_key: 缓存键
        """
        try:
            # 生成缓存键
            keywords = getattr(parsed_query, 'search_keywords', None) or []
            cache_key = self._generate_cache_key(user_prompt, keywords)
            
            # 准备缓存数据
            cache_data = {
                'user_prompt': user_prompt,
                'parsed_query': self._serialize_object(parsed_query),
                'relevant_posts': relevant_posts,
                'candidate_users': candidate_users,
                'metadata': metadata or {},
                'cached_at': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            # 保存到文件
            cache_file = self.cache_dir / f"abc_{cache_key}.pkl"
            async with aiofiles.open(cache_file, 'wb') as f:
                await f.write(pickle.dumps(cache_data))
            
            # 更新索引
            self.cache_index[cache_key] = {
                'user_prompt': user_prompt[:100] + "..." if len(user_prompt) > 100 else user_prompt,
                'keywords': keywords,
                'candidate_count': len(candidate_users),
                'posts_count': len(relevant_posts),
                'cached_at': datetime.now().isoformat(),
                'file': f"abc_{cache_key}.pkl",
                'type': 'abc_results'
            }
            
            self._save_cache_index()
            logger.info(f"子任务ABC结果已缓存，键: {cache_key}")
            
            return cache_key
            
        except Exception as e:
            logger.error(f"保存ABC结果失败: {e}")
            return None
    
    async def load_abc_results(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        加载子任务ABC的结果
        
        Args:
            cache_key: 缓存键
            
        Returns:
            缓存的ABC结果，如果不存在或过期返回None
        """
        try:
            if cache_key not in self.cache_index:
                logger.warning(f"缓存键不存在: {cache_key}")
                return None
            
            cache_info = self.cache_index[cache_key]
            cache_file = self.cache_dir / cache_info['file']
            
            if not cache_file.exists():
                logger.warning(f"缓存文件不存在: {cache_file}")
                return None
            
            # 检查是否过期
            cached_at = datetime.fromisoformat(cache_info['cached_at'])
            if datetime.now() - cached_at > self.default_ttl:
                logger.warning(f"缓存已过期: {cache_key}")
                await self._remove_cache(cache_key)
                return None
            
            # 加载数据
            async with aiofiles.open(cache_file, 'rb') as f:
                cache_data = pickle.loads(await f.read())
            
            # 反序列化对象
            cache_data['parsed_query'] = self._deserialize_object(cache_data['parsed_query'])
            
            logger.info(f"成功加载ABC缓存: {cache_key}")
            return cache_data
            
        except Exception as e:
            logger.error(f"加载ABC结果失败: {e}")
            return None
    
    async def find_similar_cache(self, user_prompt: str, similarity_threshold: float = 0.8) -> Optional[str]:
        """
        查找相似的缓存
        
        Args:
            user_prompt: 用户输入
            similarity_threshold: 相似度阈值
            
        Returns:
            最相似的缓存键，如果没有找到返回None
        """
        try:
            if not self.cache_index:
                return None
            
            # 简单的字符串相似度匹配（可以优化为语义相似度）
            best_match = None
            best_score = 0.0
            
            for cache_key, cache_info in self.cache_index.items():
                if cache_info['type'] != 'abc_results':
                    continue
                
                cached_prompt = cache_info['user_prompt']
                score = self._calculate_text_similarity(user_prompt, cached_prompt)
                
                if score > best_score and score >= similarity_threshold:
                    best_score = score
                    best_match = cache_key
            
            if best_match:
                logger.info(f"找到相似缓存: {best_match}, 相似度: {best_score:.3f}")
            
            return best_match
            
        except Exception as e:
            logger.error(f"查找相似缓存失败: {e}")
            return None
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单实现）"""
        # 使用Jaccard相似度
        set1 = set(text1.lower().split())
        set2 = set(text2.lower().split())
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def list_cached_sessions(self) -> List[Dict[str, Any]]:
        """列出所有缓存的会话"""
        sessions = []
        for cache_key, cache_info in self.cache_index.items():
            if cache_info['type'] == 'abc_results':
                sessions.append({
                    'cache_key': cache_key,
                    'user_prompt': cache_info['user_prompt'],
                    'candidate_count': cache_info['candidate_count'],
                    'posts_count': cache_info['posts_count'],
                    'cached_at': cache_info['cached_at']
                })
        
        # 按时间排序
        sessions.sort(key=lambda x: x['cached_at'], reverse=True)
        return sessions
    
    async def clean_expired_cache(self) -> int:
        """清理过期缓存"""
        cleaned_count = 0
        expired_keys = []
        
        for cache_key, cache_info in self.cache_index.items():
            try:
                cached_at = datetime.fromisoformat(cache_info['cached_at'])
                if datetime.now() - cached_at > self.default_ttl:
                    expired_keys.append(cache_key)
            except Exception as e:
                logger.warning(f"检查缓存过期时间失败: {e}")
                expired_keys.append(cache_key)
        
        for cache_key in expired_keys:
            await self._remove_cache(cache_key)
            cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个过期缓存")
        
        return cleaned_count
    
    async def _remove_cache(self, cache_key: str) -> None:
        """删除缓存"""
        try:
            if cache_key in self.cache_index:
                cache_info = self.cache_index[cache_key]
                cache_file = self.cache_dir / cache_info['file']
                
                if cache_file.exists():
                    cache_file.unlink()
                
                del self.cache_index[cache_key]
                self._save_cache_index()
                
        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
    
    def _serialize_object(self, obj: Any) -> Dict[str, Any]:
        """序列化对象"""
        try:
            if hasattr(obj, '__dict__'):
                return {
                    'type': obj.__class__.__name__,
                    'data': obj.__dict__
                }
            else:
                return {'type': 'primitive', 'data': obj}
        except Exception as e:
            logger.warning(f"序列化对象失败: {e}")
            return {'type': 'error', 'data': str(obj)}
    
    def _deserialize_object(self, data: Dict[str, Any]) -> Any:
        """反序列化对象"""
        try:
            if data['type'] == 'primitive':
                return data['data']
            elif data['type'] == 'error':
                return data['data']
            else:
                # 尝试重构对象（简单实现）
                from ..models.user_models import ParsedQuery
                if data['type'] == 'ParsedQuery':
                    return ParsedQuery(**data['data'])
                else:
                    return data['data']
        except Exception as e:
            logger.warning(f"反序列化对象失败: {e}")
            return data['data']
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        self._load_cache_index()
        stats = {
            'total_cached_sessions': len([k for k, v in self.cache_index.items() if v['type'] == 'abc_results']),
            'cache_directory': str(self.cache_dir),
            'index_file_size': self.index_file.stat().st_size if self.index_file.exists() else 0,
            'total_cache_files': len(list(self.cache_dir.glob("*.pkl"))),
            'oldest_cache': None,
            'newest_cache': None
        }
        
        if self.cache_index:
            timestamps = [info['cached_at'] for info in self.cache_index.values() if info['type'] == 'abc_results']
            if timestamps:
                stats['oldest_cache'] = min(timestamps)
                stats['newest_cache'] = max(timestamps)
        
        return stats

    def get_cache_metadata(self, key: str) -> Optional[Dict[str, Any]]:
        """获取指定缓存的元数据"""
        self._load_cache_index()
        return self.cache_index.get(key, {}).get('metadata') 