"""
用户画像构建器 - 负责构建完整的用户三观画像
实现业务架构中的子任务E+F：用户画像构建与追问
"""
import json
import logging
import uuid
from typing import List, Dict, Optional, Any, Tuple, Set
from datetime import datetime
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ..models.user_models import (
    ParsedQuery, UserProfile, InteractionSession, UserContentHistory
)
from ..models.graph_models import (
    UserGraph, NodeType, RelationType, GraphNode, GraphEdge
)
from ..services.ai_service import AIService
from .semantic_analyzer import SemanticAnalyzer
from .graph_builder import GraphBuilder

logger = logging.getLogger(__name__)


class UserProfiler:
    """
    用户画像构建器 - 整合语义分析和图谱构建的结果
    
    核心功能：
    1. 构建完整用户画像的主流程
    2. 基于图谱增强画像质量
    3. 识别画像缺失维度
    4. 生成追问问题以补全画像
    5. 动态更新和优化画像
    """
    
    def __init__(self, 
                 semantic_analyzer: Optional[SemanticAnalyzer] = None,
                 graph_builder: Optional[GraphBuilder] = None,
                 ai_service: Optional[AIService] = None):
        """初始化用户画像构建器"""
        self.semantic_analyzer = semantic_analyzer or SemanticAnalyzer()
        self.graph_builder = graph_builder or GraphBuilder()
        self.ai_service = ai_service or AIService()
        
        # 配置参数
        self.min_completeness_threshold = 0.6  # 最小完整性阈值
        self.max_questions_per_session = 5     # 每次会话最大问题数
        self.dimension_weights = {             # 维度权重
            NodeType.EXPERIENCE: 0.3,
            NodeType.BELIEF: 0.35,
            NodeType.EMOTION: 0.2,
            NodeType.TOPIC: 0.15
        }
        
        # 标准画像模板（用于完整性评估）
        self.standard_profile_template = {
            NodeType.EXPERIENCE: {
                "min_nodes": 2,
                "key_areas": ["工作经历", "学习经历", "重要决策", "人际关系"]
            },
            NodeType.BELIEF: {
                "min_nodes": 2,
                "key_areas": ["价值观", "人生原则", "道德标准", "决策理念"]
            },
            NodeType.EMOTION: {
                "min_nodes": 1,
                "key_areas": ["当前情绪状态", "情绪模式", "应对方式"]
            },
            NodeType.TOPIC: {
                "min_nodes": 1,
                "key_areas": ["关注领域", "讨论话题", "兴趣方向"]
            }
        }
        
        logger.info("用户画像构建器初始化完成")
    
    async def build_comprehensive_profile(self, 
                                        user_input: str,
                                        user_contents: Optional[List[str]] = None,
                                        user_context: Optional[str] = None) -> UserProfile:
        """
        构建完整用户画像的主流程
        
        Args:
            user_input: 用户输入文本
            user_contents: 用户的历史内容（可选）
            user_context: 用户上下文信息（可选）
            
        Returns:
            UserProfile: 完整的用户画像
        """
        logger.info(f"开始构建完整用户画像，输入：{user_input[:50]}...")
        
        try:
            # 步骤1：语义解析用户输入
            logger.info("步骤1：执行语义解析")
            parsed_query = await self.semantic_analyzer.parse_user_input(user_input)
            
            # 步骤2：构建用户图谱
            logger.info("步骤2：构建用户图谱")
            if user_contents:
                # 如果有历史内容，使用历史内容构建图谱
                user_graph = await self.graph_builder.build_user_graph(
                    contents=user_contents,
                    parsed_query=parsed_query,
                    user_context=user_context
                )
            else:
                # 仅基于用户输入构建图谱
                user_graph = await self.graph_builder.build_user_graph(
                    contents=[user_input],
                    parsed_query=parsed_query,
                    user_context=user_context
                )
            
            # 步骤3：基于解析结果增强图谱
            logger.info("步骤3：增强图谱质量")
            enhanced_graph = await self._enhance_graph_with_query(user_graph, parsed_query)
            
            # 步骤4：构建用户画像
            logger.info("步骤4：构建用户画像")
            user_profile = self._create_user_profile(
                user_input=user_input,
                user_graph=enhanced_graph,
                parsed_query=parsed_query
            )
            
            # 步骤5：评估画像完整性
            logger.info("步骤5：评估画像完整性")
            completeness_score = user_profile.update_completeness()
            
            logger.info(f"用户画像构建完成，完整性评分：{completeness_score:.2f}")
            return user_profile
            
        except Exception as e:
            logger.error(f"构建用户画像失败：{e}")
            # 创建基础画像作为降级策略
            return self._create_fallback_profile(user_input)
    
    async def _enhance_graph_with_query(self, user_graph: UserGraph, 
                                      parsed_query: ParsedQuery) -> UserGraph:
        """基于语义解析结果增强图谱"""
        logger.info("基于语义解析结果增强图谱")
        
        # 添加从ParsedQuery中提取的额外信息
        enhanced_graph = user_graph
        
        # 1. 添加情绪节点
        for emotion, intensity in parsed_query.emotional_state.items():
            if intensity > 0.3:  # 只添加显著的情绪
                emotion_node = GraphNode(
                    node_id=f"emotion_{emotion}_{uuid.uuid4().hex[:8]}",
                    node_type=NodeType.EMOTION,
                    content=f"{emotion}情绪状态",
                    weight=intensity,
                    metadata={
                        "source": "semantic_analysis",
                        "intensity": intensity,
                        "confidence": parsed_query.confidence
                    }
                )
                enhanced_graph.add_node(emotion_node)
        
        # 2. 添加价值观信息节点
        for key, value in parsed_query.values_info.items():
            if value and str(value).lower() != "unknown":
                belief_node = GraphNode(
                    node_id=f"belief_{key}_{uuid.uuid4().hex[:8]}",
                    node_type=NodeType.BELIEF,
                    content=f"{key}: {value}",
                    weight=0.7,
                    metadata={
                        "source": "semantic_analysis",
                        "dimension": key,
                        "confidence": parsed_query.confidence
                    }
                )
                enhanced_graph.add_node(belief_node)
        
        # 3. 添加话题节点
        for topic in parsed_query.topics:
            topic_node = GraphNode(
                node_id=f"topic_{topic}_{uuid.uuid4().hex[:8]}",
                node_type=NodeType.TOPIC,
                content=topic,
                weight=0.6,
                metadata={
                    "source": "semantic_analysis",
                    "confidence": parsed_query.confidence
                }
            )
            enhanced_graph.add_node(topic_node)
        
        logger.info(f"图谱增强完成，新增节点数：{len(enhanced_graph.nodes) - len(user_graph.nodes)}")
        return enhanced_graph
    
    def _create_user_profile(self, user_input: str, user_graph: UserGraph, 
                           parsed_query: ParsedQuery) -> UserProfile:
        """创建用户画像"""
        user_id = f"user_{uuid.uuid4().hex[:8]}"
        
        profile = UserProfile(
            user_id=user_id,
            original_query=user_input,
            graph=user_graph,
            version=1  # 明确设置初始版本号
        )
        
        # 计算初始完整性
        profile.update_completeness()
        
        return profile
    
    def _create_fallback_profile(self, user_input: str) -> UserProfile:
        """创建降级画像"""
        logger.warning("创建降级用户画像")
        
        user_id = f"fallback_user_{uuid.uuid4().hex[:8]}"
        
        # 创建基础图谱
        basic_graph = UserGraph(user_id=user_id)
        
        # 添加基础的话题节点
        topic_node = GraphNode(
            node_id=f"topic_general_{uuid.uuid4().hex[:8]}",
            node_type=NodeType.TOPIC,
            content="一般性咨询",
            weight=0.5,
            metadata={"source": "fallback"}
        )
        basic_graph.add_node(topic_node)
        
        # 添加基础的情绪节点
        emotion_node = GraphNode(
            node_id=f"emotion_confused_{uuid.uuid4().hex[:8]}",
            node_type=NodeType.EMOTION,
            content="困惑迷茫",
            weight=0.6,
            metadata={"source": "fallback"}
        )
        basic_graph.add_node(emotion_node)
        
        return UserProfile(
            user_id=user_id,
            original_query=user_input,
            graph=basic_graph
        )
    
    def identify_missing_dimensions(self, user_profile: UserProfile, 
                                  reference_profiles: Optional[List[UserProfile]] = None) -> List[str]:
        """
        识别用户画像中缺失的重要维度
        
        Args:
            user_profile: 待分析的用户画像
            reference_profiles: 参考画像列表（可选）
            
        Returns:
            List[str]: 缺失的维度列表
        """
        logger.info("识别画像缺失维度")
        
        missing_dimensions = []
        node_counts = user_profile.graph.get_node_count_by_type()
        
        # 基于标准模板检查
        for node_type, template in self.standard_profile_template.items():
            current_count = node_counts.get(node_type, 0)
            min_required = template["min_nodes"]
            
            if current_count < min_required:
                missing_dimensions.extend([
                    f"{node_type.value}维度不足",
                    *template["key_areas"][:min_required - current_count]
                ])
        
        # 如果有参考画像，进行对比分析
        if reference_profiles:
            common_dimensions = self._analyze_common_dimensions(reference_profiles)
            current_dimensions = set(user_profile.graph.nodes.keys())
            
            for common_dim in common_dimensions:
                if common_dim not in current_dimensions:
                    missing_dimensions.append(f"缺少常见维度：{common_dim}")
        
        user_profile.missing_dimensions = missing_dimensions
        logger.info(f"识别到 {len(missing_dimensions)} 个缺失维度")
        
        return missing_dimensions
    
    def _analyze_common_dimensions(self, reference_profiles: List[UserProfile]) -> Set[str]:
        """分析参考画像中的常见维度"""
        dimension_counts = {}
        
        for profile in reference_profiles:
            for node in profile.graph.nodes.values():
                # 提取节点的主要特征作为维度
                dimension_key = f"{node.node_type.value}_{node.content[:20]}"
                dimension_counts[dimension_key] = dimension_counts.get(dimension_key, 0) + 1
        
        # 返回出现频率高于阈值的维度
        threshold = len(reference_profiles) * 0.3  # 30%的画像中都有
        common_dimensions = {
            dim for dim, count in dimension_counts.items() 
            if count >= threshold
        }
        
        return common_dimensions
    
    async def generate_clarifying_questions(self, user_profile: UserProfile,
                                          max_questions: int = 3) -> List[str]:
        """
        生成追问问题以补全画像
        
        Args:
            user_profile: 用户画像
            max_questions: 最大问题数
            
        Returns:
            List[str]: 追问问题列表
        """
        logger.info("生成追问问题")
        
        missing_dimensions = user_profile.missing_dimensions
        if not missing_dimensions:
            missing_dimensions = self.identify_missing_dimensions(user_profile)
        
        questions = []
        
        # 为每个缺失维度生成问题
        question_templates = {
            NodeType.EXPERIENCE: [
                "能否分享一个对您影响深刻的工作/学习经历？",
                "在面临重要选择时，您通常如何做决定？",
                "描述一次让您成长的挑战经历？"
            ],
            NodeType.BELIEF: [
                "什么是您最看重的人生价值？",
                "在做重要决定时，您的核心原则是什么？",
                "您认为什么样的生活方式是有意义的？"
            ],
            NodeType.EMOTION: [
                "目前您的情绪状态如何？有什么特别的感受吗？",
                "面对压力时，您通常如何调节情绪？",
                "什么事情最容易影响您的心情？"
            ],
            NodeType.TOPIC: [
                "除了当前的问题，您还关注哪些生活领域？",
                "最近有什么话题或事情特别引起您的思考？",
                "您希望在哪些方面得到更多建议？"
            ]
        }
        
        # 识别最需要补充的维度
        node_counts = user_profile.graph.get_node_count_by_type()
        priority_types = sorted(
            NodeType, 
            key=lambda nt: (node_counts.get(nt, 0), -self.dimension_weights.get(nt, 0))
        )
        
        # 为优先级高的维度生成问题
        for node_type in priority_types[:max_questions]:
            if node_counts.get(node_type, 0) < self.standard_profile_template[node_type]["min_nodes"]:
                template_questions = question_templates.get(node_type, [])
                if template_questions:
                    questions.append(template_questions[0])  # 选择第一个问题
        
        # 如果还需要更多问题，使用AI生成个性化问题
        if len(questions) < max_questions:
            try:
                ai_questions = await self._generate_ai_questions(user_profile, max_questions - len(questions))
                questions.extend(ai_questions)
            except Exception as e:
                logger.warning(f"AI问题生成失败，使用默认问题：{e}")
                # 使用通用问题补充
                default_questions = [
                    "能否详细描述一下您当前面临的具体情况？",
                    "这个问题对您来说最困难的部分是什么？",
                    "您希望通过解决这个问题达到什么目标？"
                ]
                questions.extend(default_questions[:max_questions - len(questions)])
        
        logger.info(f"生成了 {len(questions)} 个追问问题")
        return questions[:max_questions]
    
    async def _generate_ai_questions(self, user_profile: UserProfile, 
                                   num_questions: int) -> List[str]:
        """使用 LangChain 生成个性化追问问题"""
        try:
            logger.info("使用 LangChain 生成追问问题...")
            
            # 检查是否有新的 LangChain 方法
            if not hasattr(self.ai_service, 'get_clarifying_questions_chain'):
                logger.warning("AI服务未提供追问问题生成链，使用兼容模式")
                return await self._generate_ai_questions_legacy(user_profile, num_questions)
            
            # 获取追问问题生成链
            questions_chain = self.ai_service.get_clarifying_questions_chain()
            
            # 准备输入数据
            profile_summary = user_profile.to_summary()
            
            # 使用 LangChain 链生成问题
            from ..models.ai_response_models import ClarifyingQuestionsResult
            result: ClarifyingQuestionsResult = await questions_chain.ainvoke({
                "num_questions": num_questions,
                "original_query": user_profile.original_query,
                "completeness_score": user_profile.completeness_score,
                "node_counts": profile_summary['node_counts'],
                "key_beliefs": profile_summary['key_beliefs'],
                "emotional_patterns": profile_summary['emotional_patterns'],
                "missing_dimensions": user_profile.missing_dimensions
            })
            
            logger.info(f"LangChain 成功生成 {len(result.questions)} 个追问问题")
            return result.questions[:num_questions]
            
        except Exception as e:
            logger.error(f"LangChain 问题生成失败：{e}")
            return []
    
    async def _generate_ai_questions_legacy(self, user_profile: UserProfile, 
                                          num_questions: int) -> List[str]:
        """传统的 AI 问题生成方法（作为降级策略）"""
        try:
            profile_summary = user_profile.to_summary()
            
            prompt = f"""
基于以下用户画像信息，生成 {num_questions} 个个性化的追问问题，帮助更好地了解用户的价值观和需求。

用户画像摘要：
- 原始查询：{user_profile.original_query}
- 完整性评分：{user_profile.completeness_score:.2f}
- 节点统计：{profile_summary['node_counts']}
- 关键信念：{profile_summary['key_beliefs']}
- 情绪模式：{profile_summary['emotional_patterns']}
- 缺失维度：{user_profile.missing_dimensions}

请生成针对性的问题，帮助补充缺失信息。每个问题都应该：
1. 针对具体的缺失维度
2. 易于理解和回答
3. 能够揭示深层的价值观或经历

请以JSON格式返回：
{{"questions": ["问题1", "问题2", ...]}}
"""
            
            response = await self.ai_service.get_completion(
                prompt=prompt,
                max_tokens=500,
                temperature=0.7
            )
            
            data = json.loads(response)
            return data.get("questions", [])
            
        except Exception as e:
            logger.error(f"传统AI问题生成失败：{e}")
            return []
    
    async def update_profile_with_answers(self, user_profile: UserProfile,
                                        questions_and_answers: Dict[str, str]) -> UserProfile:
        """
        基于用户回答更新画像
        
        Args:
            user_profile: 原始用户画像
            questions_and_answers: 问题和回答的字典
            
        Returns:
            UserProfile: 更新后的用户画像
        """
        logger.info(f"基于 {len(questions_and_answers)} 个回答更新画像")
        
        try:
            # 分析所有回答，提取新的图谱元素
            all_answers = list(questions_and_answers.values())
            
            # 构建新的图谱元素
            answer_graph = await self.graph_builder.build_user_graph(
                contents=all_answers,
                user_context="追问回答补充"
            )
            
            # 合并到原始图谱
            merged_graph = user_profile.graph.merge_with(answer_graph)
            
            # 更新画像
            user_profile.graph = merged_graph
            user_profile.version += 1
            user_profile.updated_at = datetime.now()
            
            # 重新计算完整性
            new_completeness = user_profile.update_completeness()
            
            logger.info(f"画像更新完成，新完整性评分：{new_completeness:.2f}")
            return user_profile
            
        except Exception as e:
            logger.error(f"更新画像失败：{e}")
            return user_profile
    
    def validate_profile_completeness(self, user_profile: UserProfile,
                                    min_threshold: Optional[float] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        验证画像完整性
        
        Args:
            user_profile: 用户画像
            min_threshold: 最小完整性阈值
            
        Returns:
            Tuple[bool, Dict]: (是否达标, 详细评估报告)
        """
        threshold = min_threshold or self.min_completeness_threshold
        current_score = user_profile.completeness_score
        
        # 详细分析各维度
        node_counts = user_profile.graph.get_node_count_by_type()
        dimension_analysis = {}
        
        for node_type, template in self.standard_profile_template.items():
            current_count = node_counts.get(node_type, 0)
            min_required = template["min_nodes"]
            
            dimension_analysis[node_type.value] = {
                "current_count": current_count,
                "min_required": min_required,
                "is_sufficient": current_count >= min_required,
                "coverage_ratio": current_count / min_required if min_required > 0 else 1.0
            }
        
        is_complete = current_score >= threshold
        
        report = {
            "overall_score": current_score,
            "threshold": threshold,
            "is_complete": is_complete,
            "dimension_analysis": dimension_analysis,
            "missing_dimensions": user_profile.missing_dimensions,
            "suggestions": self._generate_completeness_suggestions(dimension_analysis)
        }
        
        logger.info(f"画像完整性验证：{current_score:.2f} >= {threshold} = {is_complete}")
        return is_complete, report
    
    def _generate_completeness_suggestions(self, dimension_analysis: Dict[str, Any]) -> List[str]:
        """生成完整性改进建议"""
        suggestions = []
        
        for dimension, analysis in dimension_analysis.items():
            if not analysis["is_sufficient"]:
                suggestions.append(f"建议补充{dimension}维度信息，当前{analysis['current_count']}个，建议至少{analysis['min_required']}个")
        
        if not suggestions:
            suggestions.append("画像信息已比较完整，可以进行匹配分析")
        
        return suggestions
    
    async def analyze_completeness(self, user_graph: UserGraph, 
                                 reference_graphs: Optional[List[UserGraph]] = None) -> Dict[str, Any]:
        """
        分析图谱完整性并生成追问问题
        
        Args:
            user_graph: 用户图谱
            reference_graphs: 参考图谱列表（可选）
            
        Returns:
            Dict[str, Any]: 完整性分析结果，包含分数、缺失维度和追问问题
        """
        logger.info("开始分析图谱完整性...")
        
        try:
            # 创建临时用户画像用于分析
            temp_profile = UserProfile(
                user_id="temp_analysis",
                original_query="图谱完整性分析",
                graph=user_graph
            )
            
            # 计算完整性分数
            completeness_score = temp_profile.update_completeness()
            
            # 识别缺失维度
            missing_dimensions = self.identify_missing_dimensions(temp_profile)
            
            # 生成追问问题
            follow_up_questions = await self.generate_clarifying_questions(
                temp_profile, max_questions=3
            )
            
            # 分析与参考图谱的差异
            missing_aspects = []
            if reference_graphs:
                missing_aspects = self._analyze_reference_differences(
                    user_graph, reference_graphs
                )
            
            # 判断是否完整
            is_complete = (completeness_score >= self.min_completeness_threshold and 
                          len(missing_dimensions) <= 2)
            
            result = {
                'completeness_score': completeness_score,
                'is_complete': is_complete,
                'missing_dimensions': missing_dimensions,
                'missing_aspects': missing_aspects,
                'follow_up_questions': follow_up_questions,
                'analysis_details': {
                    'node_counts': user_graph.get_node_count_by_type(),
                    'total_nodes': len(user_graph.nodes),
                    'total_edges': len(user_graph.edges),
                    'threshold_used': self.min_completeness_threshold
                }
            }
            
            logger.info(f"完整性分析完成：分数={completeness_score:.2f}，完整性={is_complete}")
            logger.info(f"缺失维度数量={len(missing_dimensions)}，追问问题数量={len(follow_up_questions)}")
            
            return result
            
        except Exception as e:
            logger.error(f"图谱完整性分析失败: {e}")
            # 返回默认结果
            return {
                'completeness_score': 0.5,
                'is_complete': False,
                'missing_dimensions': ["无法分析图谱完整性"],
                'missing_aspects': [],
                'follow_up_questions': [
                    "能否提供更多关于您的背景信息？",
                    "您在这个问题上有什么具体的期望？",
                    "除了当前描述的情况，还有其他相关的经历吗？"
                ],
                'analysis_details': {
                    'error': str(e),
                    'node_counts': {},
                    'total_nodes': 0,
                    'total_edges': 0,
                    'threshold_used': self.min_completeness_threshold
                }
            }
    
    def _analyze_reference_differences(self, user_graph: UserGraph, 
                                     reference_graphs: List[UserGraph]) -> List[str]:
        """分析用户图谱与参考图谱的差异"""
        try:
            user_node_types = set()
            user_contents = set()
            
            # 收集用户图谱的特征
            for node in user_graph.nodes.values():
                user_node_types.add(node.node_type)
                # 提取内容关键词
                content_words = node.content.lower().split()[:3]  # 取前3个词作为特征
                user_contents.update(content_words)
            
            # 分析参考图谱的常见特征
            reference_features = {}
            for ref_graph in reference_graphs[:10]:  # 最多分析10个参考图谱
                for node in ref_graph.nodes.values():
                    node_type = node.node_type
                    if node_type not in reference_features:
                        reference_features[node_type] = set()
                    
                    content_words = node.content.lower().split()[:3]
                    reference_features[node_type].update(content_words)
            
            # 识别缺失的常见特征
            missing_aspects = []
            
            for node_type, common_contents in reference_features.items():
                if node_type not in user_node_types:
                    missing_aspects.append(f"缺少{node_type.value}类型的信息")
                else:
                    # 检查是否缺少常见内容
                    missing_contents = common_contents - user_contents
                    if len(missing_contents) > 3:  # 如果缺少很多常见内容
                        sample_missing = list(missing_contents)[:2]
                        missing_aspects.append(f"{node_type.value}维度可能缺少：{', '.join(sample_missing)}")
            
            logger.info(f"参考差异分析完成，识别到{len(missing_aspects)}个潜在缺失")
            return missing_aspects[:5]  # 最多返回5个主要缺失
            
        except Exception as e:
            logger.warning(f"参考差异分析失败: {e}")
            return []
    
    async def close(self) -> None:
        """关闭用户画像构建器"""
        if self.semantic_analyzer:
            await self.semantic_analyzer.close()
        if self.graph_builder:
            await self.graph_builder.close()
        if self.ai_service:
            await self.ai_service.close()
        logger.info("用户画像构建器已关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            "min_completeness_threshold": self.min_completeness_threshold,
            "max_questions_per_session": self.max_questions_per_session,
            "dimension_weights": self.dimension_weights
        }
        
        # 合并子模块统计
        if hasattr(self.semantic_analyzer, 'get_cache_stats'):
            stats["semantic_analyzer"] = self.semantic_analyzer.get_cache_stats()
        
        if hasattr(self.graph_builder, 'get_cache_stats'):
            stats["graph_builder"] = self.graph_builder.get_cache_stats()
        
        return stats