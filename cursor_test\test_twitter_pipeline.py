"""
Twitter 平台 Pipeline 测试
验证平台切换功能和基本流程
"""
import pytest
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.pipeline import RedditResonancePipeline

@pytest.mark.asyncio
async def test_twitter_pipeline_initialization():
    """测试Twitter平台的Pipeline初始化"""
    try:
        # 测试Twitter平台初始化
        twitter_pipeline = RedditResonancePipeline(platform="twitter")
        
        # 验证平台设置
        assert twitter_pipeline.platform == "twitter"
        assert hasattr(twitter_pipeline, 'social_service')
        assert twitter_pipeline.social_service.__class__.__name__ == "TwitterService"
        
        # 验证向后兼容性
        assert twitter_pipeline.reddit_service is twitter_pipeline.social_service
        
        print("✅ Twitter平台初始化测试通过")
        
        # 清理资源
        await twitter_pipeline.close()
        
    except Exception as e:
        print(f"❌ Twitter平台初始化测试失败: {e}")
        raise

@pytest.mark.asyncio
async def test_reddit_pipeline_compatibility():
    """测试Reddit平台的向后兼容性"""
    try:
        # 测试默认Reddit平台
        reddit_pipeline = RedditResonancePipeline()  # 默认platform="reddit"
        
        # 验证平台设置
        assert reddit_pipeline.platform == "reddit"
        assert hasattr(reddit_pipeline, 'social_service')
        assert reddit_pipeline.social_service.__class__.__name__ == "RedditService"
        
        print("✅ Reddit平台兼容性测试通过")
        
        # 清理资源
        await reddit_pipeline.close()
        
    except Exception as e:
        print(f"❌ Reddit平台兼容性测试失败: {e}")
        raise

@pytest.mark.asyncio 
@pytest.mark.slow
async def test_twitter_service_connection():
    """测试Twitter服务连接（标记为慢速测试）"""
    try:
        twitter_pipeline = RedditResonancePipeline(platform="twitter")
        
        # 测试连接
        connection_ok = await twitter_pipeline.social_service.test_connection()
        
        if connection_ok:
            print("✅ Twitter服务连接测试通过")
        else:
            print("⚠️  Twitter服务连接测试失败（可能是网络问题）")
        
        # 清理资源
        await twitter_pipeline.close()
        
    except ImportError as e:
        print(f"⚠️  Twitter依赖缺失，跳过连接测试: {e}")
        pytest.skip("Twitter依赖缺失")
    except Exception as e:
        print(f"❌ Twitter服务连接测试错误: {e}")
        # 不抛出异常，因为可能是网络或配置问题

@pytest.mark.asyncio
@pytest.mark.slow  
async def test_twitter_search_basic():
    """测试Twitter基本搜索功能（标记为慢速测试）"""
    try:
        twitter_pipeline = RedditResonancePipeline(platform="twitter")
        
        # 测试简单搜索
        test_keywords = ["生活困惑"]
        
        # 执行搜索（只测试第一阶段）
        try:
            search_results = await twitter_pipeline.social_service.search_posts_by_keywords_enhanced(
                keywords=test_keywords,
                limit=5,  # 只搜索5条，快速测试
                time_filter="week"  # 限制时间范围
            )
            
            print(f"✅ Twitter搜索测试完成，找到 {len(search_results)} 条结果")
            
            # 验证结果格式
            if search_results:
                first_result = search_results[0]
                expected_fields = ['id', 'text', 'score', 'created_utc', 'platform']
                for field in expected_fields:
                    assert field in first_result, f"缺少字段: {field}"
                
                assert first_result['platform'] == 'twitter'
                print("✅ Twitter结果格式验证通过")
            
        except Exception as search_error:
            print(f"⚠️  Twitter搜索测试失败（可能是网络或API问题）: {search_error}")
        
        # 清理资源
        await twitter_pipeline.close()
        
    except ImportError as e:
        print(f"⚠️  Twitter依赖缺失，跳过搜索测试: {e}")
        pytest.skip("Twitter依赖缺失")
    except Exception as e:
        print(f"❌ Twitter搜索测试错误: {e}")

@pytest.mark.asyncio
async def test_platform_interface_compatibility():
    """测试平台接口兼容性"""
    try:
        # 测试两个平台的接口一致性
        twitter_pipeline = RedditResonancePipeline(platform="twitter")
        reddit_pipeline = RedditResonancePipeline(platform="reddit")
        
        # 检查关键方法是否存在
        required_methods = [
            'search_posts_by_keywords_enhanced',
            'rerank_posts_by_embedding', 
            'llm_final_rank_posts',
            'extract_quality_commenters_detailed',
            'get_user_comprehensive_history',
            'close',
            'test_connection'
        ]
        
        for method_name in required_methods:
            assert hasattr(twitter_pipeline.social_service, method_name), f"Twitter服务缺少方法: {method_name}"
            assert hasattr(reddit_pipeline.social_service, method_name), f"Reddit服务缺少方法: {method_name}"
        
        print("✅ 平台接口兼容性测试通过")
        
        # 清理资源
        await twitter_pipeline.close()
        await reddit_pipeline.close()
        
    except Exception as e:
        print(f"❌ 平台接口兼容性测试失败: {e}")
        raise

if __name__ == "__main__":
    print("🚀 开始Twitter平台测试...")
    
    # 运行基础测试
    asyncio.run(test_twitter_pipeline_initialization())
    asyncio.run(test_reddit_pipeline_compatibility()) 
    asyncio.run(test_platform_interface_compatibility())
    
    print("\n🔄 运行扩展测试（需要网络连接）...")
    try:
        asyncio.run(test_twitter_service_connection())
        asyncio.run(test_twitter_search_basic())
    except Exception as e:
        print(f"⚠️  扩展测试跳过: {e}")
    
    print("\n✅ Twitter平台测试完成！") 