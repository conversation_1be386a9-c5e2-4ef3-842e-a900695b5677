#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的业务流程工具

业务需求：
1. 搜索关键词，获取前30条热门推文
2. 从这30条推文中提取作者（去重）
3. 抓取这些作者的历史推文，构建用户画像

成本分析：
- 步骤1：搜索30条推文 = 30 records (使用Posts Scraper)
- 步骤3：抓取N个作者 = N records (使用Profile Scraper)
- 总成本：30 + N records（N通常<30，因为可能有重复作者）
"""

import argparse
import json
import os
import urllib.parse
import requests
import time
from datetime import datetime

# API配置
API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
POSTS_DATASET_ID = "gd_lwxkxvnf1cynvib9co"      # 搜索推文用
PROFILES_DATASET_ID = "gd_lwxmeb2u1cniijd7t4"    # 抓取用户用

class TwitterWorkflow:
    """Twitter搜索+用户画像完整工作流"""
    
    def __init__(self, api_token=API_TOKEN):
        self.api_token = api_token
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }
        
    def step1_search_top_posts(self, search_query, max_posts=30):
        """
        步骤1：搜索前N条推文
        成本：N records（按推文计费）
        """
        print(f"🔍 步骤1：搜索前{max_posts}条推文")
        print(f"关键词: '{search_query}'")
        
        # 构造搜索URL
        encoded_query = urllib.parse.quote_plus(search_query)
        search_url = f"https://x.com/search?q={encoded_query}&src=typed_query"
        
        # 注意：这里需要传入推文URL列表，但搜索API不支持
        # 实际上我们需要先手动获取推文URL列表
        print("⚠️  注意：Posts Scraper需要具体的推文URL列表")
        print(f"📍 搜索页面: {search_url}")
        print("💡 建议：先手动复制前30条推文URL，或使用其他方法获取")
        
        # 这里返回搜索URL供参考
        return {
            "search_url": search_url,
            "note": "需要手动获取推文URL列表"
        }
    
    def step2_extract_authors(self, posts_data):
        """
        步骤2：从推文数据中提取作者列表（去重）
        """
        print("👥 步骤2：提取推文作者")
        
        authors = set()
        if isinstance(posts_data, list):
            for post in posts_data:
                author = post.get("user_posted")
                if author:
                    authors.add(author)
        
        author_list = list(authors)
        print(f"发现 {len(author_list)} 个唯一作者")
        return author_list
    
    def step3_profile_authors(self, author_list, posts_per_author=200):
        """
        步骤3：抓取作者的历史推文
        成本：len(author_list) records（按用户计费）
        """
        print(f"📊 步骤3：抓取 {len(author_list)} 个作者的历史推文")
        print(f"每作者推文数: {posts_per_author}")
        print(f"💰 成本: {len(author_list)} records")
        
        data = []
        for author in author_list:
            # 构造用户URL
            if not author.startswith("https://"):
                author_url = f"https://x.com/{author.replace('@', '')}"
            else:
                author_url = author
                
            data.append({
                "url": author_url,
                "max_number_of_posts": posts_per_author
            })
        
        return self._trigger_profile_collection(data, f"作者画像_{len(author_list)}人")
    
    def _trigger_profile_collection(self, data, task_name):
        """触发Profile数据采集"""
        url = "https://api.brightdata.com/datasets/v3/trigger"
        params = {
            "dataset_id": PROFILES_DATASET_ID,
            "include_errors": "true",
        }
        
        try:
            print("🚀 正在触发Profile采集...")
            response = requests.post(url, headers=self.headers, params=params, json=data, timeout=30)
            result = response.json()
            
            if response.status_code == 200:
                snapshot_id = result.get("snapshot_id")
                print(f"✅ Profile采集已启动！")
                print(f"📊 快照ID: {snapshot_id}")
                
                # 保存任务信息
                task_info = {
                    "task_name": task_name,
                    "snapshot_id": snapshot_id,
                    "timestamp": datetime.now().isoformat(),
                    "user_count": len(data),
                    "billing_note": f"按用户Profile计费：{len(data)} records"
                }
                
                return {"success": True, "snapshot_id": snapshot_id, "task_info": task_info}
                
            else:
                print(f"❌ 采集失败: {result}")
                return {"success": False, "error": result}
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return {"success": False, "error": str(e)}

def explain_workflow():
    """解释完整的工作流程"""
    print("📋 完整业务流程说明")
    print("=" * 60)
    
    print("\n🎯 目标：")
    print("搜索关键词 → 前30条推文 → 提取作者 → 构建用户画像")
    
    print("\n📊 流程分解：")
    print("1️⃣ 搜索前30条推文")
    print("   - 工具：Posts Scraper")
    print("   - 成本：30 records（按推文计费）")
    print("   - 数据：推文内容 + 作者信息")
    
    print("\n2️⃣ 提取唯一作者")
    print("   - 工具：数据处理脚本")
    print("   - 成本：0（本地处理）")
    print("   - 结果：去重后的作者列表（通常10-25人）")
    
    print("\n3️⃣ 抓取作者历史推文")
    print("   - 工具：Profile Scraper")
    print("   - 成本：N records（N=作者数量，按用户计费）")
    print("   - 数据：每作者最多3200条历史推文")
    
    print("\n💰 总成本估算：")
    print("• 搜索阶段：30 records")
    print("• 画像阶段：10-25 records（取决于作者去重后数量）")
    print("• 总计：40-55 records")
    
    print("\n✨ 优势：")
    print("• 获得精准的相关用户（对关键词有发言的活跃用户）")
    print("• 每个用户包含大量历史数据用于画像分析")
    print("• 成本可控且可预测")

def demo_workflow():
    """演示工作流程（不实际执行）"""
    print("\n🎭 工作流程演示")
    print("=" * 40)
    
    workflow = TwitterWorkflow()
    
    # 模拟输入
    search_query = "openai gpt"
    
    # 步骤1：搜索推文
    search_result = workflow.step1_search_top_posts(search_query, 30)
    print(f"搜索URL: {search_result['search_url']}")
    
    print("\n" + "-" * 40)
    
    # 模拟推文数据（实际需要从步骤1获取）
    mock_posts = [
        {"user_posted": "elonmusk", "content": "..."},
        {"user_posted": "openai", "content": "..."},
        {"user_posted": "sama", "content": "..."},
        {"user_posted": "elonmusk", "content": "..."},  # 重复作者
    ]
    
    # 步骤2：提取作者
    authors = workflow.step2_extract_authors(mock_posts)
    print(f"提取的作者: {authors}")
    
    print("\n" + "-" * 40)
    
    # 步骤3：抓取画像（演示模式，不实际执行）
    print("步骤3：抓取作者画像（演示模式）")
    print(f"将为 {len(authors)} 个作者创建画像")
    print("每作者200条历史推文")
    print(f"总成本：30（搜索）+ {len(authors)}（画像）= {30 + len(authors)} records")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Twitter搜索+画像工作流程")
    parser.add_argument("--demo", action="store_true", help="演示工作流程")
    parser.add_argument("--explain", action="store_true", help="解释流程")
    parser.add_argument("--authors", help="直接输入作者列表，跳过搜索步骤")
    parser.add_argument("--posts_per_author", type=int, default=200, help="每作者推文数")
    
    args = parser.parse_args()
    
    import sys
    if args.explain or len(sys.argv) == 1:
        explain_workflow()
    
    if args.demo:
        demo_workflow()
    
    if args.authors:
        # 直接执行步骤3
        workflow = TwitterWorkflow()
        author_list = [a.strip() for a in args.authors.split(",")]
        result = workflow.step3_profile_authors(author_list, args.posts_per_author)
        
        if result.get("success"):
            print(f"\n✅ 成功启动 {len(author_list)} 个作者的画像抓取")
            print(f"快照ID: {result['snapshot_id']}")
        else:
            print("\n❌ 抓取失败") 