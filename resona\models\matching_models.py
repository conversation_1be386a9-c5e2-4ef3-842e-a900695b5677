"""
匹配相关数据模型 - 用于共鸣评估和推荐生成
"""
from datetime import datetime
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from .user_models import UserProfile, CandidateUser
from .graph_models import UserGraph, NodeType, RelationType


class ResonanceScore(BaseModel):
    """共鸣评分详情"""
    overall_score: float = Field(..., description="总体共鸣分数 (0-1)")
    
    # 细分评分
    structural_similarity: float = Field(default=0.0, description="图谱结构相似度")
    semantic_similarity: float = Field(default=0.0, description="语义相似度")
    emotional_alignment: float = Field(default=0.0, description="情绪匹配度")
    value_compatibility: float = Field(default=0.0, description="价值观兼容性")
    experience_overlap: float = Field(default=0.0, description="经验重叠度")
    
    # 匹配细节
    matched_nodes: Dict[str, str] = Field(default_factory=dict, description="匹配的节点对")
    matched_paths: List[List[str]] = Field(default_factory=list, description="匹配的路径")
    shared_themes: List[str] = Field(default_factory=list, description="共同主题")
    
    # 元数据
    calculation_method: str = Field(default="hybrid", description="计算方法")
    confidence: float = Field(default=1.0, description="评分置信度")
    
    def calculate_weighted_score(self, weights: Optional[Dict[str, float]] = None) -> float:
        """计算加权总分"""
        if weights is None:
            weights = {
                'structural': 0.25,
                'semantic': 0.25,
                'emotional': 0.20,
                'value': 0.20,
                'experience': 0.10
            }
        
        self.overall_score = (
            self.structural_similarity * weights.get('structural', 0.25) +
            self.semantic_similarity * weights.get('semantic', 0.25) +
            self.emotional_alignment * weights.get('emotional', 0.20) +
            self.value_compatibility * weights.get('value', 0.20) +
            self.experience_overlap * weights.get('experience', 0.10)
        )
        
        return self.overall_score
    
    def get_strength_indicators(self) -> Dict[str, str]:
        """获取强度指示器"""
        indicators = {}
        
        score_ranges = [
            (0.9, "极强"),
            (0.8, "很强"),
            (0.7, "强"),
            (0.6, "中等"),
            (0.4, "较弱"),
            (0.0, "弱")
        ]
        
        scores = {
            "结构相似度": self.structural_similarity,
            "语义相似度": self.semantic_similarity,
            "情绪匹配度": self.emotional_alignment,
            "价值兼容性": self.value_compatibility,
            "经验重叠度": self.experience_overlap
        }
        
        for name, score in scores.items():
            for threshold, label in score_ranges:
                if score >= threshold:
                    indicators[name] = label
                    break
        
        return indicators


class RankedCandidate(BaseModel):
    """排序后的候选用户"""
    candidate: CandidateUser = Field(..., description="候选用户")
    resonance_score: ResonanceScore = Field(..., description="共鸣评分")
    rank: int = Field(..., description="排名")
    
    # 推荐理由
    primary_resonance_reasons: List[str] = Field(default_factory=list, description="主要共鸣原因")
    supporting_evidence: List[str] = Field(default_factory=list, description="支撑证据")
    potential_concerns: List[str] = Field(default_factory=list, description="潜在顾虑")
    
    def get_recommendation_strength(self) -> str:
        """获取推荐强度等级"""
        score = self.resonance_score.overall_score
        
        if score >= 0.8:
            return "强烈推荐"
        elif score >= 0.6:
            return "推荐"
        elif score >= 0.4:
            return "谨慎推荐"
        else:
            return "不推荐"


class RecommendationSummary(BaseModel):
    """推荐摘要"""
    summary_text: str = Field(..., description="摘要文本")
    key_connection_points: List[str] = Field(default_factory=list, description="关键连接点")
    shared_experiences: List[str] = Field(default_factory=list, description="共同经历")
    complementary_perspectives: List[str] = Field(default_factory=list, description="互补观点")
    
    # 交流建议
    conversation_starters: List[str] = Field(default_factory=list, description="话题开场白")
    suggested_approach: str = Field(default="", description="建议的交流方式")
    tone_recommendation: str = Field(default="", description="语调建议")
    
    def generate_opening_message(self, user_name: str = "朋友") -> str:
        """生成开场消息"""
        if not self.conversation_starters:
            return f"Hi，看到你的一些想法很有共鸣，想和你聊聊。"
        
        starter = self.conversation_starters[0]
        return f"Hi，{starter} 感觉我们可能有相似的经历，想和你交流一下想法。"


class MatchingContext(BaseModel):
    """匹配上下文信息"""
    user_profile: UserProfile = Field(..., description="查询用户画像")
    candidate_pool_size: int = Field(..., description="候选池大小")
    search_parameters: Dict[str, Any] = Field(default_factory=dict, description="搜索参数")
    
    # 匹配策略
    matching_strategy: str = Field(default="hybrid", description="匹配策略")
    weights: Dict[str, float] = Field(default_factory=dict, description="评分权重")
    
    # 过滤条件
    min_score_threshold: float = Field(default=0.3, description="最低分数阈值")
    max_results: int = Field(default=5, description="最大结果数")
    
    # 时间信息
    search_started_at: datetime = Field(default_factory=datetime.utcnow, description="搜索开始时间")
    total_processing_time: Optional[float] = Field(default=None, description="总处理时间（秒）")


class MatchingResult(BaseModel):
    """完整的匹配结果"""
    context: MatchingContext = Field(..., description="匹配上下文")
    ranked_candidates: List[RankedCandidate] = Field(default_factory=list, description="排序后的候选用户")
    
    # 结果统计
    total_candidates_processed: int = Field(default=0, description="处理的候选用户总数")
    candidates_above_threshold: int = Field(default=0, description="超过阈值的候选数")
    
    # 系统信息
    matching_version: str = Field(default="2.0", description="匹配算法版本")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="生成时间")
    
    def get_top_recommendations(self, n: int = 3) -> List[RankedCandidate]:
        """获取前N个推荐"""
        return self.ranked_candidates[:n]
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取结果统计摘要"""
        if not self.ranked_candidates:
            return {"message": "无匹配结果"}
        
        scores = [candidate.resonance_score.overall_score 
                 for candidate in self.ranked_candidates]
        
        return {
            "推荐用户数": len(self.ranked_candidates),
            "最高分": max(scores),
            "平均分": sum(scores) / len(scores),
            "处理候选数": self.total_candidates_processed,
            "成功率": f"{(len(scores) / max(self.total_candidates_processed, 1) * 100):.1f}%"
        }
    
    def generate_recommendations_with_summaries(self) -> List[Dict[str, Any]]:
        """生成包含摘要的推荐列表"""
        recommendations = []
        
        for candidate in self.ranked_candidates:
            rec = {
                "rank": candidate.rank,
                "username": candidate.candidate.username,
                "overall_score": candidate.resonance_score.overall_score,
                "recommendation_strength": candidate.get_recommendation_strength(),
                "strength_indicators": candidate.resonance_score.get_strength_indicators(),
                "primary_reasons": candidate.primary_resonance_reasons,
                "shared_themes": candidate.resonance_score.shared_themes
            }
            recommendations.append(rec)
        
        return recommendations


class MatchingMetrics(BaseModel):
    """匹配性能指标"""
    total_processing_time: float = Field(..., description="总处理时间")
    llm_calls_count: int = Field(default=0, description="LLM调用次数")
    reddit_api_calls: int = Field(default=0, description="Reddit API调用次数")
    
    # 各阶段耗时
    query_parsing_time: float = Field(default=0.0, description="查询解析时间")
    content_retrieval_time: float = Field(default=0.0, description="内容检索时间")
    graph_building_time: float = Field(default=0.0, description="图谱构建时间")
    matching_calculation_time: float = Field(default=0.0, description="匹配计算时间")
    
    # 质量指标
    successful_matches: int = Field(default=0, description="成功匹配数")
    failed_extractions: int = Field(default=0, description="提取失败数")
    
    def calculate_efficiency_score(self) -> float:
        """计算效率分数"""
        if self.total_processing_time <= 0:
            return 0.0
        
        # 基于时间和成功率的效率评分
        success_rate = self.successful_matches / max(
            self.successful_matches + self.failed_extractions, 1
        )
        
        # 理想处理时间为30秒
        time_efficiency = min(30.0 / self.total_processing_time, 1.0)
        
        return (success_rate * 0.7 + time_efficiency * 0.3)


class GraphMatchingResult(BaseModel):
    """图谱匹配结果详情"""
    query_graph: UserGraph = Field(..., description="查询用户图谱")
    candidate_graph: UserGraph = Field(..., description="候选用户图谱")
    
    # 匹配详情
    node_mappings: Dict[str, str] = Field(default_factory=dict, description="节点映射")
    edge_alignments: List[Dict[str, Any]] = Field(default_factory=list, description="边对齐")
    subgraph_matches: List[Dict[str, Any]] = Field(default_factory=list, description="子图匹配")
    
    # 匹配分数
    structural_score: float = Field(default=0.0, description="结构匹配分数")
    semantic_score: float = Field(default=0.0, description="语义匹配分数")
    
    def get_matched_subgraphs(self) -> List[Dict[str, Any]]:
        """获取匹配的子图"""
        return self.subgraph_matches
    
    def find_strongest_connections(self, top_k: int = 3) -> List[Dict[str, Any]]:
        """找到最强的连接"""
        connections = []
        
        for mapping in self.node_mappings.items():
            query_node_id, candidate_node_id = mapping
            query_node = self.query_graph.get_node(query_node_id)
            candidate_node = self.candidate_graph.get_node(candidate_node_id)
            
            if query_node and candidate_node:
                connections.append({
                    "query_content": query_node.content,
                    "candidate_content": candidate_node.content,
                    "node_type": query_node.node_type.value,
                    "strength": (query_node.weight + candidate_node.weight) / 2
                })
        
        # 按强度排序
        connections.sort(key=lambda x: x["strength"], reverse=True)
        return connections[:top_k] 