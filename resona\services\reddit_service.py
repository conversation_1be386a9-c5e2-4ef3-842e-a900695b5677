"""
Reddit 数据获取服务
"""
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Any
import asyncpraw
import praw
from praw.models import Redditor, Submission, Comment
from tenacity import retry, stop_after_attempt, wait_exponential
import logging
import sys
import os
# 添加aiohttp导入支持代理配置
from aiohttp import ClientSession

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.config import settings
from models import RedditUser, RedditPost, RedditComment
from .base_social_service import BaseSocialService

logger = logging.getLogger(__name__)

class RedditService(BaseSocialService):
    """Reddit API 服务类"""
    
    def __init__(self):
        """初始化 Reddit 客户端"""
        # 如果未配置凭据，则使用匿名只读模式
        self.client_id = settings.reddit_client_id if settings.reddit_client_id else None
        self.client_secret = settings.reddit_client_secret if settings.reddit_client_secret else None
        
        # 读取代理并设置环境变量（PRAW 会自动使用）
        proxy_url = os.getenv("HTTPS_PROXY") or os.getenv("HTTP_PROXY")
        if proxy_url:
            logger.info(f"使用代理访问 Reddit: {proxy_url}")
            # 确保环境变量已设置
            os.environ["HTTPS_PROXY"] = proxy_url
            os.environ["HTTP_PROXY"] = proxy_url
        
        # 同步客户端
        self.reddit = praw.Reddit(
            client_id=self.client_id,
            client_secret=self.client_secret,
            user_agent=settings.reddit_user_agent
        )
        self.reddit.read_only = True
        
        # 异步客户端 - 延迟初始化避免在非事件循环环境中创建ClientSession
        self.async_reddit = None
        self._async_reddit_initialized = False
    
    async def _ensure_async_reddit(self):
        """确保异步Reddit客户端已初始化"""
        if not self._async_reddit_initialized:
            # 在异步环境中创建ClientSession和异步客户端
            session = ClientSession(trust_env=True)
            self.async_reddit = asyncpraw.Reddit(
                client_id=self.client_id,
                client_secret=self.client_secret,
                user_agent=settings.reddit_user_agent,
                requestor_kwargs={"session": session}  # 传递支持代理的session
            )
            self.async_reddit.read_only = True
            self._async_reddit_initialized = True
            logger.debug("异步Reddit客户端初始化完成")
        
        # 返回已初始化的实例，方便调用方直接使用
        return self.async_reddit
    
    async def search_users_by_topic(self, query: str, limit: int = 50) -> List[str]:
        """
        根据主题搜索相关用户
        
        Args:
            query: 搜索查询
            limit: 返回用户数限制
            
        Returns:
            用户名列表
        """
        await self._ensure_async_reddit()
        
        users: Set[str] = set()
        
        try:
            logger.info(f"Reddit服务：开始搜索用户")
            logger.info(f"  - 搜索关键词: '{query}'")
            logger.info(f"  - 最大返回数量: {limit}")
            logger.info(f"  - 将在以下subreddits中搜索: {', '.join(settings.reddit_subreddits)}")
            
            # 在相关 subreddit 中搜索帖子
            for subreddit_name in settings.reddit_subreddits:
                try:
                    logger.info(f"  正在搜索 r/{subreddit_name}...")
                    subreddit = await self.async_reddit.subreddit(subreddit_name)
                    
                    # 搜索最近的相关帖子
                    search_count = 0
                    async for submission in subreddit.search(query, limit=10, time_filter="month"):
                        search_count += 1
                        logger.debug(f"找到帖子: {submission.title[:50]}...")
                        
                        # 添加作者
                        if submission.author and submission.author.name != "[deleted]":
                            users.add(submission.author.name)
                        
                        # 获取热门评论的作者
                        try:
                            await submission.load()
                            await submission.comments.replace_more(limit=0)
                            
                            for comment in submission.comments[:5]:  # 前5个评论
                                if comment.author and comment.author.name != "[deleted]":
                                    users.add(comment.author.name)
                        except Exception as comment_error:
                            logger.debug(f"获取评论时出错: {comment_error}")
                            continue
                        
                        if len(users) >= limit:
                            break
                    
                    logger.info(f"在 r/{subreddit_name} 中搜索到 {search_count} 个帖子，累计用户: {len(users)}")
                    
                    if len(users) >= limit:
                        break
                        
                except Exception as subreddit_error:
                    logger.error(f"搜索 r/{subreddit_name} 时出错: {subreddit_error}")
                    continue
                    
        except Exception as e:
            logger.error(f"搜索Reddit用户时出错: {e}")
            
        logger.info(f"搜索完成，找到 {len(users)} 个用户")
        return list(users)[:limit]
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def get_user_history(self, username: str, limit: int = 100) -> Optional[RedditUser]:
        """
        获取用户的发帖和评论历史
        
        Args:
            username: Reddit 用户名
            limit: 获取的帖子/评论数限制
            
        Returns:
            RedditUser 对象或 None
        """
        await self._ensure_async_reddit()
        
        try:
            redditor = await self.async_reddit.redditor(username)
            
            # 获取用户信息
            await redditor.load()
            
            # 获取发帖历史
            posts = []
            async for submission in redditor.submissions.new(limit=limit//2):
                if submission.selftext and len(submission.selftext) > 50:  # 只要有内容的帖子
                    posts.append(RedditPost(
                        id=submission.id,
                        text=f"{submission.title}\n\n{submission.selftext}",
                        timestamp=datetime.fromtimestamp(getattr(submission, 'created_utc', 0)) if getattr(submission, 'created_utc', 0) else datetime.now(),
                        subreddit=submission.subreddit.display_name,
                        score=submission.score
                    ))
            
            # 获取评论历史
            comments = []
            async for comment in redditor.comments.new(limit=limit//2):
                if len(comment.body) > 20:  # 过滤太短的评论
                    comments.append(RedditComment(
                        id=comment.id,
                        text=comment.body,
                        timestamp=datetime.fromtimestamp(getattr(comment, 'created_utc', 0)) if getattr(comment, 'created_utc', 0) else datetime.now(),
                        subreddit=comment.subreddit.display_name,
                        score=comment.score
                    ))
            
            # 只返回有足够内容的用户
            if len(posts) + len(comments) >= 5:
                return RedditUser(
                    username=username,
                    posts=posts,
                    comments=comments
                )
            
            return None
            
        except Exception as e:
            logger.error(f"获取用户 {username} 历史时出错: {e}")
            return None
    
    async def batch_get_users(self, usernames: List[str]) -> List[RedditUser]:
        """
        批量获取多个用户的历史数据
        
        Args:
            usernames: 用户名列表
            
        Returns:
            RedditUser 对象列表
        """
        tasks = [self.get_user_history(username) for username in usernames]
        results = await asyncio.gather(*tasks)
        
        # 过滤掉 None 结果
        return [user for user in results if user is not None]
    
    async def get_trending_users(self, subreddit_names: List[str] = None, limit: int = 50) -> List[str]:
        """
        获取指定 subreddit 中的活跃用户
        
        Args:
            subreddit_names: subreddit 名称列表，默认使用配置中的列表
            limit: 返回用户数限制
            
        Returns:
            用户名列表
        """
        await self._ensure_async_reddit()
        
        if subreddit_names is None:
            subreddit_names = settings.reddit_subreddits
            
        users: Set[str] = set()
        
        for subreddit_name in subreddit_names:
            try:
                subreddit = await self.async_reddit.subreddit(subreddit_name)
                
                # 获取热门帖子的作者
                async for submission in subreddit.hot(limit=25):
                    if submission.author and submission.author.name != "[deleted]":
                        users.add(submission.author.name)
                        
                    if len(users) >= limit:
                        break
                        
            except Exception as e:
                logger.error(f"获取 r/{subreddit_name} 活跃用户时出错: {e}")
                
        return list(users)[:limit]
    
    async def search_posts_by_content(self, query_text: str, limit: int = 100) -> List[RedditPost]:
        """
        新方法：直接用用户输入搜索相关帖子
        
        Args:
            query_text: 用户原始输入文本
            limit: 搜索帖子数量限制
            
        Returns:
            相关帖子列表
        """
        await self._ensure_async_reddit()
        
        posts = []
        
        try:
            logger.info(f"直接搜索帖子：'{query_text[:100]}...'")
            logger.info(f"目标帖子数量：{limit}")
            
            # 在所有配置的subreddit中搜索
            for subreddit_name in settings.reddit_subreddits:
                try:
                    logger.info(f"在 r/{subreddit_name} 中搜索...")
                    subreddit = await self.async_reddit.subreddit(subreddit_name)
                    
                    # 搜索相关帖子
                    search_count = 0
                    async for submission in subreddit.search(query_text, limit=15, time_filter="month", sort="relevance"):
                        search_count += 1
                        
                        # 只要有内容的帖子
                        if submission.selftext and len(submission.selftext) > 100:
                            posts.append(RedditPost(
                                id=submission.id,
                                text=f"{submission.title}\n\n{submission.selftext}",
                                timestamp=datetime.fromtimestamp(getattr(submission, 'created_utc', 0)) if getattr(submission, 'created_utc', 0) else datetime.now(),
                                subreddit=submission.subreddit.display_name,
                                score=submission.score
                            ))
                            
                        if len(posts) >= limit:
                            break
                    
                    logger.info(f"在 r/{subreddit_name} 找到 {search_count} 个相关帖子")
                    
                    if len(posts) >= limit:
                        break
                        
                except Exception as subreddit_error:
                    logger.error(f"搜索 r/{subreddit_name} 时出错: {subreddit_error}")
                    continue
                    
        except Exception as e:
            logger.error(f"搜索帖子时出错: {e}")
            
        logger.info(f"共找到 {len(posts)} 个相关帖子")
        return posts
    
    async def extract_quality_commenters(self, posts: List[RedditPost], 
                                       min_score: int = 0, 
                                       min_length: int = 100,
                                       max_commenters: int = 50) -> List[str]:
        """
        新方法：从帖子中提取优质评论者（只筛选一级评论）
        
        Args:
            posts: 相关帖子列表
            min_score: 评论最低分数
            min_length: 评论最短长度
            max_commenters: 最大评论者数量
            
        Returns:
            优质评论者用户名列表
        """
        await self._ensure_async_reddit()
        
        quality_commenters = {}  # username -> quality_score
        
        try:
            logger.info(f"开始从 {len(posts)} 个帖子中提取优质评论者...")
            logger.info(f"筛选条件：评论分数≥{min_score}，长度≥{min_length}字符，仅一级评论")
            
            for i, post in enumerate(posts):
                try:
                    logger.debug(f"处理帖子 {i+1}/{len(posts)}: {post.id}")
                    
                    # 获取Reddit帖子对象
                    submission = await self.async_reddit.submission(id=post.id)
                    await submission.load()
                    
                    # 展开评论（限制数量避免过度加载）
                    await submission.comments.replace_more(limit=5)
                    
                    # 分析评论
                    comment_count = 0
                    top_level_count = 0
                    for comment in submission.comments.list()[:30]:  # 限制评论数
                        # 检查是否为一级评论（直接回复帖子）
                        if hasattr(comment, 'parent_id') and comment.parent_id.startswith('t3_'):
                            top_level_count += 1
                            
                            if (comment.author and 
                                comment.author.name != "[deleted]" and
                                len(comment.body) >= min_length and 
                                comment.score >= min_score):
                                
                                username = comment.author.name
                                
                                # 计算评论质量分数
                                quality_score = (
                                    comment.score * 0.4 +  # Reddit投票分数
                                    len(comment.body) / 50 * 0.3 +  # 评论长度
                                    (10 if comment.score > 20 else 5) * 0.3  # 高分奖励
                                )
                                
                                # 累加用户的质量分数
                                if username in quality_commenters:
                                    quality_commenters[username] += quality_score
                                else:
                                    quality_commenters[username] = quality_score
                                
                                comment_count += 1
                    
                    logger.debug(f"帖子 {post.id} 找到 {top_level_count} 个一级评论，{comment_count} 个优质评论")
                    
                except Exception as post_error:
                    logger.error(f"处理帖子 {post.id} 时出错: {post_error}")
                    continue
                    
        except Exception as e:
            logger.error(f"提取评论者时出错: {e}")
        
        # 按质量分数排序并返回
        sorted_commenters = sorted(quality_commenters.items(), 
                                 key=lambda x: x[1], reverse=True)
        
        result = [username for username, score in sorted_commenters[:max_commenters]]
        
        logger.info(f"成功提取 {len(result)} 个优质评论者")
        for i, (username, score) in enumerate(sorted_commenters[:5]):
            logger.info(f"  Top {i+1}: {username} (质量分数: {score:.2f})")
        
        return result
    
    async def extract_quality_commenters_detailed(self, posts: List[RedditPost], 
                                                min_score: int = 0, 
                                                min_length: int = 100,
                                                max_commenters: int = 50) -> Dict[str, Any]:
        """
        增强版：从帖子中提取优质评论者（记录详细信息和选择原因）
        
        Args:
            posts: 相关帖子列表
            min_score: 评论最低分数
            min_length: 评论最短长度
            max_commenters: 最大评论者数量
            
        Returns:
            包含所有评论者详细信息的字典
        """
        await self._ensure_async_reddit()
        
        # 存储所有评论者的详细信息
        all_commenters = {}  # username -> {comments: [], total_score: float, quality_metrics: {}}
        selected_commenters = []  # 最终选中的评论者列表
        
        extraction_stats = {
            "total_posts_processed": 0,
            "total_comments_found": 0,
            "top_level_comments": 0,
            "qualified_comments": 0,
            "unique_commenters": 0,
            "posts_with_errors": 0
        }
        
        try:
            logger.info(f"开始详细提取评论者信息...")
            logger.info(f"处理 {len(posts)} 个帖子，筛选条件：分数≥{min_score}，长度≥{min_length}字符")
            
            for i, post in enumerate(posts):
                try:
                    logger.debug(f"处理帖子 {i+1}/{len(posts)}: {post.id}")
                    extraction_stats["total_posts_processed"] += 1
                    
                    # 获取Reddit帖子对象
                    submission = await self.async_reddit.submission(id=post.id)
                    await submission.load()
                    
                    # 展开评论（限制数量避免过度加载）
                    await submission.comments.replace_more(limit=5)
                    
                    # 记录该帖子的评论信息
                    post_comment_info = {
                        "post_id": post.id,
                        "post_title": submission.title,
                        "post_subreddit": submission.subreddit.display_name,
                        "comments_analyzed": 0,
                        "top_level_found": 0,
                        "qualified_found": 0
                    }
                    
                    # 分析评论
                    for comment in submission.comments.list()[:50]:  # 增加处理的评论数
                        extraction_stats["total_comments_found"] += 1
                        post_comment_info["comments_analyzed"] += 1
                        
                        # 检查是否为一级评论（直接回复帖子）
                        if hasattr(comment, 'parent_id') and comment.parent_id.startswith('t3_'):
                            extraction_stats["top_level_comments"] += 1
                            post_comment_info["top_level_found"] += 1
                            
                            # 检查是否符合质量标准
                            if (comment.author and 
                                comment.author.name != "[deleted]" and
                                comment.author.name != "AutoModerator" and  # 排除自动回复
                                len(comment.body) >= min_length and 
                                comment.score >= min_score):
                                
                                extraction_stats["qualified_comments"] += 1
                                post_comment_info["qualified_found"] += 1
                                
                                username = comment.author.name
                                
                                # 计算详细的质量指标
                                quality_metrics = {
                                    "reddit_score": comment.score,
                                    "comment_length": len(comment.body),
                                    "length_score": min(len(comment.body) / 200, 1.0),
                                    "score_normalized": max(min(comment.score / 10, 1.0), 0.0),
                                    "engagement_bonus": 10 if comment.score > 20 else 5,
                                    "created_time": datetime.fromtimestamp(getattr(comment, 'created_utc', 0)) if getattr(comment, 'created_utc', 0) else datetime.now(),
                                    "subreddit": submission.subreddit.display_name
                                }
                                
                                # 计算综合质量分数
                                quality_score = (
                                    comment.score * 0.4 +  # Reddit投票分数
                                    len(comment.body) / 50 * 0.3 +  # 评论长度
                                    quality_metrics["engagement_bonus"] * 0.3  # 高分奖励
                                )
                                quality_metrics["total_quality_score"] = quality_score
                                
                                # 评论详细信息
                                comment_detail = {
                                    "comment_id": comment.id,
                                    "text": comment.body,
                                    "score": comment.score,
                                    "created_utc": getattr(comment, 'created_utc', 0),
                                    "subreddit": submission.subreddit.display_name,
                                    "post_title": submission.title,
                                    "post_id": submission.id,
                                    "quality_score": quality_score,
                                    "quality_metrics": quality_metrics,
                                    "permalink": f"https://reddit.com{comment.permalink}"
                                }
                                
                                # 添加到评论者记录
                                if username not in all_commenters:
                                    all_commenters[username] = {
                                        "username": username,
                                        "comments": [],
                                        "total_quality_score": 0,
                                        "comment_count": 0,
                                        "avg_score": 0,
                                        "subreddits": set(),
                                        "first_seen": quality_metrics["created_time"],
                                        "last_seen": quality_metrics["created_time"]
                                    }
                                
                                # 更新评论者信息
                                commenter_info = all_commenters[username]
                                commenter_info["comments"].append(comment_detail)
                                commenter_info["total_quality_score"] += quality_score
                                commenter_info["comment_count"] += 1
                                commenter_info["subreddits"].add(submission.subreddit.display_name)
                                
                                # 更新时间范围
                                comment_time = quality_metrics["created_time"]
                                if comment_time < commenter_info["first_seen"]:
                                    commenter_info["first_seen"] = comment_time
                                if comment_time > commenter_info["last_seen"]:
                                    commenter_info["last_seen"] = comment_time
                    
                    logger.debug(f"帖子 {post.id}: {post_comment_info['qualified_found']} 个合格评论")
                    
                except Exception as post_error:
                    logger.error(f"处理帖子 {post.id} 时出错: {post_error}")
                    extraction_stats["posts_with_errors"] += 1
                    continue
                    
        except Exception as e:
            logger.error(f"提取评论者时出错: {e}")
            return {"error": str(e), "extraction_stats": extraction_stats}
        
        # 计算最终指标并排序
        extraction_stats["unique_commenters"] = len(all_commenters)
        
        for username, info in all_commenters.items():
            # 计算平均分数和多样性分数
            info["avg_score"] = info["total_quality_score"] / info["comment_count"]
            info["subreddit_diversity"] = len(info["subreddits"])
            info["subreddits"] = list(info["subreddits"])  # 转换为列表便于序列化
            
            # 计算选择理由分数
            selection_score = (
                info["total_quality_score"] * 0.4 +  # 总质量分数
                info["comment_count"] * 2.0 +        # 评论数量
                info["subreddit_diversity"] * 1.5    # subreddit多样性
            )
            info["selection_score"] = selection_score
            
            # 生成选择理由
            reasons = []
            if info["comment_count"] >= 3:
                reasons.append(f"活跃度高({info['comment_count']}条优质评论)")
            if info["avg_score"] >= 15:
                reasons.append(f"平均质量分数高({info['avg_score']:.1f})")
            if info["subreddit_diversity"] >= 2:
                reasons.append(f"跨领域活跃({info['subreddit_diversity']}个subreddit)")
            if info["total_quality_score"] >= 50:
                reasons.append(f"总质量分数优秀({info['total_quality_score']:.1f})")
                
            info["selection_reasons"] = reasons if reasons else ["基础质量符合标准"]
        
        # 按选择分数排序
        sorted_commenters = sorted(all_commenters.items(), 
                                 key=lambda x: x[1]["selection_score"], 
                                 reverse=True)
        
        # 选择前N个作为候选
        selected_commenters = []
        for i, (username, info) in enumerate(sorted_commenters[:max_commenters]):
            selected_info = info.copy()
            selected_info["rank"] = i + 1
            selected_info["selection_confidence"] = min(info["selection_score"] / 100, 1.0)
            selected_commenters.append(selected_info)
        
        # 构建详细结果
        detailed_result = {
            "extraction_stats": extraction_stats,
            "all_commenters": dict(all_commenters),
            "selected_commenters": selected_commenters,
            "selection_criteria": {
                "min_score": min_score,
                "min_length": min_length,
                "max_commenters": max_commenters,
                "selection_algorithm": "quality_score_weighted"
            },
            "top_reasons_summary": self._generate_selection_summary(selected_commenters[:10])
        }
        
        logger.info(f"详细提取完成：{len(all_commenters)} 个评论者，选中前 {len(selected_commenters)} 个")
        
        # 显示TOP5的选择理由
        for i, info in enumerate(selected_commenters[:5]):
            logger.info(f"  Top {i+1}: {info['username']} - {'; '.join(info['selection_reasons'])}")
        
        return detailed_result
    
    def _generate_selection_summary(self, top_commenters: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成选择理由汇总"""
        if not top_commenters:
            return {}
            
        total_comments = sum(c["comment_count"] for c in top_commenters)
        avg_quality = sum(c["avg_score"] for c in top_commenters) / len(top_commenters)
        all_subreddits = set()
        for c in top_commenters:
            all_subreddits.update(c["subreddits"])
        
        return {
            "total_selected": len(top_commenters),
            "total_comments_from_selected": total_comments,
            "average_quality_score": round(avg_quality, 2),
            "coverage_subreddits": len(all_subreddits),
            "selection_quality": "high" if avg_quality > 20 else "medium" if avg_quality > 10 else "basic"
        }
    
    async def get_user_comprehensive_history(self, username: str, limit: int = 200, 
                                           filter_top_level_comments: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取用户完整历史内容（用于图谱构建）
        
        Args:
            username: Reddit用户名
            limit: 获取内容数量限制
            filter_top_level_comments: 是否只获取直接回复帖子的评论(parent_id.startswith('t3_'))
            
        Returns:
            包含用户详细信息和内容的字典
        """
        await self._ensure_async_reddit()
        
        try:
            logger.info(f"获取用户 {username} 的完整历史内容")
            logger.info(f"筛选一级评论: {filter_top_level_comments}")
            
            redditor = await self.async_reddit.redditor(username)
            await redditor.load()
            
            # ===== 新增多维过滤：账号年龄 & 总业绩 =====
            # 安全获取账号创建时间，避免属性不存在错误
            created_utc = getattr(redditor, 'created_utc', None)
            if created_utc is not None:
                account_age_days = (datetime.now() - datetime.fromtimestamp(created_utc)).days
            else:
                logger.warning(f"用户 {username} 无法获取创建时间，设为0天")
                account_age_days = 0
                
            total_karma = getattr(redditor, 'total_karma', 0)
            if account_age_days < 30 or total_karma < 200:
                logger.info(
                    f"🚫 跳过用户 {username}: 账号年龄 {account_age_days} 天, 总 Karma {total_karma} (< 阈值)"
                )
                return None  # 直接返回 None 表示不符合要求
            # ===== 过滤结束 =====
            
            # 用户基本信息
            user_info = {
                'username': username,
                'account_created': datetime.fromtimestamp(created_utc) if created_utc else datetime.now(),
                'total_karma': getattr(redditor, 'total_karma', 0),
                'link_karma': getattr(redditor, 'link_karma', 0),
                'comment_karma': getattr(redditor, 'comment_karma', 0)
            }
            
            # 获取详细内容
            posts = []
            comments = []
            filtered_comments_count = 0
            
            # 获取发帖历史
            async for submission in redditor.submissions.new(limit=limit//2):
                if submission.selftext and len(submission.selftext) > 30:
                    posts.append({
                        'id': submission.id,
                        'title': submission.title,
                        'text': submission.selftext,
                        'subreddit': submission.subreddit.display_name,
                        'score': submission.score,
                        'timestamp': datetime.fromtimestamp(submission.created_utc),
                        'num_comments': submission.num_comments,
                        'url': f"https://reddit.com{submission.permalink}"
                    })
            
            # 获取评论历史
            async for comment in redditor.comments.new(limit=limit):
                if len(comment.body) > 20 and not comment.body.startswith('[deleted]'):
                    # 应用筛选逻辑：只要直接回复帖子的评论
                    if filter_top_level_comments:
                        if comment.parent_id.startswith('t3_'):  # t3_表示是帖子的ID
                            comments.append({
                                'id': comment.id,
                                'text': comment.body,
                                'subreddit': comment.subreddit.display_name,
                                'score': comment.score,
                                'timestamp': datetime.fromtimestamp(comment.created_utc),
                                'parent_id': comment.parent_id,
                                'parent_type': 'post',  # 标记为帖子级评论
                                'url': f"https://reddit.com{comment.permalink}"
                            })
                        else:
                            filtered_comments_count += 1
                    else:
                        # 不筛选，获取所有评论
                        parent_type = 'post' if comment.parent_id.startswith('t3_') else 'comment'
                        comments.append({
                            'id': comment.id,
                            'text': comment.body,
                            'subreddit': comment.subreddit.display_name,
                            'score': comment.score,
                            'timestamp': datetime.fromtimestamp(comment.created_utc),
                            'parent_id': comment.parent_id,
                            'parent_type': parent_type,
                            'url': f"https://reddit.com{comment.permalink}"
                        })
            
            # 内容质量分析
            total_content_length = sum(len(p['text']) for p in posts) + sum(len(c['text']) for c in comments)
            avg_score = (sum(p['score'] for p in posts) + sum(c['score'] for c in comments)) / max(len(posts) + len(comments), 1)
            
            user_data = {
                'user_info': user_info,
                'posts': posts,
                'comments': comments,
                'content_stats': {
                    'total_posts': len(posts),
                    'total_comments': len(comments),
                    'filtered_comments_count': filtered_comments_count,
                    'total_content_length': total_content_length,
                    'avg_score': avg_score,
                    'active_subreddits': list(set([p['subreddit'] for p in posts] + [c['subreddit'] for c in comments]))
                }
            }
            
            logger.info(f"用户 {username} 历史获取完成：{len(posts)}个帖子，{len(comments)}个评论")
            if filter_top_level_comments:
                logger.info(f"过滤掉 {filtered_comments_count} 个非一级评论")
            
            return user_data
            
        except Exception as e:
            logger.error(f"获取用户 {username} 完整历史失败: {e}")
            return None
    
    async def analyze_comment_quality(self, comment_data: Dict[str, Any]) -> Dict[str, float]:
        """
        分析评论质量
        
        Args:
            comment_data: 评论数据字典
            
        Returns:
            质量评估指标
        """
        try:
            comment_text = comment_data.get('text', '')
            score = comment_data.get('score', 0)
            
            # 长度分析
            length_score = min(len(comment_text) / 200, 1.0)  # 200字符以上认为充分
            
            # 分数分析
            score_normalized = max(min(score / 10, 1.0), 0.0)  # 10分以上认为优秀
            
            # 内容深度分析（简化版本）
            depth_indicators = ['因为', '但是', '所以', '如果', '虽然', '然而', '不过', '例如', '比如']
            depth_count = sum(1 for indicator in depth_indicators if indicator in comment_text)
            depth_score = min(depth_count / 3, 1.0)  # 3个以上逻辑词认为有深度
            
            # 情感丰富度
            emotion_words = ['感觉', '觉得', '认为', '希望', '担心', '开心', '难过', '激动', '失望']
            emotion_count = sum(1 for word in emotion_words if word in comment_text)
            emotion_score = min(emotion_count / 2, 1.0)
            
            # 综合质量分数
            overall_quality = (
                length_score * 0.25 +
                score_normalized * 0.25 +
                depth_score * 0.25 +
                emotion_score * 0.25
            )
            
            return {
                'length_score': length_score,
                'score_normalized': score_normalized,
                'depth_score': depth_score,
                'emotion_score': emotion_score,
                'overall_quality': overall_quality
            }
            
        except Exception as e:
            logger.error(f"分析评论质量失败: {e}")
            return {'overall_quality': 0.0}
    
    async def search_posts_by_keywords_enhanced(self, keywords: List[str], 
                                               limit: int = 100,
                                               time_filter: str = "all") -> List[Dict[str, Any]]:
        """
        增强的关键词帖子搜索 - 实现组合查询优先+并发+质量排序策略
        
        Args:
            keywords: 关键词列表
            limit: 搜索帖子数量限制
            time_filter: 时间过滤器
            
        Returns:
            增强的帖子数据列表
        """
        await self._ensure_async_reddit()
        
        posts = []
        
        try:
            logger.info(f"开始增强搜索，关键词: {keywords[:5]}")  # 只显示前5个
            
            # 并发控制
            semaphore = asyncio.Semaphore(settings.reddit_search_concurrency)
            all_posts_dict = {}  # 用于去重，key为post_id
            
            async def search_with_semaphore(query: str, subreddit_name: str, search_type: str = "combined") -> List[Dict[str, Any]]:
                """带并发控制的搜索函数"""
                async with semaphore:
                    try:
                        subreddit = await self.async_reddit.subreddit(subreddit_name)
                        found_posts = []
                        
                        search_limit = 20 if search_type == "combined" else 15
                        logger.debug(f"在 r/{subreddit_name} 搜索: {query[:50]}...")
                        
                        async for submission in subreddit.search(
                            query, 
                            limit=search_limit, 
                            time_filter=time_filter, 
                            sort="relevance"
                        ):
                            if submission.selftext and len(submission.selftext) > 100:
                                post_data = {
                                    'id': submission.id,
                                    'title': submission.title,
                                    'text': submission.selftext,
                                    'author': submission.author.name if submission.author else None,
                                    'subreddit': submission.subreddit.display_name,
                                    'score': submission.score,
                                    'upvote_ratio': getattr(submission, 'upvote_ratio', 0.5),
                                    'num_comments': submission.num_comments,
                                    'timestamp': datetime.fromtimestamp(submission.created_utc),
                                    'url': f"https://reddit.com{submission.permalink}",
                                    'matched_keyword': query,
                                    'gilded': getattr(submission, 'gilded', 0),
                                    'is_self': submission.is_self,
                                    'search_type': search_type
                                }
                                found_posts.append(post_data)
                        
                        logger.debug(f"r/{subreddit_name} {search_type}搜索找到 {len(found_posts)} 个帖子")
                        return found_posts
                        
                    except Exception as e:
                        logger.error(f"搜索 r/{subreddit_name} 失败: {e}")
                        return []
            
            # 第一阶段：组合查询（AND语义）
            combined_query = " ".join(keywords[:6])  # 限制长度避免超过Reddit查询限制
            logger.info(f"第一阶段：组合查询 - {combined_query}")
            
            # 并发搜索所有subreddit
            combined_tasks = [
                search_with_semaphore(combined_query, subreddit_name, "combined")
                for subreddit_name in settings.reddit_subreddits
            ]
            combined_results = await asyncio.gather(*combined_tasks)
            
            # 合并组合查询结果
            for subreddit_posts in combined_results:
                for post in subreddit_posts:
                    if post['id'] not in all_posts_dict:
                        all_posts_dict[post['id']] = post
            
            logger.info(f"组合查询找到 {len(all_posts_dict)} 个去重帖子")
            
            # 第二阶段：如果结果不足，用单关键词补量
            if len(all_posts_dict) < limit // 2:
                logger.info("第二阶段：单关键词补量搜索")
                
                # 优先选择重要关键词
                priority_keywords = keywords[:4]  # 取前4个最重要的关键词
                
                single_tasks = [
                    search_with_semaphore(keyword, subreddit_name, "single")
                    for keyword in priority_keywords
                    for subreddit_name in settings.reddit_subreddits[:8]  # 限制subreddit数量
                ]
                single_results = await asyncio.gather(*single_tasks)
                
                # 合并单关键词结果
                for subreddit_posts in single_results:
                    for post in subreddit_posts:
                        if post['id'] not in all_posts_dict:
                            all_posts_dict[post['id']] = post
                
                logger.info(f"补量后共找到 {len(all_posts_dict)} 个去重帖子")
            
            # 转换为列表并计算质量分数
            posts = list(all_posts_dict.values())
            
            # 第三阶段：质量打分和时间衰减
            for post in posts:
                quality_score = self._calculate_enhanced_post_quality(post)
                post['quality_score'] = quality_score
            
            # 按质量分数排序并取前N个
            posts.sort(key=lambda x: x.get('quality_score', 0), reverse=True)
            posts = posts[:limit]
            
            logger.info(f"最终返回 {len(posts)} 个高质量帖子")
            return posts
            
        except Exception as e:
            logger.error(f"增强搜索失败: {e}")
            return []
    
    def _calculate_enhanced_post_quality(self, post_data: Dict[str, Any]) -> float:
        """计算增强的帖子质量分数，包含时间衰减"""
        try:
            # 基础质量分数
            base_quality = self._calculate_post_quality(post_data)
            
            # 时间衰减计算
            post_time = post_data.get('timestamp')
            if post_time:
                years_ago = (datetime.now() - post_time).days / 365.25
                # 时间衰减：超过配置年限的帖子按指数衰减
                if years_ago > settings.reddit_time_decay_years:
                    time_decay = 0.8 ** (years_ago - settings.reddit_time_decay_years)
                else:
                    time_decay = 1.0
            else:
                time_decay = 0.8  # 没有时间信息的帖子降权
            
            # 搜索类型权重
            search_type = post_data.get('search_type', 'single')
            search_weight = 1.2 if search_type == 'combined' else 1.0  # 组合查询结果加权
            
            # 最终质量分数
            final_score = base_quality * time_decay * search_weight
            
            return min(final_score, 1.0)  # 限制最大值为1.0
            
        except Exception:
            return 0.5  # 默认中等质量
    
    def _calculate_post_quality(self, post_data: Dict[str, Any]) -> float:
        """计算帖子质量分数"""
        try:
            # 分数权重
            score = post_data.get('score', 0)
            score_weight = min(score / 100, 1.0)  # 100分以上认为高质量
            
            # 评论数权重
            num_comments = post_data.get('num_comments', 0)
            comment_weight = min(num_comments / 50, 1.0)  # 50个评论以上认为有讨论价值
            
            # 内容长度权重
            text_length = len(post_data.get('text', ''))
            length_weight = min(text_length / 500, 1.0)  # 500字符以上认为内容丰富
            
            # 投票比例权重
            upvote_ratio = post_data.get('upvote_ratio', 0.5)
            ratio_weight = upvote_ratio
            
            # 金牌奖励
            gilded = post_data.get('gilded', 0)
            gilded_weight = min(gilded * 0.2, 0.5)  # 金牌最多贡献50%
            
            # 综合质量分数
            quality_score = (
                score_weight * 0.3 +
                comment_weight * 0.25 +
                length_weight * 0.2 +
                ratio_weight * 0.15 +
                gilded_weight * 0.1
            )
            
            return min(quality_score, 1.0)
            
        except Exception:
            return 0.5  # 默认中等质量
    
    async def get_subreddit_trending_topics(self, subreddit_names: List[str] = None,
                                          limit: int = 20) -> Dict[str, List[str]]:
        """
        获取subreddit的热门话题
        
        Args:
            subreddit_names: subreddit名称列表
            limit: 每个subreddit的热门帖子数量
            
        Returns:
            {subreddit_name: [热门话题关键词]}
        """
        await self._ensure_async_reddit()
        
        if subreddit_names is None:
            subreddit_names = settings.reddit_subreddits
        
        trending_topics = {}
        
        for subreddit_name in subreddit_names:
            try:
                logger.info(f"获取 r/{subreddit_name} 的热门话题")
                
                subreddit = await self.async_reddit.subreddit(subreddit_name)
                
                # 收集热门帖子标题
                titles = []
                async for submission in subreddit.hot(limit=limit):
                    if submission.title and len(submission.title) > 10:
                        titles.append(submission.title.lower())
                
                # 简单的关键词提取
                keywords = []
                for title in titles:
                    words = title.split()
                    keywords.extend([word for word in words if len(word) > 4])
                
                # 统计词频并返回热门词汇
                from collections import Counter
                word_counts = Counter(keywords)
                trending_topics[subreddit_name] = [word for word, count in word_counts.most_common(10)]
                
                logger.info(f"r/{subreddit_name} 热门话题：{trending_topics[subreddit_name][:5]}")
                
            except Exception as e:
                logger.error(f"获取 r/{subreddit_name} 热门话题失败: {e}")
                trending_topics[subreddit_name] = []
        
        return trending_topics

    async def close(self):
        """关闭 Reddit 连接"""
        if self.async_reddit:
            await self.async_reddit.close()
        
    async def test_connection(self) -> bool:
        """测试 Reddit API 连接"""
        try:
            await self._ensure_async_reddit()
            subreddit = await self.async_reddit.subreddit("popular")
            # 获取一条热门帖子作为连接测试
            async for _ in subreddit.hot(limit=1):
                logger.info("Reddit API 连接测试成功")
                return True
            return True  # 即使没有帖子也认为成功
        except Exception as e:
            logger.exception(f"Reddit API 连接测试失败: {e}")
            return False 

    async def rerank_posts_by_embedding(self, posts: List[Dict[str, Any]], 
                                       user_query: str, 
                                       top_k: int = 30) -> List[Dict[str, Any]]:
        """使用Embedding对帖子进行重排序"""
        try:
            if not posts or len(posts) <= top_k:
                return posts
            
            logger.info(f"使用Embedding重排 {len(posts)} 个帖子，取前 {top_k} 个")
            
            # 获取AI服务用于embedding
            from ..services.ai_service import AIService
            ai_service = AIService()
            
            # 生成用户查询的embedding
            user_embedding = await ai_service.get_embeddings([user_query])
            if not user_embedding:
                logger.warning("无法生成用户查询embedding，跳过重排序")
                return posts[:top_k]
            
            user_vec = user_embedding[0]
            
            # 为每个帖子生成embedding并计算相似度
            post_similarities = []
            
            # 批量处理embedding
            post_texts = []
            for post in posts:
                # 组合标题和正文的前512字符作为embedding输入
                combined_text = f"{post['title']} {post['text'][:512]}"
                post_texts.append(combined_text)
            
            # 批量获取embedding
            post_embeddings = await ai_service.get_embeddings(post_texts)
            
            if not post_embeddings or len(post_embeddings) != len(posts):
                logger.warning("帖子embedding生成失败，使用原始排序")
                return posts[:top_k]
            
            # 计算余弦相似度
            import numpy as np
            
            for i, (post, post_vec) in enumerate(zip(posts, post_embeddings)):
                try:
                    # 计算余弦相似度
                    similarity = np.dot(user_vec, post_vec) / (
                        np.linalg.norm(user_vec) * np.linalg.norm(post_vec)
                    )
                    
                    # 结合原始质量分数
                    original_score = post.get('quality_score', 0.5)
                    combined_score = similarity * 0.7 + original_score * 0.3
                    
                    post_similarities.append({
                        'post': post,
                        'similarity': float(similarity),
                        'combined_score': float(combined_score)
                    })
                    
                except Exception as e:
                    logger.debug(f"计算帖子 {i} 相似度失败: {e}")
                    post_similarities.append({
                        'post': post,
                        'similarity': 0.0,
                        'combined_score': post.get('quality_score', 0.5)
                    })
            
            # 按组合分数排序
            post_similarities.sort(key=lambda x: x['combined_score'], reverse=True)
            
            # 返回重排序后的帖子
            reranked_posts = [item['post'] for item in post_similarities[:top_k]]
            
            # 更新帖子的相似度信息
            for i, item in enumerate(post_similarities[:top_k]):
                reranked_posts[i]['embedding_similarity'] = item['similarity']
                reranked_posts[i]['combined_score'] = item['combined_score']
            
            logger.info(f"Embedding重排序完成，返回前 {len(reranked_posts)} 个帖子")
            return reranked_posts
            
        except Exception as e:
            logger.error(f"Embedding重排序失败: {e}")
            return posts[:top_k]
    
    async def llm_final_rank_posts(self, posts: List[Dict[str, Any]], 
                                  user_query: str, 
                                  top_k: int = 10) -> List[Dict[str, Any]]:
        """使用LLM对帖子进行最终排序"""
        try:
            if not posts or len(posts) <= top_k:
                return posts
            
            logger.info(f"使用LLM进行最终排序，处理 {len(posts)} 个帖子，取前 {top_k} 个")
            
            # 获取AI服务
            from ..services.ai_service import AIService
            ai_service = AIService()
            
            # 为每个帖子生成LLM相关性评分 - 并行处理
            async def score_single_post(i: int, post: Dict[str, Any]) -> Dict[str, Any]:
                """为单个帖子进行LLM评分"""
                try:
                    # 构建LLM评分prompt
                    post_summary = f"""
标题: {post['title']}
内容: {post['text'][:512]}...
subreddit: r/{post['subreddit']}
分数: {post['score']} | 评论数: {post['num_comments']}
"""
                    
                    prompt = f"""你是一名专业的内容推荐专家。请根据用户的困扰判断以下Reddit帖子的相关性。

用户困扰："{user_query}"

Reddit帖子：
{post_summary}

请评估这个帖子对解决用户困扰的相关性和有用性，给出0.0到1.0之间的分数。
考虑因素：
1. 主题相关性（是否讨论相同或相关问题）
2. 内容质量（是否有深度、有价值的讨论）
3. 实用性（是否能提供建议或经验分享）

请只返回JSON格式：{{"score": 0.0, "reason": "简短原因"}}
"""
                    
                    # 调用LLM评分
                    response = await ai_service.get_completion(
                        prompt=prompt,
                        temperature=0.1,
                        max_tokens=100
                    )
                    
                    # 解析LLM响应
                    try:
                        import json
                        json_content = ai_service._extract_json_from_response(response)
                        llm_result = json.loads(json_content)
                        llm_score = float(llm_result.get('score', 0.5))
                        llm_reason = llm_result.get('reason', '')
                    except:
                        # 如果解析失败，使用默认分数
                        llm_score = 0.5
                        llm_reason = "解析失败"
                    
                    # 结合多个维度的分数
                    embedding_sim = post.get('embedding_similarity', 0.5)
                    quality_score = post.get('quality_score', 0.5)
                    
                    # 最终综合分数
                    final_score = llm_score * 0.5 + embedding_sim * 0.3 + quality_score * 0.2
                    
                    logger.debug(f"帖子 {i+1} LLM评分: {llm_score:.3f}")
                    
                    return {
                        'post': post,
                        'llm_score': llm_score,
                        'llm_reason': llm_reason,
                        'final_score': final_score
                    }
                    
                except Exception as e:
                    logger.warning(f"LLM评分帖子 {i+1} 失败: {e}")
                    return {
                        'post': post,
                        'llm_score': 0.5,
                        'llm_reason': '评分失败',
                        'final_score': post.get('combined_score', 0.5)
                    }
            
            # 并行处理所有帖子
            logger.info(f"开始并行LLM评分，处理 {len(posts)} 个帖子...")
            tasks = [score_single_post(i, post) for i, post in enumerate(posts)]
            scored_posts = await asyncio.gather(*tasks)
            
            # 按最终分数排序
            scored_posts.sort(key=lambda x: x['final_score'], reverse=True)
            
            # 返回最终排序的帖子
            final_posts = []
            for item in scored_posts[:top_k]:
                post = item['post']
                post['llm_score'] = item['llm_score']
                post['llm_reason'] = item['llm_reason']
                post['final_score'] = item['final_score']
                final_posts.append(post)
            
            logger.info(f"LLM最终排序完成，返回前 {len(final_posts)} 个帖子")
            return final_posts
            
        except Exception as e:
            logger.error(f"LLM最终排序失败: {e}")
            return posts[:top_k] 