#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 get_user_posts.py 的修复
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'brightdata_apitest'))

from get_user_posts import TwitterDataCollector

def test_download_retry_logic():
    """测试下载重试逻辑"""
    print("🧪 测试下载重试逻辑...")
    
    API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
    DATASET_ID = "gd_lwxmeb2u1cniijd7t4"
    
    collector = TwitterDataCollector(API_TOKEN, DATASET_ID)
    
    # 测试快照列表获取
    print("📋 获取快照列表...")
    snapshots = collector.get_snapshots()
    print(f"✅ 获取到快照数量: {len(snapshots) if isinstance(snapshots, list) else len(snapshots.get('snapshots', []))}")
    
    # 如果有快照，测试使用情况查询
    snapshots_list = snapshots if isinstance(snapshots, list) else snapshots.get("snapshots", [])
    if snapshots_list:
        latest_snapshot = snapshots_list[0]
        snapshot_id = latest_snapshot.get('id')
        print(f"📊 测试最新快照 {snapshot_id} 的使用情况...")
        
        usage = collector.get_snapshot_usage(snapshot_id)
        if usage:
            print(f"✅ 使用情况: {usage}")
        else:
            print("❌ 获取使用情况失败")
    
    # 测试账户使用情况
    print("💰 测试账户使用情况...")
    account_usage = collector.get_account_usage()
    if account_usage:
        print(f"✅ 账户使用情况: {account_usage}")
    else:
        print("❌ 获取账户使用情况失败")

if __name__ == "__main__":
    test_download_retry_logic() 