#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 BrightData 的 Twitter 搜索 + 用户画像采集脚本
步骤：
1. 使用关键词在 Twitter 搜索，抓取前 N 条推文（Posts Scraper，按推文计费）。
2. 从结果中提取作者 URL，去重后取前 M 个作者。
3. 对每个作者调用 Profiles Scraper 抓取其历史推文，构建画像。
4. 所有数据统一保存在 brightdata_apitest/data 目录。

用法示例：
    python search_and_collect.py "openai gpt-4" --top_posts 30 --user_posts 200
"""

import argparse
import json
import os
import sys
import time
import urllib.parse
from datetime import datetime as _dt

import requests

# ---------------------------- 尝试不同方案的配置区 ----------------------------
API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"

# 方案1：尝试用 Profiles Scraper 结合搜索 URL（如果支持的话）
PROFILES_DATASET_ID = "gd_lwxmeb2u1cniijd7t4"

# 方案2：尝试用其他可能的数据集 ID
SEARCH_PROFILES_DATASET_ID = "gd_lwxkxvnf1cynvib9co"  # 原 Posts scraper ID
POSTS_DATASET_ID = "gd_lwxkxvnf1cynvib9co"  # Posts Scraper 数据集ID

# 方案3：检查是否有专门的搜索聚合采集器
ALTERNATIVE_DATASET_IDS = [
    "gd_lwxmeb2u1cniijd7t4",  # Profiles
    "gd_lwxkxvnf1cynvib9co",  # Posts  
    "gd_l7q7zkzqt5zw8y1j",    # 可能的其他 ID
    "gd_l7q7zkdz5zw8y1j"     # 再试一个
]

class TwitterSearchOptimizer:
    """
    尝试多种方法来优化 Twitter 搜索的计费方式
    """
    
    def __init__(self, api_token):
        self.api_token = api_token
        self.base_headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }
    
    def method_1_profile_search_hack(self, search_query, max_profiles=30):
        """
        方案1：尝试用 Profile scraper 抓取搜索结果页面的用户
        理论：如果能构造搜索URL，让Profile scraper抓取搜索结果中的用户，
        那就是按用户数计费而不是按推文数
        """
        print("🔍 方案1：尝试用Profile Scraper抓取搜索结果用户...")
        
        # 构造 Twitter 搜索 URL
        import urllib.parse
        encoded_query = urllib.parse.quote_plus(search_query)
        search_url = f"https://x.com/search?q={encoded_query}&src=typed_query&f=user"
        
        # 尝试用 Profile scraper 处理搜索页面
        data = [{
            "url": search_url,
            "max_number_of_posts": 10  # 少量推文，主要关注用户
        }]
        
        return self._trigger_collection(PROFILES_DATASET_ID, data, "Profile搜索")
    
    def method_2_multiple_user_profiles(self, user_list):
        """
        方案2：如果已经有用户列表，批量抓取这些用户
        这样仍然是按用户数计费
        """
        print("🔍 方案2：批量用户Profile抓取...")
        
        data = []
        for username in user_list:
            if not username.startswith("https://"):
                username = f"https://x.com/{username.replace('@', '')}"
            data.append({
                "url": username,
                "max_number_of_posts": 100  # 每用户抓取较多推文
            })
        
        return self._trigger_collection(PROFILES_DATASET_ID, data, "批量用户抓取")
    
    def method_3_test_alternative_datasets(self, search_query):
        """
        方案3：测试其他可能的数据集 ID，看是否有更优计费方式
        """
        print("🔍 方案3：测试其他数据集ID...")
        
        import urllib.parse
        encoded_query = urllib.parse.quote_plus(search_query)
        search_url = f"https://x.com/search?q={encoded_query}&src=typed_query"
        
        results = {}
        for i, dataset_id in enumerate(ALTERNATIVE_DATASET_IDS):
            try:
                print(f"   测试数据集 {i+1}: {dataset_id}")
                
                # 测试不同的数据格式
                data = [{
                    "url": search_url,
                    "max_number_of_posts": 10
                }]
                
                result = self._trigger_collection(dataset_id, data, f"测试{i+1}")
                results[dataset_id] = result
                
            except Exception as e:
                print(f"   ❌ 数据集 {dataset_id} 失败: {e}")
                results[dataset_id] = {"error": str(e)}
        
        return results
    
    def method_4_search_to_users_workflow(self, search_query, max_posts=30):
        """
        方案4：两步法 - 先少量搜索获取用户列表，再批量抓取用户
        第一步用 Posts scraper 获取少量推文和作者
        第二步用 Profiles scraper 批量抓取这些作者
        """
        print("🔍 方案4：两步法搜索...")
        
        # 步骤1：搜索少量推文获取作者
        import urllib.parse
        encoded_query = urllib.parse.quote_plus(search_query)
        search_url = f"https://x.com/search?q={encoded_query}&src=typed_query"
        
        print("   步骤1：搜索前10条推文获取作者列表...")
        search_data = [{"url": search_url}]
        
        # 触发搜索（这里会按推文计费，但只要10条）
        search_result = self._trigger_collection(SEARCH_PROFILES_DATASET_ID, search_data, "少量搜索")
        
        if search_result and "snapshot_id" in search_result:
            # 等待并获取结果
            import time
            print("   等待搜索完成...")
            time.sleep(30)  # 等待搜索完成
            
            # 这里应该解析搜索结果，提取用户列表
            # 然后调用 method_2 批量抓取用户
            print("   步骤2：批量抓取发现的用户...")
            # （实际实现需要解析搜索结果）
            
        return search_result
    
    def _trigger_collection(self, dataset_id, data, method_name):
        """通用的采集触发方法"""
        url = "https://api.brightdata.com/datasets/v3/trigger"
        params = {
            "dataset_id": dataset_id,
            "include_errors": "true",
        }
        
        try:
            response = requests.post(url, headers=self.base_headers, params=params, json=data, timeout=30)
            result = response.json()
            
            if response.status_code == 200:
                snapshot_id = result.get("snapshot_id")
                print(f"   ✅ {method_name} 成功，快照ID: {snapshot_id}")
                return {"success": True, "snapshot_id": snapshot_id, "method": method_name}
            else:
                print(f"   ❌ {method_name} 失败，状态码: {response.status_code}")
                print(f"   响应: {result}")
                return {"success": False, "error": result, "method": method_name}
                
        except Exception as e:
            print(f"   ❌ {method_name} 异常: {e}")
            return {"success": False, "error": str(e), "method": method_name}

def test_all_methods(search_query="openai gpt"):
    """
    测试所有方法，找出最优的计费方案
    """
    print(f"🧪 开始测试所有方法，搜索关键词: '{search_query}'")
    print("=" * 60)
    
    optimizer = TwitterSearchOptimizer(API_TOKEN)
    results = {}
    
    # 方案1：Profile搜索hack
    try:
        results["method_1"] = optimizer.method_1_profile_search_hack(search_query)
    except Exception as e:
        results["method_1"] = {"error": str(e)}
    
    print()
    
    # 方案2：批量用户（需要预先知道用户列表）
    test_users = ["elonmusk", "BillGates", "cnn", "openai"]
    try:
        results["method_2"] = optimizer.method_2_multiple_user_profiles(test_users[:2])  # 只测试2个用户
    except Exception as e:
        results["method_2"] = {"error": str(e)}
    
    print()
    
    # 方案3：测试其他数据集ID
    try:
        results["method_3"] = optimizer.method_3_test_alternative_datasets(search_query)
    except Exception as e:
        results["method_3"] = {"error": str(e)}
    
    print()
    
    # 方案4：两步法
    try:
        results["method_4"] = optimizer.method_4_search_to_users_workflow(search_query)
    except Exception as e:
        results["method_4"] = {"error": str(e)}
    
    print()
    print("=" * 60)
    print("🏆 测试结果总结:")
    for method, result in results.items():
        if isinstance(result, dict) and result.get("success"):
            print(f"✅ {method}: 成功 - {result.get('method', '')}")
        else:
            print(f"❌ {method}: 失败")
    
    return results

# ---------------------------- 配置区 ----------------------------
SAVE_DIR = "brightdata_apitest/data"

# ------------------------ 工具函数 ------------------------------

def ensure_dir(path: str):
    os.makedirs(path, exist_ok=True)


def trigger_dataset(dataset_id: str, payload: list):
    url = "https://api.brightdata.com/datasets/v3/trigger"
    headers = {"Authorization": f"Bearer {API_TOKEN}", "Content-Type": "application/json"}
    params = {"dataset_id": dataset_id, "include_errors": "true"}
    resp = requests.post(url, headers=headers, params=params, json=payload, timeout=30)
    resp.raise_for_status()
    return resp.json()


def wait_until_ready(snapshot_id: str, timeout_sec: int = 600):
    url = f"https://api.brightdata.com/datasets/v3/progress/{snapshot_id}"
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    start = time.time()
    while True:
        r = requests.get(url, headers=headers, timeout=30).json()
        status = r.get("status")
        if status == "ready":
            return True
        if status == "failed":
            print(f"❌ Snapshot {snapshot_id} failed: {r}")
            return False
        if time.time() - start > timeout_sec:
            print("⏰ Timeout waiting snapshot ready")
            return False
        time.sleep(10)


def download_snapshot_json(snapshot_id: str):
    """下载 snapshot JSON 内容并返回 Python 对象"""
    url = f"https://api.brightdata.com/datasets/v3/snapshot/{snapshot_id}"
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    params = {"format": "json"}
    r = requests.get(url, headers=headers, params=params, timeout=60)
    r.raise_for_status()
    return r.json()

# ------------------------ 主流程 ------------------------------

from brightdata_apitest.get_user_posts import TwitterDataCollector  # 复用已实现的类


def run_search_and_collect(query: str, top_posts: int, user_posts: int):
    ensure_dir(SAVE_DIR)

    # 1. 构造搜索 URL
    encoded = urllib.parse.quote_plus(query)
    search_url = f"https://twitter.com/search?q={encoded}&src=typed_query&f=live"
    payload = [{"url": search_url, "max_number_of_posts": top_posts}]
    print(f"🚀 触发搜索任务：{query}，URL={search_url}")
    trigger_res = trigger_dataset(POSTS_DATASET_ID, payload)
    snapshot_id = trigger_res.get("snapshot_id")
    if not snapshot_id:
        print("❌ 触发失败", trigger_res)
        return
    print(f"✅ 搜索 Snapshot ID: {snapshot_id}")
    if not wait_until_ready(snapshot_id):
        return

    posts_data = download_snapshot_json(snapshot_id)
    ts = _dt.now().strftime("%Y%m%d_%H%M%S")
    raw_file = os.path.join(SAVE_DIR, f"search_{ts}.json")
    with open(raw_file, "w", encoding="utf-8") as f:
        json.dump(posts_data, f, ensure_ascii=False, indent=2)
    print(f"🔖 已保存搜索原始结果到 {raw_file}")

    # 2. 提取作者 URL
    user_urls = []
    for p in posts_data:
        url = p.get("user_posted") or p.get("url")  # Posts Scraper返回字段名可能为 user_posted 或 url
        if url and url.startswith("https://x.com/"):
            user_urls.append(url.split("/status")[0])  # 去掉 /status/.. 部分
    unique_users = []
    seen = set()
    for u in user_urls:
        if u not in seen:
            unique_users.append(u)
            seen.add(u)
    print(f"📊 共提取到 {len(unique_users)} 个作者，取前 {len(unique_users)} 个进行画像")

    # 3. 逐个用户抓取历史推文
    collector = TwitterDataCollector(API_TOKEN, PROFILES_DATASET_ID)
    results = []
    for idx, u in enumerate(unique_users, 1):
        username = u.replace("https://x.com/", "")
        print(f"\n[{idx}/{len(unique_users)}] 抓取 {username} 的历史推文 …")
        res = collector.collect_user_posts(username, max_posts=user_posts, wait_for_completion=True)
        if res:
            results.append(res)

    # 4. 汇总保存
    summary_path = os.path.join(SAVE_DIR, f"profiles_{ts}.json")
    with open(summary_path, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"✅ 所有用户画像已保存到 {summary_path}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Twitter 搜索 -> 用户画像 一条龙脚本")
    parser.add_argument("query", help="搜索关键词，支持 Twitter 高级搜索语法")
    parser.add_argument("--top_posts", type=int, default=30, help="抓取搜索结果前多少推文")
    parser.add_argument("--user_posts", type=int, default=320, help="每个用户抓取多少历史推文 (<=3200)")
    args = parser.parse_args()

    run_search_and_collect(args.query, args.top_posts, args.user_posts) 