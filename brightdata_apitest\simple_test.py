import requests
import json

def test_brightdata_api():
    """
    测试BrightData API连接和基本功能
    """
    # API配置
    API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
    DATASET_ID = "gd_lwxmeb2u1cniijd7t4"
    
    print("=== BrightData API 测试 ===")
    print(f"Dataset ID: {DATASET_ID}")
    print()
    
    # 1. 测试获取已完成的快照
    print("1. 获取已完成的快照...")
    try:
        url = "https://api.brightdata.com/datasets/v3/snapshots"
        headers = {
            "Authorization": f"Bearer {API_TOKEN}",
        }
        params = {
            "dataset_id": DATASET_ID,
            "status": "ready",
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"成功获取快照数据:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if "snapshots" in result and result["snapshots"]:
                print(f"\n找到 {len(result['snapshots'])} 个已完成的快照")
                for i, snapshot in enumerate(result["snapshots"][:3]):  # 只显示前3个
                    print(f"快照 {i+1}: {snapshot.get('snapshot_id', 'N/A')}")
            else:
                print("没有找到已完成的快照")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"获取快照时出错: {e}")
    
    print()
    
    # 2. 测试触发新的数据收集（可选）
    print("2. 测试触发数据收集...")
    try:
        trigger_url = "https://api.brightdata.com/datasets/v3/trigger"
        headers = {
            "Authorization": f"Bearer {API_TOKEN}",
            "Content-Type": "application/json",
        }
        params = {
            "dataset_id": DATASET_ID,
            "include_errors": "true",
        }
        
        # 测试数据 - 获取一个用户的少量帖子
        test_data = [
            {"url": "https://x.com/elonmusk", "max_number_of_posts": 5}
        ]
        
        print(f"发送请求到: {trigger_url}")
        print(f"请求数据: {test_data}")
        
        response = requests.post(trigger_url, headers=headers, params=params, json=test_data, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("成功触发数据收集:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if "snapshot_id" in result:
                snapshot_id = result["snapshot_id"]
                print(f"\n✅ 数据收集已触发，快照ID: {snapshot_id}")
                print("您可以稍后使用这个ID检查进度")
            else:
                print("⚠️ 未获取到快照ID")
        else:
            print(f"触发失败: {response.text}")
            
    except Exception as e:
        print(f"触发数据收集时出错: {e}")

def show_usage_examples():
    """
    显示使用示例
    """
    print("\n=== 使用示例 ===")
    print("1. 获取指定用户的帖子:")
    print("   data = [{'url': 'https://x.com/用户名', 'max_number_of_posts': 数量}]")
    print()
    print("2. 批量获取多个用户:")
    print("   data = [")
    print("       {'url': 'https://x.com/elonmusk', 'max_number_of_posts': 100},")
    print("       {'url': 'https://x.com/BillGates', 'max_number_of_posts': 50},")
    print("   ]")
    print()
    print("3. 工作流程:")
    print("   - 步骤1: 使用 trigger API 发起数据收集")
    print("   - 步骤2: 使用 progress API 检查收集进度")
    print("   - 步骤3: 使用 snapshots API 获取完成的数据")

if __name__ == "__main__":
    test_brightdata_api()
    show_usage_examples() 