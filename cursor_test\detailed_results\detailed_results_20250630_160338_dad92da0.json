{"session_info": {"session_id": "detailed_results_20250630_160338_dad92da0", "start_time": "2025-06-30T16:03:38.833718", "end_time": "2025-06-30T16:03:38.834957", "status": "completed", "user_prompt": "最小化Pipeline测试", "total_execution_time": 0.001239, "version": "2.0"}, "subtask_results": {"子任务D: Redditor语料提取与图谱构建_2025-06-30T16:03:38.833718": {"task_name": "子任务D: Redditor语料提取与图谱构建", "timestamp": "2025-06-30T16:03:38.833718", "execution_time_seconds": 2.5, "status": "success", "data": {"candidate_graphs": [{"username": "test_user_1", "success": true, "graph_serialized": "mock_serialized_graph", "user_data": {"posts_count": 5, "comments_count": 15, "total_content": "test content"}, "build_time": 1.2}, {"username": "test_user_2", "success": true, "graph_serialized": "mock_serialized_graph", "user_data": {"posts_count": 3, "comments_count": 8}, "build_time": 0.8}], "input_users_count": 2, "successful_graphs": 2, "failed_users": 0, "construction_summary": {"total_users_attempted": 2, "successful_constructions": 2, "success_rate": 1.0, "avg_construction_time": 1.0, "avg_nodes_per_graph": 0.0, "avg_edges_per_graph": 0.0}}, "metrics": {"data_size": 643, "data_type": "dict", "timestamp": "2025-06-30T16:03:38.833718"}, "error_info": null}}, "performance_metrics": {"subtask_timings": {"子任务D: Redditor语料提取与图谱构建": 2.5}}, "error_logs": [], "final_results": {"success": true, "test_type": "minimal_pipeline_test", "issues_tested": ["UserGraph对象访问", "candidate_graphs数据结构处理", "统计代码执行", "任务D结果保存", "详细结果管理器功能"], "all_tests_passed": true}}