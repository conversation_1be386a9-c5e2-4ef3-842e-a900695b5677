#!/usr/bin/env python3
"""调试URL解析器"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'only_profile'))

try:
    from url_parser import RedditUrlParser, RedditLinkType
    print("成功导入URL解析器")
    
    parser = RedditUrlParser()
    
    # 测试失败的URL
    test_urls = [
        "https://reddit.com/r/Python/comments/abc123/title/xyz789",
        "https://www.reddit.com/r/Python/comments/abc123/post_title/comment_id_123"
    ]
    
    for url in test_urls:
        print(f"\n测试URL: {url}")
        result = parser.parse_url(url)
        print(f"  类型: {result.link_type}")
        print(f"  有效: {result.is_valid}")
        print(f"  错误: {result.error_message}")
        print(f"  子版块: {result.subreddit}")
        print(f"  帖子ID: {result.post_id}")
        print(f"  评论ID: {result.comment_id}")
        
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc() 