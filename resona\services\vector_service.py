"""
向量数据库服务 - FAISS 集成
"""
import json
import logging
import pickle
from pathlib import Path
from typing import List, Tuple, Dict, Optional
import numpy as np
import faiss
from datetime import datetime
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.config import settings
from models import RedditUser

logger = logging.getLogger(__name__)

class VectorService:
    """向量数据库服务类 - 管理 FAISS 索引"""
    
    def __init__(self):
        """初始化向量服务"""
        self.dimension = settings.embedding_dimension
        self.index_path = settings.faiss_index_path
        self.metadata_path = self.index_path.with_suffix('.meta')
        
        # 创建或加载索引
        self.index = None
        self.user_metadata: Dict[int, RedditUser] = {}  # idx -> user mapping
        self.username_to_idx: Dict[str, int] = {}  # username -> idx mapping
        
        self._load_or_create_index()
    
    def _load_or_create_index(self):
        """加载或创建 FAISS 索引"""
        if self.index_path.exists() and self.metadata_path.exists():
            try:
                # 加载现有索引
                self.index = faiss.read_index(str(self.index_path))
                
                # 加载元数据
                with open(self.metadata_path, 'rb') as f:
                    data = pickle.load(f)
                    self.user_metadata = data['user_metadata']
                    self.username_to_idx = data['username_to_idx']
                
                logger.info(f"加载了包含 {self.index.ntotal} 个用户的索引")
            except Exception as e:
                logger.error(f"加载索引失败: {e}")
                self._create_new_index()
        else:
            self._create_new_index()
    
    def _create_new_index(self):
        """创建新的 FAISS 索引"""
        # 使用 L2 距离的索引，后续会转换为余弦相似度
        self.index = faiss.IndexFlatL2(self.dimension)
        self.user_metadata = {}
        self.username_to_idx = {}
        logger.info("创建了新的 FAISS 索引")
    
    def add_user(self, user: RedditUser, embedding: List[float]):
        """
        添加用户到索引
        
        Args:
            user: Reddit 用户对象
            embedding: 用户的 embedding 向量
        """
        # 检查是否已存在
        if user.username in self.username_to_idx:
            # 更新现有用户
            idx = self.username_to_idx[user.username]
            self.user_metadata[idx] = user
            # FAISS 不支持直接更新，需要重建索引
            logger.warning(f"用户 {user.username} 已存在，跳过更新")
            return
        
        # 归一化向量（用于余弦相似度）
        embedding_array = np.array([embedding], dtype='float32')
        faiss.normalize_L2(embedding_array)
        
        # 添加到索引
        idx = self.index.ntotal
        self.index.add(embedding_array)
        
        # 保存元数据
        self.user_metadata[idx] = user
        self.username_to_idx[user.username] = idx
        
        logger.debug(f"添加用户 {user.username} 到索引，当前总数: {self.index.ntotal}")
    
    def batch_add_users(self, users_with_embeddings: List[Tuple[RedditUser, List[float]]]):
        """
        批量添加用户到索引
        
        Args:
            users_with_embeddings: (用户, embedding) 元组列表
        """
        new_users = []
        new_embeddings = []
        
        for user, embedding in users_with_embeddings:
            if user.username not in self.username_to_idx:
                new_users.append(user)
                new_embeddings.append(embedding)
        
        if new_embeddings:
            # 归一化向量
            embeddings_array = np.array(new_embeddings, dtype='float32')
            faiss.normalize_L2(embeddings_array)
            
            # 批量添加到索引
            start_idx = self.index.ntotal
            self.index.add(embeddings_array)
            
            # 更新元数据
            for i, user in enumerate(new_users):
                idx = start_idx + i
                self.user_metadata[idx] = user
                self.username_to_idx[user.username] = idx
            
            logger.info(f"批量添加了 {len(new_users)} 个用户到索引")
    
    def search(self, query_embedding: List[float], k: int = 10) -> List[Tuple[RedditUser, float]]:
        """
        搜索最相似的用户
        
        Args:
            query_embedding: 查询向量
            k: 返回的用户数
            
        Returns:
            (用户, 相似度分数) 元组列表
        """
        if self.index.ntotal == 0:
            logger.warning("索引为空，无法搜索")
            return []
        
        # 归一化查询向量
        query_array = np.array([query_embedding], dtype='float32')
        faiss.normalize_L2(query_array)
        
        # 搜索
        k = min(k, self.index.ntotal)
        distances, indices = self.index.search(query_array, k)
        
        # 转换为余弦相似度（1 - normalized_L2_distance/2）
        similarities = 1 - distances[0] / 2
        
        # 构建结果
        results = []
        for idx, similarity in zip(indices[0], similarities):
            if idx >= 0 and idx in self.user_metadata:
                user = self.user_metadata[idx]
                results.append((user, float(similarity)))
        
        return results
    
    def save_index(self):
        """保存索引到磁盘"""
        try:
            # 确保目录存在
            self.index_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存 FAISS 索引
            faiss.write_index(self.index, str(self.index_path))
            
            # 保存元数据
            with open(self.metadata_path, 'wb') as f:
                pickle.dump({
                    'user_metadata': self.user_metadata,
                    'username_to_idx': self.username_to_idx,
                    'saved_at': datetime.utcnow().isoformat()
                }, f)
            
            logger.info(f"索引已保存，包含 {self.index.ntotal} 个用户")
        except Exception as e:
            logger.error(f"保存索引失败: {e}")
            raise
    
    def get_user_by_username(self, username: str) -> Optional[RedditUser]:
        """
        根据用户名获取用户对象
        
        Args:
            username: Reddit 用户名
            
        Returns:
            用户对象或 None
        """
        idx = self.username_to_idx.get(username)
        if idx is not None:
            return self.user_metadata.get(idx)
        return None
    
    def remove_user(self, username: str):
        """
        从索引中移除用户（需要重建索引）
        
        Args:
            username: Reddit 用户名
        """
        if username in self.username_to_idx:
            # FAISS 不支持删除，需要重建索引
            logger.warning(f"删除用户 {username} 需要重建索引，暂不支持")
    
    def get_stats(self) -> Dict[str, any]:
        """
        获取索引统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'total_users': self.index.ntotal if self.index else 0,
            'index_dimension': self.dimension,
            'index_path': str(self.index_path),
            'index_exists': self.index_path.exists()
        }
    
    def clear_index(self):
        """清空索引"""
        self._create_new_index()
        logger.info("索引已清空") 