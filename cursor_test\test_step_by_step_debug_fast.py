"""
步进调试测试脚本 - 快速版
专门为快速测试设计，大幅减少数据量以提高运行速度
适合开发和调试时使用
"""
import asyncio
import sys
import os
import logging
import json
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 配置日志输出
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout)  # 输出到控制台
    ]
)

from resona.pipeline import RedditResonancePipeline
from resona.config import settings

# ============================================================================
# 快速模式配置类
# ============================================================================

class FastModeConfig:
    """快速模式配置 - 大幅减少数据量以提高运行速度"""
    
    # Reddit搜索配置（大幅减少）
    FAST_REDDIT_SEARCH_LIMIT = 10          # 原始：50，减少80%
    FAST_REDDIT_HISTORY_LIMIT = 20         # 原始：100，减少80%
    FAST_POST_SEARCH_LIMIT = 20             # 原始：100，减少80%
    FAST_MAX_QUALITY_COMMENTERS = 5        # 原始：30，减少83%
    
    # 推荐和匹配配置（减少）
    FAST_TOP_K_MATCHES = 2                 # 原始：5，减少60%
    FAST_MAX_RECOMMENDATIONS = 2           # 原始：3，减少33%
    
    # Embedding和重排配置（减少）
    FAST_EMBEDDING_RERANK_COUNT = 10       # 原始：30，减少67%
    FAST_LLM_FINAL_RANK_COUNT = 5          # 原始：10，减少50%
    
    # 并发控制（减少）
    FAST_MAX_CONCURRENT_EMBEDDINGS = 2     # 原始：5，减少60%
    FAST_EMBEDDING_BATCH_SIZE = 5          # 原始：10，减少50%
    
    @classmethod
    def apply_fast_config(cls):
        """应用快速模式配置到全局settings"""
        print("⚡ 应用快速模式配置...")
        
        # 保存原始配置（用于恢复）
        cls.original_config = {
            'reddit_search_limit': settings.reddit_search_limit,
            'reddit_history_limit': settings.reddit_history_limit,
            'post_search_limit': settings.post_search_limit,
            'max_quality_commenters': settings.max_quality_commenters,
            'top_k_matches': settings.top_k_matches,
            'embedding_rerank_count': settings.embedding_rerank_count,
            'llm_final_rank_count': settings.llm_final_rank_count,
            'max_concurrent_embeddings': settings.max_concurrent_embeddings,
            'embedding_batch_size': settings.embedding_batch_size,
        }
        
        # 应用快速配置
        settings.reddit_search_limit = cls.FAST_REDDIT_SEARCH_LIMIT
        settings.reddit_history_limit = cls.FAST_REDDIT_HISTORY_LIMIT
        settings.post_search_limit = cls.FAST_POST_SEARCH_LIMIT
        settings.max_quality_commenters = cls.FAST_MAX_QUALITY_COMMENTERS
        settings.top_k_matches = cls.FAST_TOP_K_MATCHES
        settings.embedding_rerank_count = cls.FAST_EMBEDDING_RERANK_COUNT
        settings.llm_final_rank_count = cls.FAST_LLM_FINAL_RANK_COUNT
        settings.max_concurrent_embeddings = cls.FAST_MAX_CONCURRENT_EMBEDDINGS
        settings.embedding_batch_size = cls.FAST_EMBEDDING_BATCH_SIZE
        
        print("📊 快速模式配置已应用:")
        print(f"   - Reddit搜索帖子: {cls.FAST_POST_SEARCH_LIMIT} (原始: {cls.original_config['post_search_limit']})")
        print(f"   - 每用户历史数据: {cls.FAST_REDDIT_HISTORY_LIMIT} (原始: {cls.original_config['reddit_history_limit']})")
        print(f"   - 最大候选用户: {cls.FAST_MAX_QUALITY_COMMENTERS} (原始: {cls.original_config['max_quality_commenters']})")
        print(f"   - 推荐数量: {cls.FAST_MAX_RECOMMENDATIONS} (原始: 3)")
        print(f"   - 并发embedding: {cls.FAST_MAX_CONCURRENT_EMBEDDINGS} (原始: {cls.original_config['max_concurrent_embeddings']})")
        print()
    
    @classmethod
    def restore_original_config(cls):
        """恢复原始配置"""
        if hasattr(cls, 'original_config'):
            print("🔄 恢复原始配置...")
            settings.reddit_search_limit = cls.original_config['reddit_search_limit']
            settings.reddit_history_limit = cls.original_config['reddit_history_limit']
            settings.post_search_limit = cls.original_config['post_search_limit']
            settings.max_quality_commenters = cls.original_config['max_quality_commenters']
            settings.top_k_matches = cls.original_config['top_k_matches']
            settings.embedding_rerank_count = cls.original_config['embedding_rerank_count']
            settings.llm_final_rank_count = cls.original_config['llm_final_rank_count']
            settings.max_concurrent_embeddings = cls.original_config['max_concurrent_embeddings']
            settings.embedding_batch_size = cls.original_config['embedding_batch_size']

# ============================================================================
# 简化的结果捕获器
# ============================================================================

class SimplifiedResultsCapture:
    """简化版结果捕获器 - 只记录关键信息以减少开销"""
    
    def __init__(self):
        self.results = {
            "session_info": {},
            "execution_summary": {},
            "final_stats": {}
        }
        self.start_time = datetime.now()
        
    def set_session_info(self, user_prompt: str, mode: str = "fast"):
        """设置会话基本信息"""
        self.results["session_info"] = {
            "user_prompt": user_prompt,
            "start_time": self.start_time.isoformat(),
            "mode": mode
        }
    
    def capture_execution_summary(self, result: Dict[str, Any]):
        """捕获执行摘要"""
        end_time = datetime.now()
        total_time = (end_time - self.start_time).total_seconds()
        
        self.results["execution_summary"] = {
            "success": result.get('success', False),
            "total_execution_time": total_time,
            "processing_time": result.get('processing_time', 0),
            "end_time": end_time.isoformat(),
            "recommendations_count": len(result.get('recommendations', [])),
            "error": result.get('error', None)
        }
        
        # 记录统计信息
        stats = result.get('stats', {})
        self.results["final_stats"] = {
            "relevant_posts_found": stats.get('relevant_posts_found', 0),
            "candidate_users_found": stats.get('candidate_users_found', 0),
            "successful_graphs": stats.get('successful_graphs', 0),
            "fast_mode_enabled": True
        }
    
    def save_to_file(self) -> str:
        """保存简化结果到JSON文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 确保目录存在
        results_dir = os.path.join(os.path.dirname(__file__), "detailed_results")
        os.makedirs(results_dir, exist_ok=True)
        
        filename = os.path.join(results_dir, f"fast_mode_results_{timestamp}.json")
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            
            print(f"💾 快速模式结果已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
            return ""

# ============================================================================
# 主要功能函数
# ============================================================================

def display_fast_menu():
    """显示快速模式菜单"""
    print("⚡ Reddit共鸣推荐系统 - 快速调试模式")
    print("=" * 60)
    print("🚀 专为快速测试设计，大幅减少数据量以提高运行速度")
    print("📊 数据量对比:")
    print("   - 搜索帖子: 20 (原始: 100)")
    print("   - 候选用户: 5 (原始: 30)")  
    print("   - 用户历史: 20 (原始: 100)")
    print("   - 推荐数量: 2 (原始: 3)")
    print()
    print("请选择执行模式：")
    print("1. 快速完整流程（包含缓存，约1-2分钟）")
    print("2. 查看已有缓存")
    print("3. 使用缓存快速跳转到子任务D（约30秒）")
    print("4. 极速模式测试（进一步减少数据量，约30秒）")
    print("5. 查看快速模式结果")
    print("6. 清理所有缓存")
    print("7. 退出")
    print("-" * 60)

async def run_fast_full_pipeline(test_prompt: str):
    """运行快速完整流水线"""
    print(f"📝 测试输入: {test_prompt}")
    print()
    
    # 应用快速模式配置
    FastModeConfig.apply_fast_config()
    
    # 初始化简化结果捕获器
    results_capture = SimplifiedResultsCapture()
    results_capture.set_session_info(test_prompt, "fast_full")
    
    try:
        print("🚀 初始化流水线...")
        pipeline = RedditResonancePipeline()
        print("✅ 流水线初始化完成")
        print()
        
        print("⚡ 开始执行快速完整流水线...")
        print("-" * 40)
        
        # 执行流水线
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            additional_contents=None,
            max_recommendations=FastModeConfig.FAST_MAX_RECOMMENDATIONS,
            use_cache=True
        )
        
        # 捕获结果
        results_capture.capture_execution_summary(result)
        results_capture.save_to_file()
        
        # 显示结果摘要
        print()
        print("=" * 50)
        print("📊 快速模式执行结果:")
        
        if result.get("success"):
            print("✅ 流水线执行成功")
            print(f"⏱️  总耗时: {result.get('processing_time', 0):.2f}秒")
            print(f"📈 推荐数量: {len(result.get('recommendations', []))}")
            
            stats = result.get('stats', {})
            print(f"📋 找到相关帖子: {stats.get('relevant_posts_found', 0)}")
            print(f"👥 候选用户数: {stats.get('candidate_users_found', 0)}")
            print(f"🔗 成功构建图谱: {stats.get('successful_graphs', 0)}")
            
            # 显示推荐用户
            recommendations = result.get('recommendations', [])
            if recommendations:
                print("\n🎯 推荐用户:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"  {i}. {rec.get('candidate_id', 'unknown')} (分数: {rec.get('resonance_score', 0):.3f})")
            
            # 显示缓存信息
            cache_key = result.get('pipeline_results', {}).get('cache_key')
            if cache_key:
                print(f"\n💾 ABC结果已缓存，键: {cache_key[:12]}...")
        else:
            print("❌ 流水线执行失败")
            print(f"错误: {result.get('error', '未知错误')}")
        
        await pipeline.close()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
    finally:
        # 恢复原始配置
        FastModeConfig.restore_original_config()

async def run_ultra_fast_mode(test_prompt: str):
    """运行极速模式测试 - 进一步减少数据量"""
    print(f"📝 测试输入: {test_prompt}")
    print()
    
    # 极速模式配置（比快速模式更激进）
    print("🚀 应用极速模式配置...")
    
    # 保存原始配置
    original_config = {
        'reddit_search_limit': settings.reddit_search_limit,
        'reddit_history_limit': settings.reddit_history_limit,
        'post_search_limit': settings.post_search_limit,
        'max_quality_commenters': settings.max_quality_commenters,
        'top_k_matches': settings.top_k_matches,
    }
    
    # 极速配置
    settings.reddit_search_limit = 5
    settings.reddit_history_limit = 10
    settings.post_search_limit = 10
    settings.max_quality_commenters = 3
    settings.top_k_matches = 1
    
    print("📊 极速模式配置:")
    print("   - 搜索帖子: 10")
    print("   - 候选用户: 3")
    print("   - 用户历史: 10")
    print("   - 推荐数量: 1")
    print()
    
    results_capture = SimplifiedResultsCapture()
    results_capture.set_session_info(test_prompt, "ultra_fast")
    
    try:
        print("🚀 初始化流水线...")
        pipeline = RedditResonancePipeline()
        print("✅ 流水线初始化完成")
        
        print("⚡ 开始执行极速模式...")
        print("-" * 40)
        
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            additional_contents=None,
            max_recommendations=1,
            use_cache=True
        )
        
        results_capture.capture_execution_summary(result)
        results_capture.save_to_file()
        
        print()
        print("=" * 50)
        print("🏃‍♂️ 极速模式执行结果:")
        
        if result.get("success"):
            print("✅ 极速测试成功")
            print(f"⏱️  总耗时: {result.get('processing_time', 0):.2f}秒")
            
            stats = result.get('stats', {})
            print(f"📋 帖子: {stats.get('relevant_posts_found', 0)}")
            print(f"👥 用户: {stats.get('candidate_users_found', 0)}")
            print(f"🔗 图谱: {stats.get('successful_graphs', 0)}")
            
            recommendations = result.get('recommendations', [])
            if recommendations:
                rec = recommendations[0]
                print(f"🎯 推荐: {rec.get('candidate_id', 'unknown')} (分数: {rec.get('resonance_score', 0):.3f})")
        else:
            print("❌ 极速测试失败")
            print(f"错误: {result.get('error', '未知错误')}")
        
        await pipeline.close()
        
    except Exception as e:
        print(f"❌ 极速模式出错: {e}")
    finally:
        # 恢复原始配置
        for key, value in original_config.items():
            setattr(settings, key, value)

async def show_cache_list_fast(pipeline):
    """显示缓存列表（简化版）"""
    print("\n💾 查看已有缓存...")
    try:
        cached_sessions = await pipeline.list_cached_sessions()
        if not cached_sessions:
            print("❌ 没有找到已缓存的ABC结果")
            return None
        
        print(f"📋 找到 {len(cached_sessions)} 个缓存:")
        for i, session in enumerate(cached_sessions, 1):
            cache_key = session.get('cache_key', 'unknown')
            user_prompt = session.get('user_prompt', 'N/A')[:30] + "..." if len(session.get('user_prompt', '')) > 30 else session.get('user_prompt', 'N/A')
            print(f"  {i}. {cache_key[:12]}... - {user_prompt}")
        
        return cached_sessions
    except Exception as e:
        print(f"❌ 获取缓存失败: {e}")
        return None

async def run_fast_from_cache(cache_key: str, test_prompt: str):
    """使用缓存快速跳转（快速版）"""
    print(f"🚀 使用缓存 {cache_key[:12]}... 快速跳转...")
    print(f"📝 查询: {test_prompt}")
    
    FastModeConfig.apply_fast_config()
    
    try:
        pipeline = RedditResonancePipeline()
        
        result = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            cache_key=cache_key,
            additional_contents=None,
            max_recommendations=FastModeConfig.FAST_MAX_RECOMMENDATIONS,
            use_cache=True
        )
        
        print(f"⏱️  耗时: {result.get('processing_time', 0):.2f}秒")
        print(f"📈 推荐: {len(result.get('recommendations', []))}")
        
        await pipeline.close()
        
    except Exception as e:
        print(f"❌ 缓存跳转失败: {e}")
    finally:
        FastModeConfig.restore_original_config()

def view_fast_results():
    """查看快速模式结果"""
    results_dir = os.path.join(os.path.dirname(__file__), "detailed_results")
    if not os.path.exists(results_dir):
        print("❌ 结果目录不存在")
        return
    
    # 查找快速模式结果文件
    fast_files = [f for f in os.listdir(results_dir) if f.startswith('fast_mode_results_')]
    
    if not fast_files:
        print("❌ 没有找到快速模式结果文件")
        return
    
    # 按时间排序，显示最新的几个
    fast_files.sort(reverse=True)
    
    print(f"📁 找到 {len(fast_files)} 个快速模式结果:")
    for i, filename in enumerate(fast_files[:5], 1):
        print(f"  {i}. {filename}")
    
    # 显示最新结果的摘要
    if fast_files:
        latest_file = os.path.join(results_dir, fast_files[0])
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            session_info = data.get('session_info', {})
            summary = data.get('execution_summary', {})
            stats = data.get('final_stats', {})
            
            print(f"\n📊 最新结果摘要 ({fast_files[0]}):")
            print(f"   查询: {session_info.get('user_prompt', 'N/A')[:50]}...")
            print(f"   成功: {summary.get('success', False)}")
            print(f"   耗时: {summary.get('total_execution_time', 0):.2f}秒")
            print(f"   推荐: {summary.get('recommendations_count', 0)}")
            print(f"   帖子: {stats.get('relevant_posts_found', 0)}")
            print(f"   用户: {stats.get('candidate_users_found', 0)}")
            
        except Exception as e:
            print(f"❌ 读取结果文件失败: {e}")

async def clear_cache_fast():
    """快速清理缓存"""
    try:
        pipeline = RedditResonancePipeline()
        await pipeline.clear_all_cache()
        await pipeline.close()
        print("✅ 缓存已清理")
    except Exception as e:
        print(f"❌ 清理缓存失败: {e}")

# ============================================================================
# 主程序
# ============================================================================

async def main():
    """主程序入口"""
    print("⚡ Reddit共鸣推荐系统 - 快速调试模式")
    print("💡 功能说明：")
    print("   - 大幅减少数据量，显著提高运行速度")
    print("   - 适合开发和调试阶段使用")
    print("   - 保持原有功能逻辑，只是减少数据规模")
    print("   🆕 新增：极速模式，进一步减少执行时间")
    print()
    
    # 预设测试查询
    test_queries = [
        "我坚持创业7年了，但一直都没什么成绩，每天都在学习反思，和别人聊天的时候总能意识到自己懂得很多东西，但是到自己做了又总是失败。我还应该继续坚持吗？",
        "我是一个计算机专业的学生，对未来的职业发展很迷茫，不知道应该考研还是直接工作。看到周围同学都在准备考研，我也很焦虑。",
        "最近工作压力很大，总是加班到很晚，感觉生活完全没有平衡。想要改变现状，但又担心影响职业发展。"
    ]
    
    while True:
        display_fast_menu()
        try:
            choice = input("请选择 (1-7): ").strip()
            
            if choice == "1":
                print("\n请选择测试查询:")
                for i, query in enumerate(test_queries, 1):
                    print(f"  {i}. {query[:30]}...")
                print(f"  {len(test_queries)+1}. 自定义输入")
                
                query_choice = input(f"请选择 (1-{len(test_queries)+1}): ").strip()
                
                if query_choice.isdigit() and 1 <= int(query_choice) <= len(test_queries):
                    test_prompt = test_queries[int(query_choice)-1]
                elif query_choice == str(len(test_queries)+1):
                    test_prompt = input("请输入您的测试查询: ").strip()
                    if not test_prompt:
                        print("❌ 输入不能为空")
                        continue
                else:
                    print("❌ 无效选择")
                    continue
                
                await run_fast_full_pipeline(test_prompt)
                
            elif choice == "2":
                pipeline = RedditResonancePipeline()
                await show_cache_list_fast(pipeline)
                await pipeline.close()
                
            elif choice == "3":
                pipeline = RedditResonancePipeline()
                cached_sessions = await show_cache_list_fast(pipeline)
                if cached_sessions:
                    try:
                        cache_idx = int(input("请选择要使用的缓存编号: ").strip()) - 1
                        if 0 <= cache_idx < len(cached_sessions):
                            cache_key = cached_sessions[cache_idx]['cache_key']
                            test_prompt = cached_sessions[cache_idx].get('user_prompt', '测试查询')
                            await run_fast_from_cache(cache_key, test_prompt)
                        else:
                            print("❌ 无效编号")
                    except ValueError:
                        print("❌ 请输入有效数字")
                await pipeline.close()
                
            elif choice == "4":
                print("\n🏃‍♂️ 极速模式 - 进一步减少数据量")
                query_choice = input("使用默认查询？(Y/n): ").strip().lower()
                if query_choice in ['', 'y', 'yes']:
                    test_prompt = test_queries[0]
                else:
                    test_prompt = input("请输入测试查询: ").strip()
                    if not test_prompt:
                        test_prompt = test_queries[0]
                
                await run_ultra_fast_mode(test_prompt)
                
            elif choice == "5":
                view_fast_results()
                
            elif choice == "6":
                confirm = input("确认清理所有缓存？(y/N): ").strip().lower()
                if confirm == 'y':
                    await clear_cache_fast()
                
            elif choice == "7":
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请重新输入")
            
            print("\n" + "="*50)
            input("按回车键继续...")
            print()
            
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序出错: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 