#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版搜索测试
尝试更短的关键词，看能否找到相关帖子
"""

import requests
import json
import urllib.parse
import time
from datetime import datetime

API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
PROFILES_DATASET_ID = "gd_lwxmeb2u1cniijd7t4"

def test_search_with_shorter_keywords():
    """测试用更短的关键词搜索"""
    print("🔍 尝试用更短的关键词搜索")
    
    # 从长句中提取关键词
    keywords = [
        "entrepreneurship 7 years",
        "persevering entrepreneurship", 
        "entrepreneurship success",
        "haven't achieved success"
    ]
    
    results = {}
    
    for keyword in keywords:
        print(f"\n测试关键词: '{keyword}'")
        result = search_with_keyword(keyword)
        results[keyword] = result
        
        if result.get("success"):
            print(f"✅ 关键词 '{keyword}' 搜索成功，快照ID: {result['snapshot_id']}")
        else:
            print(f"❌ 关键词 '{keyword}' 搜索失败")
        
        time.sleep(2)  # 避免请求过快
    
    return results

def search_with_keyword(keyword):
    """用单个关键词搜索"""
    encoded_query = urllib.parse.quote_plus(keyword)
    search_url = f"https://x.com/search?q={encoded_query}&f=user"
    
    data = [{
        "url": search_url,
        "max_number_of_posts": 5  # 减少数量
    }]
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json",
    }
    
    params = {
        "dataset_id": PROFILES_DATASET_ID,
        "include_errors": "true",
    }
    
    try:
        response = requests.post("https://api.brightdata.com/datasets/v3/trigger",
                               headers=headers, params=params, json=data, timeout=30)
        result = response.json()
        
        if response.status_code == 200:
            return {
                "success": True,
                "snapshot_id": result.get("snapshot_id"),
                "search_url": search_url
            }
        else:
            return {"success": False, "error": result}
            
    except Exception as e:
        return {"success": False, "error": str(e)}

def check_existing_snapshots():
    """检查现有快照，看看之前是否有相关搜索结果"""
    print("📋 检查现有快照...")
    
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    params = {"dataset_id": PROFILES_DATASET_ID, "status": "ready"}
    
    try:
        response = requests.get("https://api.brightdata.com/datasets/v3/snapshots", 
                              headers=headers, params=params, timeout=30)
        
        if response.status_code == 200:
            snapshots = response.json()
            if snapshots:
                print(f"发现 {len(snapshots)} 个已完成的快照")
                
                # 检查最近的几个快照
                recent_snapshots = snapshots[-5:]  # 最新5个
                for i, snap in enumerate(recent_snapshots, 1):
                    snapshot_id = snap.get('id')
                    size = snap.get('dataset_size')
                    created = snap.get('created', '')
                    print(f"{i}. ID: {snapshot_id} - 大小: {size} 条 - 时间: {created}")
                
                # 尝试下载最新的快照看看内容
                if recent_snapshots:
                    latest_snapshot = recent_snapshots[-1]
                    download_and_analyze_snapshot(latest_snapshot['id'], "最新快照")
                
                return snapshots
        return []
        
    except Exception as e:
        print(f"❌ 检查快照异常: {e}")
        return []

def download_and_analyze_snapshot(snapshot_id, name):
    """下载并分析快照内容"""
    print(f"\n📥 下载{name}: {snapshot_id}")
    
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    
    try:
        response = requests.get(f"https://api.brightdata.com/datasets/v3/snapshot/{snapshot_id}",
                              headers=headers, params={"format": "json"}, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            # 保存到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"brightdata_apitest/data/existing_snapshot_{snapshot_id}_{timestamp}.json"
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 数据已保存: {filename}")
            
            # 简单分析
            if isinstance(data, list) and data:
                print(f"📊 数据分析: 共 {len(data)} 条记录")
                
                # 查看前几条记录的结构
                sample = data[0]
                print("📝 数据结构示例:")
                print(f"  - 字段: {list(sample.keys())}")
                
                # 如果有posts字段，显示一些内容
                if "posts" in sample and sample["posts"]:
                    posts = sample["posts"][:3]  # 前3条帖子
                    print(f"  - 用户: {sample.get('profile_name', 'Unknown')}")
                    print(f"  - 帖子数: {len(sample['posts'])}")
                    for i, post in enumerate(posts, 1):
                        content = post.get("description", "")[:80] + "..."
                        print(f"    {i}. {content}")
            
            return {"filename": filename, "data": data}
        else:
            print(f"❌ 下载失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 下载异常: {e}")
        return None

def main():
    print("🧪 简化版搜索测试")
    print("目标: 找到关于创业7年但没有成功的相关内容")
    print("=" * 60)
    
    # 先检查现有快照
    existing_snapshots = check_existing_snapshots()
    
    print("\n" + "="*60)
    print("💡 搜索总结:")
    print("由于BrightData的限制，直接搜索推文内容比较困难")
    print("建议的替代方案:")
    print("1. 手动在Twitter搜索并复制推文URL")
    print("2. 使用Profile Scraper抓取知名创业者的推文")
    print("3. 搜索创业相关的标签用户")
    
    # 尝试一个关键词搜索
    print(f"\n🔍 尝试搜索创业相关用户...")
    result = search_with_keyword("entrepreneur startup")
    
    if result.get("success"):
        print(f"✅ 找到创业相关用户，快照ID: {result['snapshot_id']}")
        print("💡 这可以作为替代方案，获取创业者的推文内容")
    else:
        print("❌ 搜索失败")
    
    print(f"\n📊 已有快照数量: {len(existing_snapshots)}")
    print("建议: 查看现有快照的内容，可能包含相关的创业话题")

if __name__ == "__main__":
    main() 