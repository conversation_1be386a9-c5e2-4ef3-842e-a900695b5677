#!/usr/bin/env python3
"""
测试google/gemini-2.5-flash模型配置
"""
import os
import sys
import unittest
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
only_profile_dir = project_root / "only_profile"
sys.path.insert(0, str(only_profile_dir))

class TestGeminiModelConfig(unittest.TestCase):
    """测试Gemini模型配置"""
    
    def setUp(self):
        """设置测试环境"""
        # 确保加载正确的配置
        if 'config' in sys.modules:
            del sys.modules['config']
        
        # 设置环境变量
        os.environ['LLM_MODEL'] = 'google/gemini-2.5-flash'
        os.environ['EMBEDDING_MODEL'] = 'Qwen/Qwen3-Embedding-8B'
        
        # 重新导入配置
        from config import config
        self.config = config
    
    def test_model_configuration(self):
        """测试模型配置是否正确"""
        print(f"当前模型: {self.config.LLM_MODEL}")
        print(f"嵌入模型: {self.config.EMBEDDING_MODEL}")
        
        # 验证模型名称
        self.assertEqual(self.config.LLM_MODEL, "google/gemini-2.5-flash")
        self.assertEqual(self.config.EMBEDDING_MODEL, "Qwen/Qwen3-Embedding-8B")
        
        # 验证模型配置是否存在
        self.assertIn("google/gemini-2.5-flash", self.config.MODEL_CONFIGS)
        
        # 获取模型配置
        model_config = self.config.MODEL_CONFIGS["google/gemini-2.5-flash"]
        
        # 验证配置参数
        self.assertEqual(model_config["context_window"], 1048576)
        self.assertTrue(model_config["supports_json_mode"])
        self.assertTrue(model_config["supports_function_calling"])
        self.assertEqual(model_config["max_output_tokens"], 8192)
        self.assertEqual(model_config["recommended_temperature"], 0.7)
        self.assertEqual(model_config["recommended_top_p"], 0.9)
        
        print("✅ 模型配置验证通过")
    
    def test_token_budget_calculation(self):
        """测试Token预算计算"""
        token_budget = self.config.calculate_token_budget()
        
        print(f"上下文窗口: {token_budget['context_window']}")
        print(f"最大输出Token: {token_budget['max_output_tokens']}")
        print(f"系统提示Token: {token_budget['system_prompt_tokens']}")
        print(f"可用Token: {token_budget['available_tokens']}")
        print(f"有效预算: {token_budget['effective_budget']}")
        
        # 验证Token预算计算
        self.assertEqual(token_budget['context_window'], 1048576)
        self.assertGreater(token_budget['effective_budget'], 800000)  # 应该有足够的Token
        
        print("✅ Token预算计算验证通过")
    
    def test_model_description(self):
        """测试模型描述"""
        description = self.config.get_model_description()
        expected_description = "Google Gemini 2.5 Flash - 快速高效的对话模型"
        
        self.assertEqual(description, expected_description)
        print(f"✅ 模型描述: {description}")
    
    def test_config_validation(self):
        """测试配置验证"""
        validation = self.config.validate_config()
        
        print(f"配置验证结果: {validation}")
        
        # 验证配置是否有效
        self.assertTrue(validation["valid"])
        
        # 检查是否有警告
        if validation["warnings"]:
            print("⚠️ 配置警告:")
            for warning in validation["warnings"]:
                print(f"   - {warning}")
        
        print("✅ 配置验证通过")
    
    def test_config_summary(self):
        """测试配置摘要"""
        summary = self.config.get_config_summary()
        
        print(f"配置摘要: {summary}")
        
        # 验证摘要信息
        self.assertEqual(summary['llm_model'], 'google/gemini-2.5-flash')
        self.assertEqual(summary['embedding_model'], 'Qwen/Qwen3-Embedding-8B')
        self.assertIn('ai_service', summary)
        self.assertIn('token_budget', summary)
        
        print("✅ 配置摘要验证通过")

def run_performance_test():
    """运行性能测试"""
    print("\n🚀 开始性能测试...")
    
    # 模拟配置加载时间
    import time
    start_time = time.time()
    
    # 重新加载配置
    if 'config' in sys.modules:
        del sys.modules['config']
    
    from config import config
    load_time = time.time() - start_time
    
    print(f"配置加载时间: {load_time:.3f}秒")
    
    # 测试Token预算计算性能
    start_time = time.time()
    for _ in range(100):
        config.calculate_token_budget()
    calc_time = time.time() - start_time
    
    print(f"100次Token预算计算时间: {calc_time:.3f}秒")
    print(f"平均每次计算时间: {calc_time/100:.6f}秒")
    
    print("✅ 性能测试完成")

if __name__ == '__main__':
    print("🧪 开始测试google/gemini-2.5-flash模型配置")
    print("=" * 60)
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()
    
    print("\n🎉 所有测试完成！")
    print("=" * 60) 