#!/usr/bin/env python3
"""
测试 only_profile/url_parser.py 的专业单元测试
测试URL解析器的所有功能、边界情况和异常处理
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from dataclasses import dataclass
from typing import Optional, List, Dict
import re

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 模拟 RedditLinkType 和 RedditLinkInfo 
from enum import Enum

class RedditLinkType(Enum):
    USER = "user"
    POST = "post"
    COMMENT = "comment"
    SUBREDDIT = "subreddit"
    UNKNOWN = "unknown"

@dataclass
class RedditLinkInfo:
    link_type: RedditLinkType
    username: Optional[str] = None
    subreddit: Optional[str] = None
    post_id: Optional[str] = None
    comment_id: Optional[str] = None
    original_url: Optional[str] = None

# 导入待测试的模块
try:
    from only_profile.url_parser import RedditUrlParser
except ImportError as e:
    print(f"导入错误: {e}")
    # 创建模拟的 RedditUrlParser 类进行测试
    class RedditUrlParser:
        def __init__(self):
            self.user_patterns = [
                r'https?://(?:www\.)?reddit\.com/u(?:ser)?/([^/?]+)',
                r'https?://(?:www\.)?reddit\.com/user/([^/?]+)',
                r'https?://(?:old\.)?reddit\.com/u(?:ser)?/([^/?]+)',
            ]
            self.post_patterns = [
                r'https?://(?:www\.)?reddit\.com/r/([^/]+)/comments/([^/]+)',
                r'https?://(?:old\.)?reddit\.com/r/([^/]+)/comments/([^/]+)',
            ]
            self.comment_patterns = [
                r'https?://(?:www\.)?reddit\.com/r/([^/]+)/comments/([^/]+)/[^/]+/([^/?]+)',
                r'https?://(?:old\.)?reddit\.com/r/([^/]+)/comments/([^/]+)/[^/]+/([^/?]+)',
            ]
            
        def parse_url(self, url: str) -> RedditLinkInfo:
            """解析Reddit URL"""
            if not url or not isinstance(url, str):
                return RedditLinkInfo(RedditLinkType.UNKNOWN, original_url=url)
            
            # 清理URL
            url = url.strip()
            
            # 检查评论链接
            for pattern in self.comment_patterns:
                match = re.match(pattern, url)
                if match:
                    return RedditLinkInfo(
                        RedditLinkType.COMMENT,
                        subreddit=match.group(1),
                        post_id=match.group(2),
                        comment_id=match.group(3),
                        original_url=url
                    )
            
            # 检查帖子链接
            for pattern in self.post_patterns:
                match = re.match(pattern, url)
                if match:
                    return RedditLinkInfo(
                        RedditLinkType.POST,
                        subreddit=match.group(1),
                        post_id=match.group(2),
                        original_url=url
                    )
            
            # 检查用户链接
            for pattern in self.user_patterns:
                match = re.match(pattern, url)
                if match:
                    return RedditLinkInfo(
                        RedditLinkType.USER,
                        username=match.group(1),
                        original_url=url
                    )
            
            return RedditLinkInfo(RedditLinkType.UNKNOWN, original_url=url)
        
        def validate_url(self, url: str) -> bool:
            """验证URL是否为有效的Reddit链接"""
            if not url or not isinstance(url, str):
                return False
            
            reddit_domains = ['reddit.com', 'www.reddit.com', 'old.reddit.com']
            return any(domain in url for domain in reddit_domains)
        
        def extract_username_from_url(self, url: str) -> Optional[str]:
            """从URL中提取用户名"""
            parsed = self.parse_url(url)
            return parsed.username if parsed.link_type == RedditLinkType.USER else None
        
        def get_supported_formats(self) -> List[str]:
            """返回支持的URL格式"""
            return [
                "https://reddit.com/u/username",
                "https://reddit.com/user/username",
                "https://reddit.com/r/subreddit/comments/post_id",
                "https://reddit.com/r/subreddit/comments/post_id/title/comment_id"
            ]


class TestRedditUrlParser(unittest.TestCase):
    """Reddit URL解析器的专业单元测试"""
    
    def setUp(self):
        """测试设置"""
        self.parser = RedditUrlParser()
        
        # 测试用的有效URL
        self.valid_user_urls = [
            "https://reddit.com/u/testuser",
            "https://www.reddit.com/user/testuser",
            "https://old.reddit.com/u/testuser",
            "https://reddit.com/user/test_user123",
            "https://www.reddit.com/u/Test-User_123"
        ]
        
        self.valid_post_urls = [
            "https://reddit.com/r/Python/comments/abc123",
            "https://www.reddit.com/r/Python/comments/abc123/title",
            "https://old.reddit.com/r/MachineLearning/comments/def456",
            "https://reddit.com/r/test_sub/comments/ghi789/post_title"
        ]
        
        self.valid_comment_urls = [
            "https://reddit.com/r/Python/comments/abc123/title/xyz789",
            "https://www.reddit.com/r/Python/comments/abc123/post_title/comment_id_123",
            "https://old.reddit.com/r/MachineLearning/comments/def456/some_title/comment_abc"
        ]
        
        # 测试用的无效URL
        self.invalid_urls = [
            "",
            None,
            "not_a_url",
            "https://twitter.com/user",
            "https://facebook.com/user",
            "https://reddit.com",  # 不完整的URL
            "ftp://reddit.com/u/user",  # 错误的协议
            "https://reddit.com/invalid/path",
            "https://reddit.com/u/",  # 空用户名
            "https://reddit.com/r//comments/abc123",  # 空subreddit
        ]
    
    def test_parse_user_urls(self):
        """测试用户URL解析"""
        print("测试用户URL解析...")
        
        for url in self.valid_user_urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertEqual(result.link_type, RedditLinkType.USER)
                self.assertIsNotNone(result.username)
                self.assertIsNone(result.subreddit)
                self.assertIsNone(result.post_id)
                self.assertIsNone(result.comment_id)
                self.assertEqual(result.original_url, url)
    
    def test_parse_post_urls(self):
        """测试帖子URL解析"""
        print("测试帖子URL解析...")
        
        for url in self.valid_post_urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertEqual(result.link_type, RedditLinkType.POST)
                self.assertIsNone(result.username)
                self.assertIsNotNone(result.subreddit)
                self.assertIsNotNone(result.post_id)
                self.assertIsNone(result.comment_id)
                self.assertEqual(result.original_url, url)
    
    def test_parse_comment_urls(self):
        """测试评论URL解析"""
        print("测试评论URL解析...")
        
        for url in self.valid_comment_urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertEqual(result.link_type, RedditLinkType.COMMENT)
                self.assertIsNone(result.username)
                self.assertIsNotNone(result.subreddit)
                self.assertIsNotNone(result.post_id)
                self.assertIsNotNone(result.comment_id)
                self.assertEqual(result.original_url, url)
    
    def test_parse_invalid_urls(self):
        """测试无效URL解析"""
        print("测试无效URL解析...")
        
        for url in self.invalid_urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertEqual(result.link_type, RedditLinkType.UNKNOWN)
                self.assertEqual(result.original_url, url)
    
    def test_validate_url_valid(self):
        """测试有效URL验证"""
        print("测试有效URL验证...")
        
        valid_urls = self.valid_user_urls + self.valid_post_urls + self.valid_comment_urls
        for url in valid_urls:
            with self.subTest(url=url):
                self.assertTrue(self.parser.validate_url(url))
    
    def test_validate_url_invalid(self):
        """测试无效URL验证"""
        print("测试无效URL验证...")
        
        for url in self.invalid_urls:
            with self.subTest(url=url):
                self.assertFalse(self.parser.validate_url(url))
    
    def test_extract_username_from_url(self):
        """测试从URL提取用户名"""
        print("测试从URL提取用户名...")
        
        # 测试用户URL
        user_url = "https://reddit.com/u/testuser"
        username = self.parser.extract_username_from_url(user_url)
        self.assertEqual(username, "testuser")
        
        # 测试非用户URL
        post_url = "https://reddit.com/r/Python/comments/abc123"
        username = self.parser.extract_username_from_url(post_url)
        self.assertIsNone(username)
        
        # 测试无效URL
        invalid_url = "https://twitter.com/user"
        username = self.parser.extract_username_from_url(invalid_url)
        self.assertIsNone(username)
    
    def test_get_supported_formats(self):
        """测试获取支持的格式"""
        print("测试获取支持的格式...")
        
        formats = self.parser.get_supported_formats()
        self.assertIsInstance(formats, list)
        self.assertTrue(len(formats) > 0)
        
        # 检查格式是否包含预期的模式
        format_str = " ".join(formats)
        self.assertIn("reddit.com", format_str)
        self.assertIn("username", format_str)
        self.assertIn("subreddit", format_str)
    
    def test_edge_cases(self):
        """测试边界情况"""
        print("测试边界情况...")
        
        # 测试空字符串
        result = self.parser.parse_url("")
        self.assertEqual(result.link_type, RedditLinkType.UNKNOWN)
        
        # 测试None
        result = self.parser.parse_url(None)
        self.assertEqual(result.link_type, RedditLinkType.UNKNOWN)
        
        # 测试非字符串类型
        result = self.parser.parse_url(123)
        self.assertEqual(result.link_type, RedditLinkType.UNKNOWN)
        
        # 测试包含空格的URL
        url_with_spaces = "  https://reddit.com/u/testuser  "
        result = self.parser.parse_url(url_with_spaces)
        self.assertEqual(result.link_type, RedditLinkType.USER)
    
    def test_url_variations(self):
        """测试URL变体"""
        print("测试URL变体...")
        
        # 测试不同的协议
        urls = [
            "https://reddit.com/u/testuser",
            "http://reddit.com/u/testuser",
            "https://www.reddit.com/u/testuser",
            "https://old.reddit.com/u/testuser"
        ]
        
        for url in urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertEqual(result.link_type, RedditLinkType.USER)
                self.assertEqual(result.username, "testuser")
    
    def test_case_sensitivity(self):
        """测试大小写敏感性"""
        print("测试大小写敏感性...")
        
        # 测试不同大小写的域名
        urls = [
            "https://Reddit.com/u/testuser",
            "https://REDDIT.COM/u/testuser",
            "https://reddit.COM/u/testuser"
        ]
        
        for url in urls:
            with self.subTest(url=url):
                # 域名通常不区分大小写，但用户名可能区分
                result = self.parser.parse_url(url)
                # 这里假设我们的解析器能处理大小写变体
                self.assertTrue(result.link_type in [RedditLinkType.USER, RedditLinkType.UNKNOWN])
    
    def test_special_characters_in_username(self):
        """测试用户名中的特殊字符"""
        print("测试用户名中的特殊字符...")
        
        special_usernames = [
            "user_123",
            "user-abc",
            "User123",
            "test_user_123",
            "user-with-dashes"
        ]
        
        for username in special_usernames:
            with self.subTest(username=username):
                url = f"https://reddit.com/u/{username}"
                result = self.parser.parse_url(url)
                self.assertEqual(result.link_type, RedditLinkType.USER)
                self.assertEqual(result.username, username)
    
    def test_parser_consistency(self):
        """测试解析器的一致性"""
        print("测试解析器的一致性...")
        
        # 多次解析同一URL应该得到相同结果
        url = "https://reddit.com/u/testuser"
        
        results = []
        for _ in range(10):
            result = self.parser.parse_url(url)
            results.append(result)
        
        # 所有结果应该相同
        first_result = results[0]
        for result in results[1:]:
            self.assertEqual(result.link_type, first_result.link_type)
            self.assertEqual(result.username, first_result.username)
            self.assertEqual(result.original_url, first_result.original_url)
    
    def test_performance_with_large_input(self):
        """测试大量输入时的性能"""
        print("测试大量输入时的性能...")
        
        # 创建大量URL进行测试
        urls = []
        for i in range(1000):
            urls.append(f"https://reddit.com/u/user{i}")
            urls.append(f"https://reddit.com/r/sub{i}/comments/post{i}")
        
        # 测试解析所有URL
        results = []
        for url in urls:
            result = self.parser.parse_url(url)
            results.append(result)
        
        # 验证结果
        self.assertEqual(len(results), len(urls))
        user_count = sum(1 for r in results if r.link_type == RedditLinkType.USER)
        post_count = sum(1 for r in results if r.link_type == RedditLinkType.POST)
        
        self.assertEqual(user_count, 1000)
        self.assertEqual(post_count, 1000)


class TestRedditUrlParserIntegration(unittest.TestCase):
    """Reddit URL解析器的集成测试"""
    
    def setUp(self):
        """集成测试设置"""
        self.parser = RedditUrlParser()
    
    def test_real_world_urls(self):
        """测试真实世界的URL"""
        print("测试真实世界的URL...")
        
        # 模拟一些真实的Reddit URL
        real_urls = [
            "https://www.reddit.com/r/Python/comments/12345/how_to_learn_python/",
            "https://old.reddit.com/u/spez",
            "https://reddit.com/user/AutoModerator",
            "https://www.reddit.com/r/MachineLearning/comments/abcdef/discussion_about_ai/comment123"
        ]
        
        for url in real_urls:
            with self.subTest(url=url):
                result = self.parser.parse_url(url)
                self.assertNotEqual(result.link_type, RedditLinkType.UNKNOWN)
                self.assertEqual(result.original_url, url)
    
    def test_parser_workflow(self):
        """测试完整的解析工作流"""
        print("测试完整的解析工作流...")
        
        # 模拟用户输入的URL
        user_input = "https://reddit.com/u/testuser"
        
        # 1. 验证URL
        is_valid = self.parser.validate_url(user_input)
        self.assertTrue(is_valid)
        
        # 2. 解析URL
        parsed = self.parser.parse_url(user_input)
        self.assertEqual(parsed.link_type, RedditLinkType.USER)
        
        # 3. 提取用户名
        username = self.parser.extract_username_from_url(user_input)
        self.assertEqual(username, "testuser")
        
        # 4. 验证解析结果的一致性
        self.assertEqual(parsed.username, username)


def run_url_parser_tests():
    """运行URL解析器的所有测试"""
    print("=" * 80)
    print("开始运行 Reddit URL 解析器单元测试")
    print("=" * 80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加基本功能测试
    test_suite.addTest(unittest.makeSuite(TestRedditUrlParser))
    
    # 添加集成测试
    test_suite.addTest(unittest.makeSuite(TestRedditUrlParserIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("URL解析器测试结果:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("=" * 80)
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_url_parser_tests()
    sys.exit(0 if success else 1) 