"""
测试LLM时间线感知图谱构建优化
验证直接让LLM处理时间信息的效果
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.pipeline import RedditResonancePipeline
from resona.config import settings

async def test_llm_timeline_optimization():
    """测试LLM时间线感知优化"""
    
    print("🧪 开始测试LLM时间线感知图谱构建优化...")
    
    # 创建测试数据 - 模拟带时间标记的Reddit内容
    test_usernames = ["test_user_timeline"]
    
    # 初始化Pipeline
    pipeline = RedditResonancePipeline()
    
    try:
        # 模拟时间线内容（使用 _format_contents_with_timeline 的输出格式）
        test_temporal_contents = [
            "【2022年3月】 刚开始创业，觉得很兴奋，满怀希望地投入到项目中",
            "【2022年8月】 遇到了第一次重大挫折，投资人撤资了，当时感到很迷茫",
            "【2023年1月】 经过反思，我意识到自己在市场分析上还不够成熟",
            "【2023年6月】 后来我学会了更仔细地做市场调研，现在我相信准备充分很重要",
            "【2023年10月】 虽然还在努力，但现在的心态比以前平和多了，不再那么焦虑"
        ]
        
        print(f"📝 测试内容：{len(test_temporal_contents)} 条时间线内容")
        for i, content in enumerate(test_temporal_contents):
            print(f"  {i+1}. {content}")
        
        # 直接测试时间线格式化功能
        print("\n🔧 测试时间线格式化功能...")
        
        # 模拟用户数据结构
        mock_user_data = {
            'posts': [
                {
                    'title': '创业初期的想法',
                    'text': '刚开始创业，觉得很兴奋，满怀希望地投入到项目中',
                    'timestamp': datetime(2022, 3, 15),
                    'score': 5
                },
                {
                    'title': '遇到挫折了',
                    'text': '遇到了第一次重大挫折，投资人撤资了，当时感到很迷茫',
                    'timestamp': datetime(2022, 8, 20),
                    'score': 12
                }
            ],
            'comments': [
                {
                    'text': '经过反思，我意识到自己在市场分析上还不够成熟',
                    'timestamp': datetime(2023, 1, 10),
                    'score': 8
                },
                {
                    'text': '后来我学会了更仔细地做市间调研，现在我相信准备充分很重要',
                    'timestamp': datetime(2023, 6, 5),
                    'score': 15
                },
                {
                    'text': '虽然还在努力，但现在的心态比以前平和多了，不再那么焦虑',
                    'timestamp': datetime(2023, 10, 12),
                    'score': 10
                }
            ]
        }
        
        # 测试格式化功能
        formatted_contents = pipeline._format_contents_with_timeline(mock_user_data)
        print(f"✅ 格式化了 {len(formatted_contents)} 条内容")
        for content in formatted_contents[:3]:
            print(f"  - {content[:100]}...")
        
        # 测试质量选择功能
        selected_contents = pipeline._select_quality_temporal_contents(formatted_contents)
        print(f"✅ 选择了 {len(selected_contents)} 条高质量内容")
        
        # 测试LLM图谱构建（如果有AI服务的话）
        if hasattr(pipeline, 'graph_builder') and pipeline.graph_builder.ai_service:
            print("\n🧠 测试LLM时间线图谱构建...")
            
            try:
                user_graph = await pipeline.graph_builder.build_user_graph(
                    contents=selected_contents[:3],  # 限制数量避免超时
                    user_context="Reddit用户 test_user_timeline，请充分利用时间信息构建因果关系"
                )
                
                print(f"✅ LLM图谱构建完成:")
                print(f"  📊 节点数: {len(user_graph.nodes)}")
                print(f"  🔗 边数: {len(user_graph.edges)}")
                
                # 统计时间线关系
                temporal_edges = [e for e in user_graph.edges if 'temporal' in e.evidence.lower() or 'time' in e.evidence.lower()]
                print(f"  ⏰ 时间线关系数: {len(temporal_edges)}")
                
                if temporal_edges:
                    print("\n🎯 时间线关系示例:")
                    for edge in temporal_edges[:3]:
                        print(f"  - {edge.relation_type.value}: {edge.evidence[:80]}...")
                
                # 显示节点类型分布
                node_types = {}
                for node in user_graph.nodes.values():
                    node_type = node.node_type.value
                    node_types[node_type] = node_types.get(node_type, 0) + 1
                
                print(f"\n📈 节点类型分布: {node_types}")
                
                result = {
                    'test_success': True,
                    'llm_graph_built': True,
                    'nodes_count': len(user_graph.nodes),
                    'edges_count': len(user_graph.edges),
                    'temporal_edges_count': len(temporal_edges),
                    'node_types': node_types,
                    'formatted_contents_count': len(formatted_contents),
                    'selected_contents_count': len(selected_contents)
                }
                
            except Exception as e:
                print(f"⚠️ LLM图谱构建失败 (可能是AI服务不可用): {e}")
                result = {
                    'test_success': True,
                    'llm_graph_built': False,
                    'error': str(e),
                    'formatted_contents_count': len(formatted_contents),
                    'selected_contents_count': len(selected_contents)
                }
        else:
            print("⚠️ AI服务不可用，跳过LLM图谱构建测试")
            result = {
                'test_success': True,
                'llm_graph_built': False,
                'reason': 'AI service not available',
                'formatted_contents_count': len(formatted_contents),
                'selected_contents_count': len(selected_contents)
            }
        
        # 保存测试结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        result_file = f"cursor_test/test_results/llm_timeline_test_{timestamp}.json"
        
        os.makedirs(os.path.dirname(result_file), exist_ok=True)
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 测试结果已保存到: {result_file}")
        print("🎉 LLM时间线感知优化测试完成!")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {'test_success': False, 'error': str(e)}
    
    finally:
        # 清理资源
        if hasattr(pipeline, 'close'):
            await pipeline.close()

def test_timeline_detection():
    """测试时间线检测逻辑"""
    print("\n🔍 测试时间线检测逻辑...")
    
    # 创建Pipeline实例
    pipeline = RedditResonancePipeline()
    
    # 测试用例
    test_cases = [
        ("【2022年3月】开始创业", True),  # 有时间标记
        ("去年我做了一个项目", False),   # 无明确时间标记  
        ("【2023年1月15日】参加面试", True), # 有具体日期
        ("最近比较迷茫", False),         # 模糊时间
        ("2022年的时候很困惑", False),   # 不是标准格式
    ]
    
    for content, expected in test_cases:
        # 模拟检测逻辑
        has_timeline = '【' in content and '年' in content and '月' in content
        result = "✅" if has_timeline == expected else "❌"
        print(f"  {result} '{content}' -> {has_timeline} (期望: {expected})")
    
    print("🔍 时间线检测测试完成!")

async def main():
    """主测试函数"""
    print("🚀 开始LLM时间线感知图谱构建优化测试")
    print("=" * 60)
    
    # 测试时间线检测
    test_timeline_detection()
    
    # 测试完整功能
    result = await test_llm_timeline_optimization()
    
    print("\n" + "=" * 60)
    if result.get('test_success'):
        print("✅ 所有测试通过!")
        if result.get('llm_graph_built'):
            print(f"🎯 LLM图谱构建成功: {result.get('nodes_count')} 节点, {result.get('edges_count')} 边")
            print(f"⏰ 时间线关系: {result.get('temporal_edges_count')} 条")
        print(f"📊 内容处理: {result.get('formatted_contents_count')} -> {result.get('selected_contents_count')}")
    else:
        print("❌ 测试失败!")
        print(f"错误: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    asyncio.run(main()) 