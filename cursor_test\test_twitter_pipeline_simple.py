"""
Twitter平台简化测试脚本 - 适配Windows环境
移除emoji字符，解决编码问题
"""
import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from resona.services.twitter_service import TwitterService
from resona.config import settings

# 配置日志 - 移除emoji字符
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cursor_test/twitter_test_simple.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def test_twitter_basic():
    """基础Twitter连接测试"""
    logger.info("开始Twitter基础连接测试...")
    
    try:
        twitter_service = TwitterService()
        
        # 简单连接测试
        logger.info("正在测试Twitter连接...")
        connection_result = await twitter_service.test_connection()
        
        if connection_result:
            logger.info("SUCCESS: Twitter连接测试成功！")
            return True
        else:
            logger.error("FAILED: Twitter连接测试失败")
            return False
            
    except Exception as e:
        logger.error(f"ERROR: Twitter连接测试异常: {e}")
        return False

async def test_search_function():
    """测试搜索功能"""
    logger.info("开始Twitter搜索功能测试...")
    
    try:
        twitter_service = TwitterService()
        
        # 使用简单关键词测试
        test_keywords = ["python", "tech"]
        
        logger.info(f"搜索关键词: {test_keywords}")
        
        # 执行搜索
        search_results = await twitter_service.search_posts_by_keywords_enhanced(
            keywords=test_keywords,
            limit=5,
            time_filter="week"
        )
        
        logger.info(f"搜索完成，获得 {len(search_results)} 条推文")
        
        if search_results:
            logger.info("SUCCESS: 搜索功能正常")
            
            # 显示前3条结果
            for i, post in enumerate(search_results[:3]):
                logger.info(f"推文 {i+1}:")
                logger.info(f"  作者: @{post.get('author', 'unknown')}")
                logger.info(f"  内容: {post.get('text', '')[:50]}...")
                logger.info(f"  质量分数: {post.get('quality_score', 0):.2f}")
                logger.info("---")
            
            return True
        else:
            logger.warning("WARNING: 搜索结果为空")
            return False
            
    except Exception as e:
        logger.error(f"ERROR: 搜索功能测试失败: {e}")
        return False

async def test_proxy_connection():
    """测试代理连接"""
    logger.info("检查代理连接状态...")
    
    try:
        import requests
        
        # 测试不同代理端口
        proxy_ports = [7890, 1080, 8080]
        
        for port in proxy_ports:
            try:
                proxies = {
                    'http': f'http://127.0.0.1:{port}',
                    'https': f'http://127.0.0.1:{port}'
                }
                
                logger.info(f"测试代理端口: {port}")
                
                # 测试连接Google（简单测试）
                response = requests.get(
                    'https://www.google.com',
                    proxies=proxies,
                    timeout=5
                )
                
                if response.status_code == 200:
                    logger.info(f"SUCCESS: 代理端口 {port} 可用")
                    return True
                    
            except Exception as e:
                logger.info(f"代理端口 {port} 不可用: {e}")
                continue
        
        logger.warning("WARNING: 未找到可用代理")
        return False
        
    except Exception as e:
        logger.error(f"ERROR: 代理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("=" * 50)
    logger.info("开始Twitter平台简化测试")
    logger.info("=" * 50)
    
    test_results = {}
    
    try:
        # 测试1: 代理连接
        logger.info("测试1: 代理连接检查")
        test_results['proxy'] = await test_proxy_connection()
        logger.info("")
        
        # 测试2: Twitter基础连接
        logger.info("测试2: Twitter基础连接")
        test_results['connection'] = await test_twitter_basic()
        logger.info("")
        
        # 测试3: 搜索功能（仅在连接成功时）
        if test_results['connection']:
            logger.info("测试3: 搜索功能")
            test_results['search'] = await test_search_function()
        else:
            logger.info("跳过搜索功能测试（连接失败）")
            test_results['search'] = False
        
        logger.info("")
        
        # 汇总结果
        logger.info("=" * 50)
        logger.info("测试结果汇总:")
        logger.info("=" * 50)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "PASS" if result else "FAIL"
            logger.info(f"  {test_name}: {status}")
        
        logger.info(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            logger.info("所有测试通过！Twitter功能正常")
        elif passed_tests > 0:
            logger.info("部分测试通过，Twitter服务部分可用")
        else:
            logger.error("所有测试失败，Twitter服务不可用")
        
        # 提供建议
        if not test_results.get('proxy', False):
            logger.info("\n建议:")
            logger.info("1. 启动代理软件（Clash/V2Ray）")
            logger.info("2. 设置环境变量: set HTTP_PROXY=http://127.0.0.1:7890")
            logger.info("3. 确保代理允许局域网连接")
            
    except Exception as e:
        logger.error(f"测试执行异常: {e}")
    
    logger.info("\n测试完成")

if __name__ == "__main__":
    asyncio.run(main()) 