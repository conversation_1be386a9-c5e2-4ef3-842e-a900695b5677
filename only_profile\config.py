"""
CogBridges Reddit用户画像分析 - 配置管理
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """应用配置类"""
    
    # =============================================================================
    # Reddit API 配置
    # =============================================================================
    REDDIT_CLIENT_ID = os.getenv("REDDIT_CLIENT_ID", "")
    REDDIT_CLIENT_SECRET = os.getenv("REDDIT_CLIENT_SECRET", "")
    REDDIT_USER_AGENT = os.getenv("REDDIT_USER_AGENT", "OnlyProfile/1.0")
    
    # =============================================================================
    # AI服务配置
    # =============================================================================
    DEEPINFRA_API_KEY = os.getenv("DEEPINFRA_API_KEY", "")
    DEEPINFRA_BASE_URL = "https://api.deepinfra.com/v1/openai"
    
    # 模型配置
    LLM_MODEL = os.getenv("LLM_MODEL", "google/gemini-2.5-flash")
    EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "Qwen/Qwen3-Embedding-8B")
    
    # =============================================================================
    # 应用配置
    # =============================================================================
    DEBUG = os.getenv("DEBUG", "True").lower() == "true"
    DEBUG_STEP_BY_STEP = os.getenv("DEBUG_STEP_BY_STEP", "True").lower() == "true"
    
    # 服务器配置
    HOST = os.getenv("HOST", "127.0.0.1")
    PORT = int(os.getenv("PORT", "5000"))
    SECRET_KEY = os.getenv("SECRET_KEY", "only-profile-secret-key-change-this")
    
    # =============================================================================
    # 数据配置
    # =============================================================================
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./only_profile.db")
    
    # 向量数据库
    FAISS_INDEX_PATH = Path(os.getenv("FAISS_INDEX_PATH", "./data/faiss_index"))
    EMBEDDING_CACHE_PATH = Path(os.getenv("EMBEDDING_CACHE_PATH", "./data/embeddings_cache"))
    EMBEDDING_DIMENSION = int(os.getenv("EMBEDDING_DIMENSION", "4096"))
    
    # =============================================================================
    # Reddit配置
    # =============================================================================
    REDDIT_SEARCH_LIMIT = int(os.getenv("REDDIT_SEARCH_LIMIT", "50"))
    REDDIT_HISTORY_LIMIT = int(os.getenv("REDDIT_HISTORY_LIMIT", "200"))
    
    # 默认子版块
    DEFAULT_SUBREDDITS = [
        "offmychest", "relationships", "career", "findapath",
        "depression", "anxiety", "selfimprovement", "decidingtobebetter",
        "advice", "relationship_advice", "AskReddit", "mentalhealth",
        "jobs", "careerguidance", "socialskills", "getmotivated"
    ]
    
    REDDIT_SUBREDDITS = os.getenv("REDDIT_SUBREDDITS", ",".join(DEFAULT_SUBREDDITS)).split(",")
    
    # =============================================================================
    # 分析配置
    # =============================================================================
    POST_SEARCH_LIMIT = int(os.getenv("POST_SEARCH_LIMIT", "100"))
    POST_RELEVANCE_THRESHOLD = float(os.getenv("POST_RELEVANCE_THRESHOLD", "0.65"))
    COMMENT_MIN_SCORE = int(os.getenv("COMMENT_MIN_SCORE", "0"))
    COMMENT_MIN_LENGTH = int(os.getenv("COMMENT_MIN_LENGTH", "80"))
    MAX_QUALITY_COMMENTERS = int(os.getenv("MAX_QUALITY_COMMENTERS", "30"))
    
    # =============================================================================
    # 并发和性能配置
    # =============================================================================
    MAX_CONCURRENT_EMBEDDINGS = int(os.getenv("MAX_CONCURRENT_EMBEDDINGS", "5"))
    EMBEDDING_BATCH_SIZE = int(os.getenv("EMBEDDING_BATCH_SIZE", "10"))
    MAX_OPENAI_CONCURRENCY = int(os.getenv("MAX_OPENAI_CONCURRENCY", "5"))
    
    # API超时配置
    OPENAI_TIMEOUT = int(os.getenv("OPENAI_TIMEOUT", "3600"))
    OPENAI_RETRY_MAX_ATTEMPTS = int(os.getenv("OPENAI_RETRY_MAX_ATTEMPTS", "3"))
    OPENAI_RETRY_BASE_DELAY = float(os.getenv("OPENAI_RETRY_BASE_DELAY", "1.0"))
    OPENAI_RETRY_MAX_DELAY = float(os.getenv("OPENAI_RETRY_MAX_DELAY", "60.0"))
    
    # =============================================================================
    # LLM Token预算配置
    # =============================================================================
    LLM_OUTPUT_RATIO = float(os.getenv("LLM_OUTPUT_RATIO", "0.2"))
    LLM_SYSTEM_RATIO = float(os.getenv("LLM_SYSTEM_RATIO", "0.1"))
    LLM_SAFETY_FACTOR = float(os.getenv("LLM_SAFETY_FACTOR", "0.9"))
    
    # 自适应配置
    ADAPTIVE_SAFETY = os.getenv("ADAPTIVE_SAFETY", "True").lower() == "true"
    MIN_SAFETY_FACTOR = float(os.getenv("MIN_SAFETY_FACTOR", "0.85"))
    MAX_SAFETY_FACTOR = float(os.getenv("MAX_SAFETY_FACTOR", "0.95"))
    ADAPTIVE_NODE_LIMIT = os.getenv("ADAPTIVE_NODE_LIMIT", "True").lower() == "true"
    MIN_NODES_PER_TEXT = int(os.getenv("MIN_NODES_PER_TEXT", "7"))
    MAX_NODES_PER_TEXT = int(os.getenv("MAX_NODES_PER_TEXT", "10"))
    
    # =============================================================================
    # 文件和日志配置
    # =============================================================================
    DETAILED_RESULTS_DIR = os.getenv("DETAILED_RESULTS_DIR", "./detailed_results")
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "only_profile.log")
    
    # =============================================================================
    # 模型配置映射
    # =============================================================================
    MODEL_CONFIGS = {
        "google/gemini-2.5-flash": {
            "context_window": 1048576,  # 匹配DeepInfra文档
            "description": "Google Gemini 2.5 Flash - 快速高效的对话模型",
            "supports_json_mode": True,
            "supports_function_calling": True,
            "max_output_tokens": 8192,
            "recommended_temperature": 0.7,  # DeepInfra默认值
            "recommended_top_p": 1.0,  # DeepInfra默认值
            "recommended_top_k": 40  # 添加以提高稳定性
        },
        "Qwen/Qwen3-235B-A22B": {
            "context_window": 40960,
            "description": "Qwen3 235B A22B - 超大规模 MoE 推理模型",
            "supports_json_mode": True,
            "supports_function_calling": True,
            "max_output_tokens": 8192,
            "recommended_temperature": 0.6,
            "recommended_top_p": 0.95
        },
        "Qwen/Qwen2.5-72B-Instruct": {
            "context_window": 32768,
            "description": "Qwen 2.5 - 通用对话模型"
        },
        "default": {
            "context_window": 32768,
            "description": "默认配置"
        }
    }
    
    # =============================================================================
    # 缓存和性能配置
    # =============================================================================
    ENABLE_CACHE = os.getenv("ENABLE_CACHE", "True").lower() == "true"
    CACHE_TTL = int(os.getenv("CACHE_TTL", "3600"))
    
    # =============================================================================
    # 开发配置
    # =============================================================================
    DEV_MODE = os.getenv("DEV_MODE", "True").lower() == "true"
    MOCK_API_CALLS = os.getenv("MOCK_API_CALLS", "False").lower() == "true"
    ENABLE_TEST_ENDPOINTS = os.getenv("ENABLE_TEST_ENDPOINTS", "True").lower() == "true"
    
    @classmethod
    def get_model_context_window(cls) -> int:
        """获取当前模型的上下文窗口大小"""
        model_config = cls.MODEL_CONFIGS.get(cls.LLM_MODEL, cls.MODEL_CONFIGS["default"])
        return model_config["context_window"]
    
    @classmethod
    def get_model_description(cls) -> str:
        """获取当前模型的描述"""
        model_config = cls.MODEL_CONFIGS.get(cls.LLM_MODEL, cls.MODEL_CONFIGS["default"])
        return model_config["description"]
    
    @classmethod
    def calculate_token_budget(cls) -> dict:
        """智能计算Token预算分配"""
        context_window = cls.get_model_context_window()
        
        # 按比例计算各部分Token数
        max_output_tokens = int(context_window * cls.LLM_OUTPUT_RATIO)
        system_prompt_tokens = int(context_window * cls.LLM_SYSTEM_RATIO)
        
        # 计算可用于输入的Token数
        available_tokens = context_window - max_output_tokens - system_prompt_tokens
        
        # 应用安全系数
        effective_budget = int(available_tokens * cls.LLM_SAFETY_FACTOR)
        
        return {
            "context_window": context_window,
            "max_output_tokens": max_output_tokens,
            "system_prompt_tokens": system_prompt_tokens,
            "available_tokens": available_tokens,
            "effective_budget": effective_budget,
            "safety_factor": cls.LLM_SAFETY_FACTOR,
            "model_description": cls.get_model_description()
        }
    
    @classmethod
    def ensure_directories(cls):
        """确保必要的目录存在"""
        directories = [
            cls.FAISS_INDEX_PATH.parent,
            cls.EMBEDDING_CACHE_PATH.parent,
            Path(cls.DETAILED_RESULTS_DIR),
            Path("./data"),
            Path("./logs")
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def validate_config(cls) -> dict:
        """验证配置的有效性"""
        errors = []
        warnings = []
        
        # 检查必要的API密钥
        if not cls.DEEPINFRA_API_KEY:
            errors.append("缺少DEEPINFRA_API_KEY配置")
        
        # 检查Reddit配置（可选）
        if not cls.REDDIT_CLIENT_ID or not cls.REDDIT_CLIENT_SECRET:
            warnings.append("Reddit API配置不完整，将使用只读模式")
        
        # 检查端口配置
        if not (1 <= cls.PORT <= 65535):
            errors.append(f"端口号无效: {cls.PORT}")
        
        # 检查模型配置
        if cls.LLM_MODEL not in cls.MODEL_CONFIGS:
            warnings.append(f"未知的模型配置: {cls.LLM_MODEL}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    @classmethod
    def get_config_summary(cls) -> dict:
        """获取配置摘要"""
        return {
            "reddit_configured": bool(cls.REDDIT_CLIENT_ID and cls.REDDIT_CLIENT_SECRET),
            "ai_service": "DeepInfra" if cls.DEEPINFRA_API_KEY else "未配置",
            "llm_model": cls.LLM_MODEL,
            "embedding_model": cls.EMBEDDING_MODEL,
            "debug_mode": cls.DEBUG,
            "server": f"{cls.HOST}:{cls.PORT}",
            "token_budget": cls.calculate_token_budget()["effective_budget"],
            "subreddit_count": len(cls.REDDIT_SUBREDDITS)
        }

# 创建全局配置实例
config = Config()

# 在模块加载时验证配置
_validation_result = config.validate_config()
if not _validation_result["valid"]:
    import logging
    logger = logging.getLogger(__name__)
    for error in _validation_result["errors"]:
        logger.error(f"配置错误: {error}")
    for warning in _validation_result["warnings"]:
        logger.warning(f"配置警告: {warning}")

# 确保目录存在
config.ensure_directories() 