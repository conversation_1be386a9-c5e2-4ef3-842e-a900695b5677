#!/usr/bin/env python3
"""
only_profile 项目的简单测试运行脚本
运行所有修正后的测试并生成总结报告
"""

import unittest
import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def run_url_parser_tests():
    """运行URL解析器测试"""
    print("=" * 60)
    print("运行 URL 解析器测试...")
    print("=" * 60)
    
    try:
        from test_only_profile_url_parser_fixed import run_url_parser_tests_fixed
        success = run_url_parser_tests_fixed()
        return success
    except Exception as e:
        print(f"URL解析器测试失败: {e}")
        return False

def run_config_tests():
    """运行配置管理测试"""
    print("=" * 60)
    print("运行配置管理测试...")
    print("=" * 60)
    
    try:
        from test_only_profile_config import run_config_tests
        success = run_config_tests()
        return success
    except Exception as e:
        print(f"配置管理测试失败: {e}")
        return False

def run_data_crawler_tests():
    """运行数据爬虫测试"""
    print("=" * 60)
    print("运行数据爬虫测试...")
    print("=" * 60)
    
    try:
        from test_only_profile_data_crawler_fixed import run_data_crawler_tests_fixed
        success = run_data_crawler_tests_fixed()
        return success
    except Exception as e:
        print(f"数据爬虫测试失败: {e}")
        return False

def run_integration_tests():
    """运行集成测试"""
    print("=" * 60)
    print("运行集成测试...")
    print("=" * 60)
    
    try:
        from test_only_profile_integration import run_integration_tests
        success = run_integration_tests()
        return success
    except Exception as e:
        print(f"集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("only_profile 项目简单测试运行器")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print("=" * 80)
    
    # 测试结果
    test_results = {}
    
    # 运行各个模块的测试
    test_modules = [
        ("URL解析器", run_url_parser_tests),
        ("配置管理", run_config_tests),
        ("数据爬虫", run_data_crawler_tests),
        ("集成测试", run_integration_tests)
    ]
    
    total_start_time = time.time()
    
    for module_name, test_func in test_modules:
        print(f"\n开始测试 {module_name}...")
        start_time = time.time()
        
        try:
            success = test_func()
            end_time = time.time()
            duration = end_time - start_time
            
            test_results[module_name] = {
                'success': success,
                'duration': duration,
                'status': '通过' if success else '失败'
            }
            
            print(f"{module_name} 测试 {'通过' if success else '失败'} (耗时: {duration:.2f}秒)")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            test_results[module_name] = {
                'success': False,
                'duration': duration,
                'status': '错误',
                'error': str(e)
            }
            
            print(f"{module_name} 测试出错: {e} (耗时: {duration:.2f}秒)")
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 生成测试报告
    print("\n" + "=" * 80)
    print("测试总结报告")
    print("=" * 80)
    
    successful_modules = 0
    failed_modules = 0
    
    for module_name, result in test_results.items():
        status = result['status']
        duration = result['duration']
        
        if result['success']:
            successful_modules += 1
            print(f"✓ {module_name}: {status} ({duration:.2f}秒)")
        else:
            failed_modules += 1
            error_msg = result.get('error', '')
            print(f"✗ {module_name}: {status} ({duration:.2f}秒)")
            if error_msg:
                print(f"  错误: {error_msg}")
    
    print("\n" + "-" * 80)
    print(f"总测试模块数: {len(test_modules)}")
    print(f"成功模块数: {successful_modules}")
    print(f"失败模块数: {failed_modules}")
    print(f"成功率: {(successful_modules / len(test_modules) * 100):.1f}%")
    print(f"总耗时: {total_duration:.2f} 秒")
    
    # 建议
    print("\n建议:")
    print("-" * 80)
    
    if failed_modules == 0:
        print("🎉 所有测试模块都通过了！项目质量良好。")
        print("建议:")
        print("1. 可以继续进行集成测试和端到端测试")
        print("2. 考虑添加性能测试和压力测试")
        print("3. 完善文档和用户指南")
    else:
        print("⚠️ 存在失败的测试模块，需要检查和修复。")
        print("建议:")
        print("1. 检查失败模块的错误信息")
        print("2. 确认项目依赖已正确安装")
        print("3. 检查项目配置是否正确")
        print("4. 确认测试环境设置正确")
    
    print("=" * 80)
    
    # 返回退出码
    return failed_modules == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 