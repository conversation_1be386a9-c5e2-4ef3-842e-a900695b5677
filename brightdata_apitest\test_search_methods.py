#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BrightData Twitter搜索计费优化测试

尝试不同方法实现搜索推文按1 record计费而不是按推文数量计费
"""

import requests
import json
import time
import urllib.parse

# API配置
API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
PROFILES_DATASET_ID = "gd_lwxmeb2u1cniijd7t4"  # Profiles Scraper
POSTS_DATASET_ID = "gd_lwxkxvnf1cynvib9co"      # Posts Scraper

class TwitterSearchOptimizer:
    """测试不同搜索方法的计费优化"""
    
    def __init__(self, api_token):
        self.api_token = api_token
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }
    
    def method_1_profile_search_hack(self, search_query):
        """方案1：用Profile scraper抓取搜索结果页面的用户"""
        print("🔍 方案1：Profile Scraper + 搜索URL")
        
        # 构造用户搜索URL
        encoded_query = urllib.parse.quote_plus(search_query)
        search_url = f"https://x.com/search?q={encoded_query}&f=user"
        
        data = [{
            "url": search_url,
            "max_number_of_posts": 10
        }]
        
        return self._trigger_collection(PROFILES_DATASET_ID, data, "Profile搜索")
    
    def method_2_batch_known_users(self, user_list):
        """方案2：批量抓取已知用户（按用户计费）"""
        print("🔍 方案2：批量用户Profile抓取")
        
        data = []
        for username in user_list:
            if not username.startswith("https://"):
                username = f"https://x.com/{username.replace('@', '')}"
            data.append({
                "url": username,
                "max_number_of_posts": 200
            })
        
        return self._trigger_collection(PROFILES_DATASET_ID, data, "批量用户")
    
    def method_3_two_step_approach(self, search_query):
        """方案3：两步法 - 先少量搜索获取用户，再批量抓取"""
        print("🔍 方案3：两步法（搜索→用户→抓取）")
        
        # 步骤1：少量搜索获取用户列表
        encoded_query = urllib.parse.quote_plus(search_query)
        search_url = f"https://x.com/search?q={encoded_query}"
        
        search_data = [{"url": search_url}]
        
        print("   步骤1：搜索少量推文获取作者...")
        result = self._trigger_collection(POSTS_DATASET_ID, search_data, "少量搜索")
        
        # 这里实际应用中需要等待结果，解析用户列表，然后进行步骤2
        print("   （步骤2需要解析结果后进行）")
        
        return result
    
    def _trigger_collection(self, dataset_id, data, method_name):
        """通用采集触发方法"""
        url = "https://api.brightdata.com/datasets/v3/trigger"
        params = {
            "dataset_id": dataset_id,
            "include_errors": "true",
        }
        
        try:
            response = requests.post(url, headers=self.headers, params=params, json=data, timeout=30)
            result = response.json()
            
            if response.status_code == 200:
                snapshot_id = result.get("snapshot_id")
                print(f"   ✅ {method_name} 成功，快照ID: {snapshot_id}")
                return {"success": True, "snapshot_id": snapshot_id, "method": method_name}
            else:
                print(f"   ❌ {method_name} 失败，状态码: {response.status_code}")
                print(f"   响应: {result}")
                return {"success": False, "error": result, "method": method_name}
                
        except Exception as e:
            print(f"   ❌ {method_name} 异常: {e}")
            return {"success": False, "error": str(e), "method": method_name}

def test_all_methods():
    """测试所有优化方法"""
    search_query = "openai gpt"
    print(f"🧪 测试搜索关键词: '{search_query}'")
    print("=" * 60)
    
    optimizer = TwitterSearchOptimizer(API_TOKEN)
    results = {}
    
    # 方案1：Profile搜索hack
    try:
        results["method_1"] = optimizer.method_1_profile_search_hack(search_query)
        time.sleep(2)
    except Exception as e:
        results["method_1"] = {"error": str(e)}
    
    print()
    
    # 方案2：批量用户（测试少量用户）
    test_users = ["elonmusk", "openai"]
    try:
        results["method_2"] = optimizer.method_2_batch_known_users(test_users)
        time.sleep(2)
    except Exception as e:
        results["method_2"] = {"error": str(e)}
    
    print()
    
    # 方案3：两步法
    try:
        results["method_3"] = optimizer.method_3_two_step_approach(search_query)
    except Exception as e:
        results["method_3"] = {"error": str(e)}
    
    print()
    print("=" * 60)
    print("🏆 测试结果总结:")
    for method, result in results.items():
        if isinstance(result, dict) and result.get("success"):
            print(f"✅ {method}: 成功 - {result.get('method', '')}")
            print(f"   快照ID: {result.get('snapshot_id')}")
        else:
            error = result.get('error', 'Unknown error') if isinstance(result, dict) else str(result)
            print(f"❌ {method}: 失败 - {error}")
    
    return results

def explain_methods():
    """解释各种方法的原理"""
    print("💡 各方法原理说明:")
    print()
    print("方案1：Profile Scraper + 搜索URL")
    print("  - 尝试用Profile数据集抓取搜索结果页面")
    print("  - 如果成功，按用户数计费而不是按推文数")
    print("  - 理论：Profile scraper可能会解析搜索页面中的用户")
    print()
    print("方案2：批量用户Profile抓取")
    print("  - 如果已知目标用户列表，直接批量抓取")
    print("  - 每个用户 = 1 record，可获取大量历史推文")
    print("  - 适合：已有用户列表或从其他渠道获取用户")
    print()
    print("方案3：两步法")
    print("  - 步骤1：少量搜索（如10条）获取活跃用户列表")
    print("  - 步骤2：用Profile scraper批量抓取这些用户")
    print("  - 优势：搜索成本低，大量数据用Profile计费")
    print()

if __name__ == "__main__":
    explain_methods()
    print()
    results = test_all_methods()
    
    # 保存测试结果
    with open("brightdata_apitest/data/search_optimization_test_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print()
    print("📊 测试结果已保存到: brightdata_apitest/data/search_optimization_test_results.json") 