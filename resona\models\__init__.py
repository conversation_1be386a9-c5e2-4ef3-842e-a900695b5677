"""
数据模型模块 - 导出所有数据模型
"""

# 图谱相关模型
from .graph_models import (
    NodeType,
    RelationType,
    GraphNode,
    GraphEdge,
    UserGraph,
    GraphElements
)

# 用户相关模型
from .user_models import (
    ParsedQuery,
    UserContentHistory,
    QualityMetrics,
    UserProfile,
    InteractionSession,
    CandidateUser
)

# 匹配相关模型
from .matching_models import (
    ResonanceScore,
    RankedCandidate,
    RecommendationSummary,
    MatchingContext,
    MatchingResult,
    MatchingMetrics,
    GraphMatchingResult
)

# 保持向后兼容 - 导入原有模型
try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from models import (
        EmotionType,
        ExpressionStyle,
        RedditPost,
        RedditComment,
        RedditUser,
        UserQuery,
        QueryProfile,
        MatchedUser,
        SystemStatus
    )
except ImportError:
    # 如果原有模型不存在，创建占位符
    from enum import Enum
    from typing import List, Optional
    from datetime import datetime
    from pydantic import BaseModel, Field
    
    class EmotionType(str, Enum):
        ANXIETY = "anxiety"
        CONFUSION = "confusion"
    
    class ExpressionStyle(str, Enum):
        SEEKING = "seeking"
    
    class RedditPost(BaseModel):
        id: str
        text: str
        timestamp: datetime
        subreddit: str
        score: int = 0
    
    class RedditComment(BaseModel):
        id: str
        text: str
        timestamp: datetime
        subreddit: str
        score: int = 0
    
    class RedditUser(BaseModel):
        username: str
        posts: List[RedditPost] = []
        comments: List[RedditComment] = []
    
    class UserQuery(BaseModel):
        text: str
    
    class QueryProfile(BaseModel):
        original_text: str
        main_topics: List[str] = []
        emotions: List[EmotionType] = []
        expression_style: ExpressionStyle = ExpressionStyle.SEEKING
        intent_summary: str = ""
        embedding_vector: Optional[List[float]] = None
    
    class MatchedUser(BaseModel):
        user: RedditUser
        similarity_score: float
        matched_posts: List[RedditPost] = []
        resonance_summary: str = ""
        resonance_tags: List[str] = []
        suggested_message: Optional[str] = None
    
    class SystemStatus(BaseModel):
        reddit_connected: bool
        deepinfra_connected: bool
        faiss_index_loaded: bool
        indexed_users_count: int

__all__ = [
    # 图谱模型
    "NodeType",
    "RelationType", 
    "GraphNode",
    "GraphEdge",
    "UserGraph",
    "GraphElements",
    
    # 用户模型
    "ParsedQuery",
    "UserContentHistory",
    "QualityMetrics",
    "UserProfile",
    "InteractionSession", 
    "CandidateUser",
    
    # 匹配模型
    "ResonanceScore",
    "RankedCandidate",
    "RecommendationSummary",
    "MatchingContext",
    "MatchingResult", 
    "MatchingMetrics",
    "GraphMatchingResult",
    
    # 向后兼容模型
    "EmotionType",
    "ExpressionStyle",
    "RedditPost",
    "RedditComment", 
    "RedditUser",
    "UserQuery",
    "QueryProfile",
    "MatchedUser",
    "SystemStatus"
] 
 