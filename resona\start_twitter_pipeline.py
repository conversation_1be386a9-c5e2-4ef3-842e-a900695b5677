"""
Twitter平台共鸣推荐流水线测试启动脚本
使用真实Twitter数据进行完整的推荐流程测试
"""
import asyncio
import logging
import os
import sys
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.pipeline import RedditResonancePipeline
from resona.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """主函数：执行Twitter平台的完整推荐流水线"""
    logger.info("🐦 启动Twitter共鸣推荐流水线测试")
    logger.info("="*60)
    
    try:
        # 创建Twitter平台的流水线
        pipeline = RedditResonancePipeline(platform="twitter")
        logger.info("✅ Twitter流水线初始化成功")
        
        # 测试用户输入
        test_prompt = """
        我是一个刚入职的程序员，每天加班到很晚，感觉工作压力很大。
        有时候觉得自己技术不够好，跟不上项目进度，担心被辞退。
        想找一些有相似经历的人聊聊，看看他们是怎么度过这个阶段的。
        """
        
        logger.info("🎯 测试场景：新人程序员工作压力问题")
        logger.info(f"用户输入: {test_prompt.strip()}")
        logger.info("")
        
        # 执行完整流水线
        start_time = datetime.now()
        results = await pipeline.execute_full_pipeline(
            user_prompt=test_prompt,
            max_recommendations=5,
            use_cache=False  # 不使用缓存，确保获取最新数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 分析结果
        if results.get('success', False):
            logger.info("🎉 Twitter流水线执行成功！")
            logger.info("="*60)
            
            # 基础信息
            logger.info(f"⏱️  执行时间: {execution_time:.2f}秒")
            logger.info(f"🆔 会话ID: {results.get('session_id', 'unknown')}")
            
            # 推荐结果
            recommendations = results.get('recommendations', [])
            logger.info(f"📋 生成推荐: {len(recommendations)}个")
            
            if recommendations:
                logger.info("\n🏆 推荐结果:")
                for i, rec in enumerate(recommendations, 1):
                    logger.info(f"\n推荐 {i}:")
                    logger.info(f"  👤 用户: @{rec.get('username', 'unknown')}")
                    logger.info(f"  🎯 匹配度: {rec.get('final_matching_score', 0):.2f}")
                    logger.info(f"  📝 推荐理由: {rec.get('recommendation_summary', 'N/A')}")
                    
                    # 显示话题启动器
                    conversation_starters = rec.get('conversation_starters', [])
                    if conversation_starters:
                        logger.info(f"  💬 话题启动器:")
                        for starter in conversation_starters[:2]:
                            logger.info(f"    • {starter}")
            
            # 流水线统计
            pipeline_stats = results.get('pipeline_stats', {})
            if pipeline_stats:
                logger.info(f"\n📊 流水线统计:")
                logger.info(f"  搜索到的推文数: {pipeline_stats.get('relevant_posts_count', 0)}")
                logger.info(f"  候选推主数: {pipeline_stats.get('candidate_users_count', 0)}")
                logger.info(f"  构建图谱数: {pipeline_stats.get('candidate_graphs_count', 0)}")
                logger.info(f"  最终推荐数: {pipeline_stats.get('final_recommendations_count', 0)}")
            
        else:
            error_msg = results.get('error', '未知错误')
            logger.error(f"❌ Twitter流水线执行失败: {error_msg}")
            logger.error(f"⏱️  执行时间: {execution_time:.2f}秒")
        
        # 关闭服务
        await pipeline.close()
        logger.info("\n🔚 测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试执行异常: {e}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main()) 