/**
 * CogBridges Reddit用户画像分析 - 前端应用
 */

class RedditProfileAnalyzer {
    constructor() {
        this.network = null;
        this.nodes = null;
        this.edges = null;
        this.currentAnalysisData = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initTooltips();
        this.loadLastResult();
        console.log('Reddit Profile Analyzer initialized');
    }
    
    bindEvents() {
        // 绑定分析按钮点击事件
        document.getElementById('analyze-btn').addEventListener('click', () => {
            this.startAnalysis();
        });
        
        // 绑定输入框回车事件
        document.getElementById('reddit-url').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.startAnalysis();
            }
        });
        
        // 绑定示例链接点击事件
        document.querySelectorAll('.example-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const url = link.getAttribute('data-url');
                document.getElementById('reddit-url').value = url;
            });
        });
        
        // 绑定图谱控制按钮
        document.getElementById('zoom-in-btn').addEventListener('click', () => {
            this.zoomIn();
        });
        
        document.getElementById('zoom-out-btn').addEventListener('click', () => {
            this.zoomOut();
        });
        
        document.getElementById('reset-view-btn').addEventListener('click', () => {
            this.resetView();
        });
    }
    
    initTooltips() {
        // 初始化Bootstrap工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    async startAnalysis() {
        const url = document.getElementById('reddit-url').value.trim();
        
        if (!url) {
            this.showError('请输入Reddit链接');
            return;
        }
        
        if (!this.isValidRedditUrl(url)) {
            this.showError('请输入有效的Reddit链接');
            return;
        }
        
        this.showStatus('正在解析Reddit链接...', 'info');
        this.hideResults();
        
        try {
            // 调用后端API进行分析
            const result = await this.callAnalysisAPI(url);
            
            if (result.success) {
                this.currentAnalysisData = result;
                this.displayResults(result);
                this.hideStatus();
            } else {
                this.showError(result.error || '分析失败，请重试');
            }
        } catch (error) {
            console.error('Analysis error:', error);
            this.showError('网络错误或服务暂时不可用');
        }
    }
    
    isValidRedditUrl(url) {
        const redditUrlPattern = /^https?:\/\/(www\.)?(reddit\.com|old\.reddit\.com|m\.reddit\.com|redd\.it)/;
        return redditUrlPattern.test(url);
    }
    
    async callAnalysisAPI(url) {
        // 模拟API调用过程
        this.updateStatus('正在解析链接...');
        await this.delay(1000);
        
        this.updateStatus('正在抓取用户数据...');
        await this.delay(2000);
        
        this.updateStatus('正在进行语义分析...');
        await this.delay(2000);
        
        this.updateStatus('正在构建人格图谱...');
        await this.delay(2000);
        
        // 实际的API调用
        try {
            const response = await axios.post('/api/analyze', {
                url: url
            }, {
                timeout: 120000,  // 2分钟超时
                onUploadProgress: (progressEvent) => {
                    // 可以显示上传进度
                }
            });
            
            return response.data;
        } catch (error) {
            if (error.code === 'ECONNABORTED') {
                throw new Error('分析超时，请重试');
            }
            throw error;
        }
    }
    
    // 模拟数据，用于开发测试
    generateMockData() {
        return {
            success: true,
            username: "example_user",
            user_profile: {
                user_id: "example_user",
                graph: {
                    nodes: {
                        "exp_1": {
                            node_id: "exp_1",
                            node_type: "EXPERIENCE",
                            content: "在科技公司工作3年的经历",
                            weight: 0.8,
                            metadata: { source: "analysis" }
                        },
                        "belief_1": {
                            node_id: "belief_1",
                            node_type: "BELIEF",
                            content: "重视工作与生活平衡",
                            weight: 0.7,
                            metadata: { source: "analysis" }
                        },
                        "emotion_1": {
                            node_id: "emotion_1",
                            node_type: "EMOTION",
                            content: "对未来发展感到焦虑",
                            weight: 0.6,
                            metadata: { source: "analysis" }
                        },
                        "topic_1": {
                            node_id: "topic_1",
                            node_type: "TOPIC",
                            content: "职业发展规划",
                            weight: 0.9,
                            metadata: { source: "analysis" }
                        }
                    },
                    edges: {
                        "edge_1": {
                            source_id: "exp_1",
                            target_id: "belief_1",
                            relation_type: "LEADS_TO",
                            weight: 0.7,
                            evidence: "工作经历塑造了价值观"
                        },
                        "edge_2": {
                            source_id: "topic_1",
                            target_id: "emotion_1",
                            relation_type: "TRIGGERS",
                            weight: 0.6,
                            evidence: "职业话题引发焦虑情绪"
                        }
                    }
                },
                completeness_score: 0.75
            },
            graph_explanation: {
                overall_portrait: "这是一个关注职业发展、注重工作生活平衡的用户。在科技行业有一定经验，但对未来发展方向存在焦虑。",
                key_traits: ["职业导向", "理性思考", "适度焦虑"],
                main_insights: ["工作经验影响价值观形成", "职业话题是主要关注点", "存在发展焦虑需要关注"],
                interaction_recommendations: ["分享职业发展经验", "提供实用建议", "理解其焦虑情绪"]
            },
            quality_metrics: {
                node_count: 4,
                edge_count: 2,
                overall_quality: "medium",
                completeness_score: 0.75
            },
            content_stats: {
                total_posts: 15,
                total_comments: 42,
                processed_texts: 20,
                subreddit_count: 8
            }
        };
    }
    
    displayResults(data) {
        // 显示结果区域
        this.showResults();
        
        // 显示用户信息
        this.displayUserInfo(data);
        
        // 显示分析摘要
        this.displayAnalysisSummary(data);
        
        // 显示质量指标
        this.displayQualityMetrics(data);
        
        // 显示图谱
        this.displayGraph(data.user_profile.graph);
        
        // 显示详细分析
        this.displayDetailedAnalysis(data.graph_explanation);
        
        // 添加淡入动画
        document.getElementById('results-row').classList.add('fade-in');
    }
    
    displayUserInfo(data) {
        document.getElementById('username-display').textContent = data.username || 'Unknown';
        document.getElementById('post-count').textContent = data.content_stats?.total_posts || 0;
        document.getElementById('comment-count').textContent = data.content_stats?.total_comments || 0;
        document.getElementById('subreddit-count').textContent = data.content_stats?.subreddit_count || 0;
        
        const confidence = Math.round((data.user_profile?.completeness_score || 0) * 100);
        document.getElementById('confidence-score').textContent = `${confidence}%`;
    }
    
    displayAnalysisSummary(data) {
        // 显示情绪标签
        const emotionContainer = document.getElementById('emotion-tags');
        emotionContainer.innerHTML = '';
        
        const emotions = data.user_profile?.graph?.nodes || {};
        Object.values(emotions).forEach(node => {
            if (node.node_type === 'EMOTION') {
                const tag = this.createTag(node.content, 'emotion');
                emotionContainer.appendChild(tag);
            }
        });
        
        // 显示话题标签
        const topicContainer = document.getElementById('topic-tags');
        topicContainer.innerHTML = '';
        
        Object.values(emotions).forEach(node => {
            if (node.node_type === 'TOPIC') {
                const tag = this.createTag(node.content, 'topic');
                topicContainer.appendChild(tag);
            }
        });
        
        // 显示性格特征标签
        const personalityContainer = document.getElementById('personality-tags');
        personalityContainer.innerHTML = '';
        
        if (data.graph_explanation?.key_traits) {
            data.graph_explanation.key_traits.forEach(trait => {
                const tag = this.createTag(trait, 'personality');
                personalityContainer.appendChild(tag);
            });
        }
    }
    
    createTag(text, type) {
        const tag = document.createElement('span');
        tag.className = `tag ${type}`;
        tag.textContent = text;
        return tag;
    }
    
    displayQualityMetrics(data) {
        const metrics = data.quality_metrics || {};
        
        document.getElementById('node-count').textContent = metrics.node_count || 0;
        document.getElementById('edge-count').textContent = metrics.edge_count || 0;
        
        const completeness = Math.round((metrics.completeness_score || 0) * 100);
        document.getElementById('completeness-score').textContent = `${completeness}%`;
        
        const qualityBadge = document.getElementById('quality-badge');
        const quality = metrics.overall_quality || 'low';
        qualityBadge.textContent = this.getQualityText(quality);
        qualityBadge.className = `badge ${this.getQualityClass(quality)}`;
    }
    
    getQualityText(quality) {
        const qualityMap = {
            'high': '高',
            'medium': '中',
            'low': '低'
        };
        return qualityMap[quality] || '未知';
    }
    
    getQualityClass(quality) {
        const classMap = {
            'high': 'bg-success',
            'medium': 'bg-warning',
            'low': 'bg-danger'
        };
        return classMap[quality] || 'bg-secondary';
    }
    
    displayGraph(graphData) {
        const container = document.getElementById('graph-container');
        
        // 隐藏加载指示器
        const loading = document.getElementById('graph-loading');
        if (loading) {
            loading.style.display = 'none';
        }
        
        // 创建网络图容器
        let networkContainer = document.getElementById('graph-network');
        if (!networkContainer) {
            networkContainer = document.createElement('div');
            networkContainer.id = 'graph-network';
            container.appendChild(networkContainer);
        }
        
        // 准备节点数据
        const nodes = new vis.DataSet();
        const edges = new vis.DataSet();
        
        // 处理节点
        if (graphData.nodes) {
            Object.values(graphData.nodes).forEach(node => {
                nodes.add({
                    id: node.node_id,
                    label: this.truncateText(node.content, 20),
                    title: node.content,  // 悬停提示
                    color: this.getNodeColor(node.node_type),
                    size: Math.max(20, node.weight * 50),
                    font: { size: 12, color: '#333' },
                    nodeType: node.node_type,
                    weight: node.weight,
                    fullContent: node.content,
                    metadata: node.metadata
                });
            });
        }
        
        // 处理边
        if (graphData.edges) {
            Object.values(graphData.edges).forEach(edge => {
                edges.add({
                    from: edge.source_id,
                    to: edge.target_id,
                    label: this.getRelationLabel(edge.relation_type),
                    color: { color: '#666', opacity: 0.6 },
                    width: Math.max(1, edge.weight * 5),
                    arrows: 'to',
                    font: { size: 10, color: '#666' },
                    relationType: edge.relation_type,
                    weight: edge.weight,
                    evidence: edge.evidence
                });
            });
        }
        
        this.nodes = nodes;
        this.edges = edges;
        
        // 网络配置
        const options = {
            nodes: {
                shape: 'dot',
                borderWidth: 2,
                borderColor: '#333',
                borderWidthSelected: 3,
                chosen: true
            },
            edges: {
                arrows: {
                    to: { enabled: true, scaleFactor: 0.5 }
                },
                smooth: {
                    enabled: true,
                    type: 'dynamic'
                }
            },
            physics: {
                stabilization: { iterations: 100 },
                barnesHut: {
                    gravitationalConstant: -2000,
                    centralGravity: 0.3,
                    springLength: 95,
                    springConstant: 0.04,
                    damping: 0.09
                }
            },
            interaction: {
                hover: true,
                selectConnectedEdges: false,
                tooltipDelay: 200
            },
            layout: {
                improvedLayout: true
            }
        };
        
        // 创建网络图
        this.network = new vis.Network(networkContainer, { nodes, edges }, options);
        
        // 绑定事件
        this.bindGraphEvents();
    }
    
    bindGraphEvents() {
        if (!this.network) return;
        
        // 节点点击事件
        this.network.on('click', (params) => {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                this.showNodeDetail(nodeId);
            }
        });
        
        // 节点悬停事件
        this.network.on('hoverNode', (params) => {
            const nodeId = params.node;
            // 可以添加悬停效果
        });
    }
    
    showNodeDetail(nodeId) {
        const nodeData = this.nodes.get(nodeId);
        if (!nodeData) return;
        
        // 填充模态框数据
        document.getElementById('node-type').textContent = this.getNodeTypeText(nodeData.nodeType);
        document.getElementById('node-weight').textContent = (nodeData.weight || 0).toFixed(2);
        document.getElementById('node-confidence').textContent = 
            ((nodeData.metadata?.confidence || 0.5) * 100).toFixed(0) + '%';
        document.getElementById('node-content').textContent = nodeData.fullContent;
        
        // 显示相关连接
        this.displayNodeConnections(nodeId);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('nodeDetailModal'));
        modal.show();
    }
    
    displayNodeConnections(nodeId) {
        const container = document.getElementById('node-connections');
        container.innerHTML = '';
        
        const connectedEdges = this.edges.get({
            filter: (edge) => edge.from === nodeId || edge.to === nodeId
        });
        
        if (connectedEdges.length === 0) {
            container.innerHTML = '<span class="text-muted">暂无连接</span>';
            return;
        }
        
        connectedEdges.forEach(edge => {
            const connectionItem = document.createElement('div');
            connectionItem.className = 'connection-item';
            
            const otherNodeId = edge.from === nodeId ? edge.to : edge.from;
            const otherNode = this.nodes.get(otherNodeId);
            const direction = edge.from === nodeId ? '→' : '←';
            
            connectionItem.innerHTML = `
                <span class="connection-type">${this.getRelationLabel(edge.relationType)}</span>
                <span>${direction} ${otherNode.label}</span>
            `;
            
            container.appendChild(connectionItem);
        });
    }
    
    getNodeColor(nodeType) {
        const colorMap = {
            'EXPERIENCE': '#FF4500',
            'BELIEF': '#0079D3',
            'EMOTION': '#EA0027',
            'TOPIC': '#46D160'
        };
        return colorMap[nodeType] || '#666';
    }
    
    getNodeTypeText(nodeType) {
        const typeMap = {
            'EXPERIENCE': '经历体验',
            'BELIEF': '信念价值观',
            'EMOTION': '情绪感受',
            'TOPIC': '话题主题'
        };
        return typeMap[nodeType] || nodeType;
    }
    
    getRelationLabel(relationType) {
        const labelMap = {
            'CAUSES': '导致',
            'INFLUENCES': '影响',
            'CONTRADICTS': '冲突',
            'SUPPORTS': '支持',
            'LEADS_TO': '促成',
            'TRIGGERS': '触发',
            'SIMILAR_TO': '相似'
        };
        return labelMap[relationType] || relationType;
    }
    
    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
    
    displayDetailedAnalysis(explanation) {
        if (!explanation) return;
        
        // 整体画像
        const portraitElement = document.getElementById('overall-portrait');
        portraitElement.textContent = explanation.overall_portrait || '暂无数据';
        
        // 关键特征
        const traitsElement = document.getElementById('key-traits');
        traitsElement.innerHTML = '';
        if (explanation.key_traits) {
            explanation.key_traits.forEach(trait => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-star me-2"></i>${trait}`;
                traitsElement.appendChild(li);
            });
        }
        
        // 核心洞察
        const insightsElement = document.getElementById('main-insights');
        insightsElement.innerHTML = '';
        if (explanation.main_insights) {
            explanation.main_insights.forEach(insight => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-lightbulb me-2"></i>${insight}`;
                insightsElement.appendChild(li);
            });
        }
        
        // 互动建议
        const recommendationsElement = document.getElementById('interaction-recommendations');
        recommendationsElement.innerHTML = '';
        if (explanation.interaction_recommendations) {
            explanation.interaction_recommendations.forEach(rec => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-comment me-2"></i>${rec}`;
                recommendationsElement.appendChild(li);
            });
        }
    }
    
    // 图谱控制方法
    zoomIn() {
        if (this.network) {
            const scale = this.network.getScale();
            this.network.moveTo({ scale: scale * 1.2 });
        }
    }
    
    zoomOut() {
        if (this.network) {
            const scale = this.network.getScale();
            this.network.moveTo({ scale: scale * 0.8 });
        }
    }
    
    resetView() {
        if (this.network) {
            this.network.fit();
        }
    }
    
    // UI状态管理
    showStatus(message, type = 'info') {
        const statusRow = document.getElementById('status-row');
        const statusAlert = document.getElementById('status-alert');
        const statusText = document.getElementById('status-text');
        
        statusAlert.className = `alert alert-${type}`;
        statusText.textContent = message;
        statusRow.style.display = 'block';
        statusRow.scrollIntoView({ behavior: 'smooth' });
    }
    
    updateStatus(message) {
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = message;
        }
    }
    
    hideStatus() {
        const statusRow = document.getElementById('status-row');
        statusRow.style.display = 'none';
    }
    
    showResults() {
        const resultsRow = document.getElementById('results-row');
        resultsRow.style.display = 'block';
        resultsRow.scrollIntoView({ behavior: 'smooth' });
    }
    
    hideResults() {
        const resultsRow = document.getElementById('results-row');
        resultsRow.style.display = 'none';
    }
    
    showError(message) {
        this.hideStatus();
        
        const errorModal = document.getElementById('errorModal');
        const errorMessage = document.getElementById('error-message');
        
        errorMessage.textContent = message;
        
        const modal = new bootstrap.Modal(errorModal);
        modal.show();
    }
    
    // 工具方法
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 从后端拉取最近一次分析结果并展示
     */
    async loadLastResult() {
        try {
            const response = await axios.get('/api/last_result', { timeout: 10000 });
            if (response.data && response.data.success) {
                this.displayResults(response.data);
                this.hideStatus();
            }
        } catch (err) {
            console.log('无历史分析结果或获取失败', err);
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.analyzer = new RedditProfileAnalyzer();
});

// 开发模式：添加测试按钮
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
        // 添加测试按钮
        const testBtn = document.createElement('button');
        testBtn.className = 'btn btn-outline-secondary btn-sm';
        testBtn.innerHTML = '<i class="fas fa-flask me-1"></i>测试数据';
        testBtn.style.position = 'fixed';
        testBtn.style.bottom = '20px';
        testBtn.style.right = '20px';
        testBtn.style.zIndex = '9999';
        
        testBtn.addEventListener('click', () => {
            if (window.analyzer) {
                const mockData = window.analyzer.generateMockData();
                window.analyzer.displayResults(mockData);
                window.analyzer.hideStatus();
                window.analyzer.showResults();
            }
        });
        
        document.body.appendChild(testBtn);
    });
} 