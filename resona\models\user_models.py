"""
用户数据模型 - 扩展支持图谱架构的用户模型
"""
from datetime import datetime
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from .graph_models import UserGraph, NodeType, RelationType


class ParsedQuery(BaseModel):
    """解析后的用户查询"""
    original_text: str = Field(..., description="原始输入文本")
    search_intent: List[str] = Field(default_factory=list, description="Reddit搜索关键词")
    values_info: Dict[str, Any] = Field(default_factory=dict, description="初始价值观信息")
    emotional_state: Dict[str, float] = Field(default_factory=dict, description="情绪状态评分")
    topics: List[str] = Field(default_factory=list, description="主要话题")
    confidence: float = Field(default=1.0, description="解析置信度")
    
    # 新增深度分析字段
    core_concerns: List[str] = Field(default_factory=list, description="核心关切点")
    decision_points: List[str] = Field(default_factory=list, description="面临的决策点")
    life_domains: List[str] = Field(default_factory=list, description="涉及的生活领域")
    support_needs: List[str] = Field(default_factory=list, description="需要的支持类型")
    
    def get_search_keywords(self) -> List[str]:
        """获取搜索关键词"""
        return self.search_intent
    
    def get_primary_emotions(self, threshold: float = 0.5) -> List[str]:
        """获取主要情绪（超过阈值的）"""
        return [emotion for emotion, score in self.emotional_state.items() 
                if score >= threshold]
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """获取分析摘要"""
        return {
            "search_keywords": self.search_intent,
            "primary_emotions": self.get_primary_emotions(),
            "main_topics": self.topics,
            "core_concerns": self.core_concerns,
            "decision_points": self.decision_points,
            "life_domains": self.life_domains,
            "support_needs": self.support_needs,
            "confidence": self.confidence
        }
    
    def has_high_emotional_intensity(self, threshold: float = 0.7) -> bool:
        """是否有高强度情绪"""
        return any(score >= threshold for score in self.emotional_state.values())
    
    def get_dominant_life_domain(self) -> Optional[str]:
        """获取主导生活领域"""
        return self.life_domains[0] if self.life_domains else None


class UserContentHistory(BaseModel):
    """用户内容历史"""
    username: str = Field(..., description="Reddit用户名")
    posts: List[str] = Field(default_factory=list, description="帖子内容列表")
    comments: List[str] = Field(default_factory=list, description="评论内容列表")
    total_content_length: int = Field(default=0, description="总内容长度")
    collection_timestamp: datetime = Field(default_factory=datetime.utcnow, description="收集时间")
    
    def get_all_content(self) -> List[str]:
        """获取所有内容"""
        return self.posts + self.comments
    
    def get_content_sample(self, max_items: int = 20) -> List[str]:
        """获取内容样本"""
        all_content = self.get_all_content()
        return all_content[:max_items]


class QualityMetrics(BaseModel):
    """内容质量评估指标"""
    content_length: int = Field(..., description="内容长度")
    engagement_score: float = Field(..., description="参与度分数（基于点赞、回复等）")
    coherence_score: float = Field(default=0.0, description="连贯性分数")
    depth_score: float = Field(default=0.0, description="深度分数")
    authenticity_score: float = Field(default=0.0, description="真实性分数")
    overall_quality: float = Field(default=0.0, description="总体质量分数")
    
    def calculate_overall_quality(self) -> float:
        """计算总体质量分数"""
        # 加权平均
        weights = {
            'engagement': 0.3,
            'coherence': 0.25,
            'depth': 0.25,
            'authenticity': 0.2
        }
        
        self.overall_quality = (
            self.engagement_score * weights['engagement'] +
            self.coherence_score * weights['coherence'] +
            self.depth_score * weights['depth'] +
            self.authenticity_score * weights['authenticity']
        )
        
        return self.overall_quality


class UserProfile(BaseModel):
    """增强的用户画像"""
    user_id: str = Field(..., description="用户标识")
    original_query: str = Field(..., description="用户原始查询")
    graph: UserGraph = Field(..., description="用户三观图谱")
    
    # 画像完整性
    completeness_score: float = Field(default=0.0, description="画像完整性分数")
    missing_dimensions: List[str] = Field(default_factory=list, description="缺失的重要维度")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    version: int = Field(default=1, description="版本号")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="附加元数据")
    
    def update_completeness(self) -> float:
        """更新完整性分数"""
        # 基于图谱节点类型的完整性评估
        node_counts = self.graph.get_node_count_by_type()
        
        # 期望的最小节点数
        expected_counts = {
            NodeType.EXPERIENCE: 2,
            NodeType.BELIEF: 2,
            NodeType.EMOTION: 1,
            NodeType.TOPIC: 1
        }
        
        # 计算完整性
        completeness_scores = []
        for node_type, expected in expected_counts.items():
            actual = node_counts.get(node_type, 0)
            score = min(actual / expected, 1.0) if expected > 0 else 1.0
            completeness_scores.append(score)
        
        self.completeness_score = sum(completeness_scores) / len(completeness_scores)
        self.updated_at = datetime.utcnow()
        
        return self.completeness_score
    
    def identify_missing_dimensions(self, reference_profiles: List['UserProfile']) -> List[str]:
        """识别缺失的重要维度"""
        # 分析参考画像中常见的节点类型和关系
        common_dimensions = set()
        
        for ref_profile in reference_profiles:
            ref_node_types = set(ref_profile.graph.get_node_count_by_type().keys())
            common_dimensions.update(ref_node_types)
        
        # 找出当前画像缺失的维度
        current_dimensions = set(self.graph.get_node_count_by_type().keys())
        missing = list(common_dimensions - current_dimensions)
        
        self.missing_dimensions = missing
        return missing
    
    def get_key_beliefs(self) -> List[str]:
        """获取关键信念"""
        belief_nodes = self.graph.get_nodes_by_type(NodeType.BELIEF)
        # 按权重排序
        sorted_beliefs = sorted(belief_nodes, key=lambda x: x.weight, reverse=True)
        return [node.content for node in sorted_beliefs[:5]]
    
    def get_emotional_patterns(self) -> Dict[str, float]:
        """获取情绪模式"""
        emotion_nodes = self.graph.get_nodes_by_type(NodeType.EMOTION)
        patterns = {}
        
        for node in emotion_nodes:
            # 简化情绪分类
            emotion_type = node.content.lower()
            if emotion_type not in patterns:
                patterns[emotion_type] = 0.0
            patterns[emotion_type] += node.weight
        
        return patterns
    
    def to_summary(self) -> Dict[str, Any]:
        """生成画像摘要"""
        return {
            "user_id": self.user_id,
            "completeness_score": self.completeness_score,
            "node_counts": self.graph.get_node_count_by_type(),
            "edge_counts": self.graph.get_edge_count_by_type(),
            "key_beliefs": self.get_key_beliefs(),
            "emotional_patterns": self.get_emotional_patterns(),
            "missing_dimensions": self.missing_dimensions,
            "version": self.version
        }


class InteractionSession(BaseModel):
    """用户交互会话"""
    session_id: str = Field(..., description="会话ID")
    user_profile: UserProfile = Field(..., description="用户画像")
    questions_asked: List[str] = Field(default_factory=list, description="已提问的问题")
    answers_received: Dict[str, str] = Field(default_factory=dict, description="收到的回答")
    current_stage: str = Field(default="initial", description="当前阶段")
    
    # 会话状态
    is_complete: bool = Field(default=False, description="是否完成")
    needs_clarification: bool = Field(default=True, description="是否需要追问")
    
    # 时间追踪
    started_at: datetime = Field(default_factory=datetime.utcnow, description="开始时间")
    last_interaction: datetime = Field(default_factory=datetime.utcnow, description="最后交互时间")
    
    def add_question(self, question: str) -> None:
        """添加问题"""
        self.questions_asked.append(question)
        self.last_interaction = datetime.utcnow()
    
    def add_answer(self, question: str, answer: str) -> None:
        """添加回答"""
        self.answers_received[question] = answer
        self.last_interaction = datetime.utcnow()
        
        # 更新用户画像
        self.user_profile.version += 1
        self.user_profile.updated_at = datetime.utcnow()
    
    def get_interaction_history(self) -> List[Dict[str, str]]:
        """获取交互历史"""
        history = []
        for question in self.questions_asked:
            answer = self.answers_received.get(question, "无回答")
            history.append({
                "question": question,
                "answer": answer
            })
        return history
    
    def mark_complete(self) -> None:
        """标记为完成"""
        self.is_complete = True
        self.needs_clarification = False
        self.last_interaction = datetime.utcnow()


class CandidateUser(BaseModel):
    """候选用户（来自Reddit）"""
    username: str = Field(..., description="Reddit用户名")
    content_history: UserContentHistory = Field(..., description="内容历史")
    quality_metrics: QualityMetrics = Field(..., description="质量评估")
    user_graph: Optional[UserGraph] = Field(default=None, description="用户图谱")
    
    # 候选状态
    graph_built: bool = Field(default=False, description="图谱是否已构建")
    last_analyzed: Optional[datetime] = Field(default=None, description="最后分析时间")
    
    def build_user_profile(self) -> UserProfile:
        """构建用户画像"""
        if not self.user_graph:
            raise ValueError("用户图谱尚未构建")
        
        profile = UserProfile(
            user_id=self.username,
            original_query="",  # 候选用户没有原始查询
            graph=self.user_graph
        )
        
        profile.update_completeness()
        return profile
    
    def is_high_quality(self, threshold: float = 0.6) -> bool:
        """判断是否为高质量用户"""
        return (self.quality_metrics.overall_quality >= threshold and
                self.content_history.total_content_length >= 500)  # 至少500字符内容 