"""
图谱服务 - 负责图谱的存储、检索和分析
"""
import json
import logging
import sqlite3
import pickle
import hashlib
from datetime import datetime
from typing import List, Dict, Optional, Set, Tuple, Any
from pathlib import Path
import networkx as nx
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ..models.graph_models import UserGraph, GraphNode, GraphEdge, NodeType, RelationType
from ..models.user_models import UserProfile

logger = logging.getLogger(__name__)


class GraphService:
    """
    图谱服务 - 提供图谱存储、检索、分析和匹配功能
    
    核心功能：
    1. 图谱持久化存储
    2. 基于相似度的图谱检索
    3. 图谱结构分析
    4. 子图匹配和相似度计算
    """
    
    def __init__(self, db_path: str = "data/graph_storage.db"):
        """初始化图谱服务"""
        self.db_path = db_path
        self.cache_max_size = 1000
        self._graph_cache: Dict[str, UserGraph] = {}
        self._similarity_cache: Dict[Tuple[str, str], float] = {}
        
        # 确保数据目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        logger.info("图谱服务初始化完成")
    
    def _init_database(self) -> None:
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建图谱存储表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_graphs (
                    user_id TEXT PRIMARY KEY,
                    graph_data BLOB NOT NULL,
                    graph_hash TEXT NOT NULL,
                    node_count INTEGER NOT NULL,
                    edge_count INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建图谱特征索引表（用于快速相似度搜索）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS graph_features (
                    user_id TEXT PRIMARY KEY,
                    experience_count INTEGER DEFAULT 0,
                    belief_count INTEGER DEFAULT 0,
                    emotion_count INTEGER DEFAULT 0,
                    topic_count INTEGER DEFAULT 0,
                    avg_node_weight REAL DEFAULT 0.0,
                    graph_density REAL DEFAULT 0.0,
                    keywords TEXT DEFAULT '',
                    FOREIGN KEY (user_id) REFERENCES user_graphs (user_id)
                )
            """)
            
            # 创建相似度缓存表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS similarity_cache (
                    graph1_hash TEXT NOT NULL,
                    graph2_hash TEXT NOT NULL,
                    similarity_score REAL NOT NULL,
                    calculation_method TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (graph1_hash, graph2_hash)
                )
            """)
            
            conn.commit()
            logger.info("数据库表结构初始化完成")
    
    def store_user_graph(self, user_id: str, graph: UserGraph) -> bool:
        """
        存储用户图谱
        
        Args:
            user_id: 用户ID
            graph: 用户图谱
            
        Returns:
            bool: 存储是否成功
        """
        try:
            # 序列化图谱
            graph_data = pickle.dumps(graph)
            graph_hash = hashlib.md5(graph_data).hexdigest()
            
            # 计算图谱特征
            features = self._extract_graph_features(graph)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 存储图谱
                cursor.execute("""
                    INSERT OR REPLACE INTO user_graphs 
                    (user_id, graph_data, graph_hash, node_count, edge_count, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    user_id, graph_data, graph_hash, 
                    len(graph.nodes), len(graph.edges),
                    datetime.now().isoformat()
                ))
                
                # 存储图谱特征
                cursor.execute("""
                    INSERT OR REPLACE INTO graph_features 
                    (user_id, experience_count, belief_count, emotion_count, topic_count,
                     avg_node_weight, graph_density, keywords)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user_id,
                    features['experience_count'],
                    features['belief_count'], 
                    features['emotion_count'],
                    features['topic_count'],
                    features['avg_node_weight'],
                    features['graph_density'],
                    json.dumps(features['keywords'])
                ))
                
                conn.commit()
            
            # 更新缓存
            self._graph_cache[user_id] = graph
            
            logger.info(f"成功存储用户 {user_id} 的图谱")
            return True
            
        except Exception as e:
            logger.error(f"存储用户图谱失败: {e}")
            return False
    
    def retrieve_user_graph(self, user_id: str) -> Optional[UserGraph]:
        """
        检索用户图谱
        
        Args:
            user_id: 用户ID
            
        Returns:
            UserGraph或None
        """
        # 先检查缓存
        if user_id in self._graph_cache:
            return self._graph_cache[user_id]
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT graph_data FROM user_graphs WHERE user_id = ?",
                    (user_id,)
                )
                result = cursor.fetchone()
                
                if result:
                    graph_data = result[0]
                    graph = pickle.loads(graph_data)
                    
                    # 更新缓存
                    if len(self._graph_cache) < self.cache_max_size:
                        self._graph_cache[user_id] = graph
                    
                    return graph
                
                return None
                
        except Exception as e:
            logger.error(f"检索用户图谱失败: {e}")
            return None
    
    def find_similar_graphs(self, query_graph: UserGraph, 
                          similarity_threshold: float = 0.3,
                          limit: int = 10) -> List[Tuple[str, float]]:
        """
        查找相似图谱
        
        Args:
            query_graph: 查询图谱
            similarity_threshold: 相似度阈值
            limit: 最大返回数量
            
        Returns:
            [(user_id, similarity_score)] 列表
        """
        try:
            # 提取查询图谱特征
            query_features = self._extract_graph_features(query_graph)
            
            # 预筛选候选图谱
            candidates = self._pre_filter_candidates(query_features, limit * 3)
            
            # 计算详细相似度
            similarities = []
            for user_id in candidates:
                candidate_graph = self.retrieve_user_graph(user_id)
                if candidate_graph:
                    similarity = self.calculate_graph_similarity(
                        query_graph, candidate_graph
                    )
                    if similarity >= similarity_threshold:
                        similarities.append((user_id, similarity))
            
            # 排序并返回
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:limit]
            
        except Exception as e:
            logger.error(f"查找相似图谱失败: {e}")
            return []
    
    def calculate_graph_similarity(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """
        计算两个图谱的相似度
        
        Args:
            graph1, graph2: 用户图谱
            
        Returns:
            相似度分数 (0-1)
        """
        try:
            # 生成图谱哈希用于缓存
            hash1 = self._get_graph_hash(graph1)
            hash2 = self._get_graph_hash(graph2)
            cache_key = (hash1, hash2) if hash1 <= hash2 else (hash2, hash1)
            
            # 检查缓存
            if cache_key in self._similarity_cache:
                return self._similarity_cache[cache_key]
            
            # 计算多维相似度
            structural_sim = self._calculate_structural_similarity(graph1, graph2)
            semantic_sim = self._calculate_semantic_similarity(graph1, graph2)
            topological_sim = self._calculate_topological_similarity(graph1, graph2)
            
            # 加权融合
            overall_similarity = (
                structural_sim * 0.4 +
                semantic_sim * 0.4 +
                topological_sim * 0.2
            )
            
            # 缓存结果
            if len(self._similarity_cache) < self.cache_max_size:
                self._similarity_cache[cache_key] = overall_similarity
            
            return overall_similarity
            
        except Exception as e:
            logger.error(f"计算图谱相似度失败: {e}")
            return 0.0
    
    def _extract_graph_features(self, graph: UserGraph) -> Dict[str, Any]:
        """提取图谱特征"""
        node_counts = graph.get_node_count_by_type()
        
        # 计算平均节点权重
        if graph.nodes:
            avg_weight = sum(node.weight for node in graph.nodes.values()) / len(graph.nodes)
        else:
            avg_weight = 0.0
        
        # 计算图谱密度
        num_nodes = len(graph.nodes)
        num_edges = len(graph.edges)
        if num_nodes > 1:
            max_edges = num_nodes * (num_nodes - 1)
            density = num_edges / max_edges if max_edges > 0 else 0.0
        else:
            density = 0.0
        
        # 提取关键词
        keywords = []
        for node in graph.nodes.values():
            # 简单的关键词提取
            words = node.content.split()
            keywords.extend([word.lower() for word in words if len(word) > 3])
        
        return {
            'experience_count': node_counts.get(NodeType.EXPERIENCE, 0),
            'belief_count': node_counts.get(NodeType.BELIEF, 0),
            'emotion_count': node_counts.get(NodeType.EMOTION, 0),
            'topic_count': node_counts.get(NodeType.TOPIC, 0),
            'avg_node_weight': avg_weight,
            'graph_density': density,
            'keywords': list(set(keywords))  # 去重
        }
    
    def _pre_filter_candidates(self, query_features: Dict[str, Any], 
                             limit: int) -> List[str]:
        """预筛选候选图谱"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 基于特征相似度的简单预筛选
                cursor.execute("""
                    SELECT user_id, 
                           ABS(experience_count - ?) + 
                           ABS(belief_count - ?) + 
                           ABS(emotion_count - ?) + 
                           ABS(topic_count - ?) as feature_distance
                    FROM graph_features 
                    ORDER BY feature_distance 
                    LIMIT ?
                """, (
                    query_features['experience_count'],
                    query_features['belief_count'],
                    query_features['emotion_count'],
                    query_features['topic_count'],
                    limit
                ))
                
                return [row[0] for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"预筛选候选图谱失败: {e}")
            return []
    
    def _calculate_structural_similarity(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """计算结构相似度"""
        # 节点类型分布相似度
        counts1 = graph1.get_node_count_by_type()
        counts2 = graph2.get_node_count_by_type()
        
        total1 = sum(counts1.values())
        total2 = sum(counts2.values())
        
        if total1 == 0 or total2 == 0:
            return 0.0
        
        # 计算分布相似度
        distribution_sim = 0.0
        for node_type in NodeType:
            ratio1 = counts1.get(node_type, 0) / total1
            ratio2 = counts2.get(node_type, 0) / total2
            distribution_sim += 1 - abs(ratio1 - ratio2)
        
        distribution_sim /= len(NodeType)
        return distribution_sim
    
    def _calculate_semantic_similarity(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """计算语义相似度"""
        # 提取所有节点内容
        contents1 = [node.content.lower() for node in graph1.nodes.values()]
        contents2 = [node.content.lower() for node in graph2.nodes.values()]
        
        if not contents1 or not contents2:
            return 0.0
        
        # 计算内容重叠度（简化版本）
        words1 = set()
        words2 = set()
        
        for content in contents1:
            words1.update(content.split())
        for content in contents2:
            words2.update(content.split())
        
        if not words1 or not words2:
            return 0.0
        
        # Jaccard相似度
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_topological_similarity(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """计算拓扑相似度"""
        try:
            # 转换为NetworkX图
            nx_graph1 = self._to_networkx(graph1)
            nx_graph2 = self._to_networkx(graph2)
            
            if len(nx_graph1.nodes) == 0 or len(nx_graph2.nodes) == 0:
                return 0.0
            
            # 计算图的拓扑特征
            density1 = nx.density(nx_graph1)
            density2 = nx.density(nx_graph2)
            
            # 计算平均聚类系数
            clustering1 = nx.average_clustering(nx_graph1)
            clustering2 = nx.average_clustering(nx_graph2)
            
            # 计算密度和聚类系数的相似度
            density_sim = 1 - abs(density1 - density2)
            clustering_sim = 1 - abs(clustering1 - clustering2)
            
            return (density_sim + clustering_sim) / 2
            
        except Exception as e:
            logger.warning(f"计算拓扑相似度失败: {e}")
            return 0.0
    
    def _to_networkx(self, graph: UserGraph) -> nx.Graph:
        """将UserGraph转换为NetworkX图"""
        nx_graph = nx.Graph()
        
        # 添加节点
        for node_id, node in graph.nodes.items():
            nx_graph.add_node(node_id, 
                             content=node.content,
                             node_type=node.node_type.value,
                             weight=node.weight)
        
        # 添加边
        for edge in graph.edges:
            nx_graph.add_edge(edge.source_id, edge.target_id,
                             relation_type=edge.relation_type.value,
                             weight=edge.weight)
        
        return nx_graph
    
    def _get_graph_hash(self, graph: UserGraph) -> str:
        """获取图谱哈希值"""
        # 创建图谱的唯一标识
        content = ""
        
        # 节点内容
        for node_id in sorted(graph.nodes.keys()):
            node = graph.nodes[node_id]
            content += f"{node_id}:{node.node_type.value}:{node.content}:{node.weight}|"
        
        # 边内容
        edges_sorted = sorted(graph.edges, key=lambda e: (e.source_id, e.target_id))
        for edge in edges_sorted:
            content += f"{edge.source_id}-{edge.target_id}:{edge.relation_type.value}:{edge.weight}|"
        
        return hashlib.md5(content.encode()).hexdigest()
    
    def calculate_subgraph_matching(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """
        计算子图匹配度
        
        Args:
            graph1, graph2: 用户图谱
            
        Returns:
            匹配度分数 (0-1)
        """
        try:
            # 转换为NetworkX图
            nx_graph1 = self._to_networkx(graph1)
            nx_graph2 = self._to_networkx(graph2)
            
            # 简化的子图匹配：计算节点和边的重叠度
            nodes1 = set(nx_graph1.nodes())
            nodes2 = set(nx_graph2.nodes())
            edges1 = set(nx_graph1.edges())
            edges2 = set(nx_graph2.edges())
            
            # 节点重叠度（基于内容相似性）
            node_matches = 0
            for n1 in nodes1:
                for n2 in nodes2:
                    if self._nodes_similar(nx_graph1.nodes[n1], nx_graph2.nodes[n2]):
                        node_matches += 1
                        break
            
            node_overlap = node_matches / max(len(nodes1), len(nodes2)) if max(len(nodes1), len(nodes2)) > 0 else 0
            
            # 边重叠度
            edge_overlap = len(edges1 & edges2) / max(len(edges1), len(edges2)) if max(len(edges1), len(edges2)) > 0 else 0
            
            return (node_overlap + edge_overlap) / 2
            
        except Exception as e:
            logger.warning(f"计算子图匹配失败: {e}")
            return 0.0
    
    def _nodes_similar(self, node1: Dict, node2: Dict, threshold: float = 0.7) -> bool:
        """判断两个节点是否相似"""
        # 类型必须相同
        if node1.get('node_type') != node2.get('node_type'):
            return False
        
        # 内容相似度
        content1 = set(node1.get('content', '').lower().split())
        content2 = set(node2.get('content', '').lower().split())
        
        if not content1 or not content2:
            return False
        
        # Jaccard相似度
        intersection = len(content1 & content2)
        union = len(content1 | content2)
        similarity = intersection / union if union > 0 else 0
        
        return similarity >= threshold
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 图谱总数
                cursor.execute("SELECT COUNT(*) FROM user_graphs")
                total_graphs = cursor.fetchone()[0]
                
                # 节点统计
                cursor.execute("""
                    SELECT 
                        AVG(experience_count) as avg_exp,
                        AVG(belief_count) as avg_belief,
                        AVG(emotion_count) as avg_emotion,
                        AVG(topic_count) as avg_topic
                    FROM graph_features
                """)
                node_stats = cursor.fetchone()
                
                # 缓存统计
                cache_stats = {
                    'graph_cache_size': len(self._graph_cache),
                    'similarity_cache_size': len(self._similarity_cache)
                }
                
                return {
                    'total_graphs': total_graphs,
                    'avg_experience_nodes': node_stats[0] or 0,
                    'avg_belief_nodes': node_stats[1] or 0,
                    'avg_emotion_nodes': node_stats[2] or 0,
                    'avg_topic_nodes': node_stats[3] or 0,
                    'cache_stats': cache_stats
                }
                
        except Exception as e:
            logger.error(f"获取存储统计失败: {e}")
            return {}
    
    def clear_cache(self) -> None:
        """清理缓存"""
        self._graph_cache.clear()
        self._similarity_cache.clear()
        logger.info("图谱服务缓存已清理")
    
    def close(self) -> None:
        """关闭图谱服务"""
        self.clear_cache()
        logger.info("图谱服务已关闭")