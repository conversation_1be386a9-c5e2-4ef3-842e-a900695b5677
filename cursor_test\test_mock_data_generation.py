#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟数据生成单元测试
专门测试SubtaskExecutor的模拟数据生成功能
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestMockDataGeneration(unittest.TestCase):
    """测试模拟数据生成功能"""
    
    def setUp(self):
        """测试前准备"""
        try:
            # 导入必要的模块
            from resona.models.user_models import ParsedQuery
            from resona.models.graph_models import UserGraph, GraphNode, NodeType
            self.ParsedQuery = ParsedQuery
            self.UserGraph = UserGraph
            self.GraphNode = GraphNode
            self.NodeType = NodeType
        except ImportError as e:
            self.skipTest(f"无法导入必要模块: {e}")
    
    def test_mock_parsed_query_creation(self):
        """测试模拟ParsedQuery的创建"""
        # 模拟创建ParsedQuery
        mock_query = self.ParsedQuery(
            original_text="模拟用户输入：关于职业发展的困扰",
            search_keywords=["career", "development", "confusion"],
            topics=["career", "professional_growth"],
            emotional_state={"anxiety": 0.6, "uncertainty": 0.8},
            values_info={"stability": 0.7, "growth": 0.9},
            confidence=0.75,
            core_concerns=["职业发展", "未来规划"],
            decision_points=["考研vs工作", "稳定vs发展"],
            life_domains=["职业", "教育"],
            support_needs=["建议", "经验分享"]
        )
        
        # 验证基本属性
        self.assertEqual(mock_query.original_text, "模拟用户输入：关于职业发展的困扰")
        self.assertIn("career", mock_query.topics)
        self.assertIn("anxiety", mock_query.emotional_state)
        self.assertEqual(mock_query.confidence, 0.75)
        
        print("✅ 模拟ParsedQuery创建测试通过")
    
    def test_mock_reddit_posts_structure(self):
        """测试模拟Reddit帖子的数据结构"""
        mock_posts = []
        
        # 生成10个模拟帖子
        for i in range(10):
            post = {
                'id': f'mock_post_{i+1}',
                'title': f'关于职业发展困惑的讨论 #{i+1}',
                'selftext': f'这是模拟帖子内容 {i+1}，讨论职业发展相关话题...',
                'subreddit': 'careeradvice' if i % 2 == 0 else 'personalfinance',
                'score': 50 + i * 10,
                'num_comments': 20 + i * 5,
                'created_utc': 1640995200 + i * 86400,  # 递增的时间戳
                'author': f'mock_author_{i+1}',
                'url': f'https://reddit.com/r/test/mock_post_{i+1}',
                'is_mock': True,
                'quality_score': 0.7 + (i % 3) * 0.1,
                'embedding_similarity': 0.8 + (i % 4) * 0.05,
                'llm_rank_score': 0.75 + (i % 5) * 0.04,
                'relevance_reason': f'与用户查询高度相关，涉及职业发展主题 {i+1}'
            }
            mock_posts.append(post)
        
        # 验证帖子结构
        self.assertEqual(len(mock_posts), 10)
        
        for i, post in enumerate(mock_posts):
            # 验证必需字段存在
            required_fields = ['id', 'title', 'selftext', 'subreddit', 'score', 'is_mock']
            for field in required_fields:
                self.assertIn(field, post, f"帖子 {i} 缺少字段 {field}")
            
            # 验证数据类型
            self.assertIsInstance(post['score'], int)
            self.assertIsInstance(post['is_mock'], bool)
            self.assertTrue(post['is_mock'])
            
            # 验证质量分数在合理范围内
            self.assertGreaterEqual(post['quality_score'], 0.0)
            self.assertLessEqual(post['quality_score'], 1.0)
        
        print("✅ 模拟Reddit帖子结构测试通过")
    
    def test_mock_candidate_users_generation(self):
        """测试模拟候选用户生成"""
        mock_users = []
        
        # 生成5个模拟用户
        for i in range(5):
            user = f'mock_user_{i+1}'
            mock_users.append(user)
        
        # 验证用户列表
        self.assertEqual(len(mock_users), 5)
        
        for i, user in enumerate(mock_users):
            self.assertIsInstance(user, str)
            self.assertTrue(user.startswith('mock_user_'))
            self.assertEqual(user, f'mock_user_{i+1}')
        
        print("✅ 模拟候选用户生成测试通过")
    
    def test_mock_user_graph_creation(self):
        """测试模拟用户图谱创建"""
        # 创建模拟用户图谱
        mock_user_graph = self.UserGraph(user_id="mock_user")
        
        # 添加一些模拟节点
        nodes = [
            self.GraphNode(
                node_id="stability_1",
                node_type=self.NodeType.BELIEF,
                content="稳定性",
                weight=0.8
            ),
            self.GraphNode(
                node_id="growth_1", 
                node_type=self.NodeType.BELIEF,
                content="成长机会",
                weight=0.7
            ),
            self.GraphNode(
                node_id="anxiety_1",
                node_type=self.NodeType.EMOTION,
                content="对未来的焦虑",
                weight=0.6
            )
        ]
        
        for node in nodes:
            mock_user_graph.add_node(node)
        
        # 验证图谱结构
        self.assertEqual(mock_user_graph.user_id, "mock_user")
        self.assertEqual(len(mock_user_graph.nodes), 3)
        
        # 验证节点类型
        belief_nodes = [n for n in mock_user_graph.nodes.values() if n.node_type == self.NodeType.BELIEF]
        emotion_nodes = [n for n in mock_user_graph.nodes.values() if n.node_type == self.NodeType.EMOTION]
        
        self.assertEqual(len(belief_nodes), 2)
        self.assertEqual(len(emotion_nodes), 1)
        
        print("✅ 模拟用户图谱创建测试通过")
    
    def test_mock_candidate_graphs_structure(self):
        """测试模拟候选图谱数据结构"""
        mock_candidate_graphs = []
        
        # 为3个用户生成模拟图谱
        for i in range(3):
            user_id = f'mock_user_{i+1}'
            
            graph_data = {
                'user_id': user_id,
                'graph': {
                    'nodes': [
                        {
                            'id': f'{user_id}_stability',
                            'type': 'value',
                            'content': '稳定性重视',
                            'weight': 0.8 - i * 0.1,
                            'confidence': 0.9 - i * 0.05
                        },
                        {
                            'id': f'{user_id}_growth',
                            'type': 'value', 
                            'content': '成长追求',
                            'weight': 0.7 + i * 0.1,
                            'confidence': 0.8 + i * 0.05
                        }
                    ],
                    'edges': [
                        {
                            'source': f'{user_id}_stability',
                            'target': f'{user_id}_growth',
                            'weight': 0.5 + i * 0.1,
                            'relationship': 'conflicts_with'
                        }
                    ]
                },
                'summary': f'用户{i+1}注重稳定但也渴望成长',
                'extraction_method': 'mock_simulation',
                'confidence_score': 0.8 - i * 0.05
            }
            
            mock_candidate_graphs.append(graph_data)
        
        # 验证图谱数据结构
        self.assertEqual(len(mock_candidate_graphs), 3)
        
        for i, graph_data in enumerate(mock_candidate_graphs):
            # 验证必需字段
            required_fields = ['user_id', 'graph', 'summary', 'confidence_score']
            for field in required_fields:
                self.assertIn(field, graph_data, f"图谱 {i} 缺少字段 {field}")
            
            # 验证图谱结构
            graph = graph_data['graph']
            self.assertIn('nodes', graph)
            self.assertIn('edges', graph)
            self.assertEqual(len(graph['nodes']), 2)
            self.assertEqual(len(graph['edges']), 1)
            
            # 验证节点结构
            for node in graph['nodes']:
                node_fields = ['id', 'type', 'content', 'weight', 'confidence']
                for field in node_fields:
                    self.assertIn(field, node, f"节点缺少字段 {field}")
                
                # 验证权重和置信度范围
                self.assertGreaterEqual(node['weight'], 0.0)
                self.assertLessEqual(node['weight'], 1.0)
                self.assertGreaterEqual(node['confidence'], 0.0)
                self.assertLessEqual(node['confidence'], 1.0)
        
        print("✅ 模拟候选图谱结构测试通过")

class TestMockDataConsistency(unittest.TestCase):
    """测试模拟数据的一致性和完整性"""
    
    def test_data_size_consistency(self):
        """测试不同模式下数据大小的一致性"""
        # 正常模式预期大小
        normal_mode_sizes = {
            'relevant_posts': 10,
            'candidate_users': 5,
            'candidate_graphs': 5
        }
        
        # 快速模式预期大小  
        fast_mode_sizes = {
            'relevant_posts': 5,
            'candidate_users': 3,
            'candidate_graphs': 3
        }
        
        # 验证大小设置合理性
        for key in normal_mode_sizes:
            self.assertGreater(normal_mode_sizes[key], fast_mode_sizes[key], 
                             f"正常模式的 {key} 应该比快速模式大")
        
        # 验证快速模式的减少幅度在合理范围内（30-70%减少）
        for key in normal_mode_sizes:
            reduction_ratio = (normal_mode_sizes[key] - fast_mode_sizes[key]) / normal_mode_sizes[key]
            self.assertGreaterEqual(reduction_ratio, 0.3, f"{key} 减少幅度应该至少30%")
            self.assertLessEqual(reduction_ratio, 0.7, f"{key} 减少幅度不应超过70%")
        
        print("✅ 数据大小一致性测试通过")
    
    def test_mock_data_field_completeness(self):
        """测试模拟数据字段完整性"""
        # 定义必需字段
        post_required_fields = [
            'id', 'title', 'selftext', 'subreddit', 'score', 'num_comments',
            'created_utc', 'author', 'url', 'is_mock', 'quality_score',
            'embedding_similarity', 'llm_rank_score', 'relevance_reason'
        ]
        
        graph_required_fields = [
            'user_id', 'graph', 'summary', 'extraction_method', 'confidence_score'
        ]
        
        # 验证字段定义
        for field in post_required_fields:
            self.assertIsInstance(field, str)
            self.assertGreater(len(field), 0)
        
        for field in graph_required_fields:
            self.assertIsInstance(field, str)
            self.assertGreater(len(field), 0)
        
        print("✅ 模拟数据字段完整性测试通过")

def run_mock_data_tests():
    """运行模拟数据测试套件"""
    print("🧪 开始运行模拟数据生成测试...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestMockDataGeneration,
        TestMockDataConsistency
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果摘要
    print("\n" + "=" * 60)
    print("🎯 模拟数据测试结果:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print("\n⚠️ 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100 if result.testsRun > 0 else 0
    print(f"\n✅ 测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_mock_data_tests()
    print(f"\n{'🎉 模拟数据测试全部通过！' if success else '❌ 模拟数据测试有失败，请检查上述输出'}")
    sys.exit(0 if success else 1) 