"""
测试用户画像构建器 (UserProfiler)
验证阶段4功能：用户画像构建与追问机制
"""
import asyncio
import sys
import os
import json
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resona.core.user_profiler import UserProfiler
from resona.core.semantic_analyzer import SemanticAnalyzer  
from resona.core.graph_builder import GraphBuilder
from resona.models.user_models import UserProfile, ParsedQuery
from resona.models.graph_models import UserGraph, NodeType, RelationType
from resona.services.ai_service import AIService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestUserProfiler:
    """用户画像构建器测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.profiler = None
        self.test_cases = [
            {
                "name": "职业困惑场景",
                "user_input": "我现在很迷茫，不知道是继续读研还是直接工作。家里希望我读研，但我觉得工作经验更重要。",
                "user_contents": [
                    "我是计算机专业的学生，成绩还不错，但对未来很焦虑",
                    "看到同学们都在准备考研，我也很纠结要不要跟风",
                    "我觉得实际工作能力比学历更重要，但父母不这么认为"
                ],
                "expected_dimensions": [NodeType.EXPERIENCE, NodeType.BELIEF, NodeType.EMOTION, NodeType.TOPIC]
            },
            {
                "name": "情感关系问题",
                "user_input": "和男朋友交往三年了，但最近总是吵架，不知道该不该分手。",
                "user_contents": [
                    "我们性格差异很大，他比较内向，我比较外向",
                    "最近因为未来规划问题经常争吵，他想回老家，我想留在大城市",
                    "虽然有问题，但我们也有很多美好的回忆"
                ],
                "expected_dimensions": [NodeType.EXPERIENCE, NodeType.EMOTION, NodeType.BELIEF]
            },
            {
                "name": "简单咨询",
                "user_input": "最近压力很大，怎么办？",
                "user_contents": None,
                "expected_dimensions": [NodeType.EMOTION]
            }
        ]
    
    async def setup(self):
        """设置测试环境"""
        logger.info("设置测试环境...")
        try:
            # 创建用户画像构建器
            self.profiler = UserProfiler()
            logger.info("用户画像构建器创建成功")
            return True
        except Exception as e:
            logger.error(f"设置测试环境失败：{e}")
            return False
    
    async def test_basic_profile_building(self):
        """测试基础画像构建功能"""
        logger.info("\n=== 测试基础画像构建功能 ===")
        
        test_case = self.test_cases[0]  # 使用职业困惑场景
        
        try:
            # 构建用户画像
            user_profile = await self.profiler.build_comprehensive_profile(
                user_input=test_case["user_input"],
                user_contents=test_case["user_contents"],
                user_context="测试场景"
            )
            
            # 验证画像基本属性
            assert user_profile.user_id is not None
            assert user_profile.original_query == test_case["user_input"]
            assert user_profile.graph is not None
            assert isinstance(user_profile.completeness_score, float)
            
            # 验证图谱结构
            node_counts = user_profile.graph.get_node_count_by_type()
            logger.info(f"节点统计：{node_counts}")
            
            # 验证至少包含期望的维度
            for expected_type in test_case["expected_dimensions"]:
                assert node_counts.get(expected_type, 0) > 0, f"缺少 {expected_type} 维度"
            
            # 输出画像摘要
            summary = user_profile.to_summary()
            logger.info(f"画像摘要：{json.dumps(summary, ensure_ascii=False, indent=2)}")
            
            logger.info("✅ 基础画像构建测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 基础画像构建测试失败：{e}")
            return False
    
    async def test_profile_completeness_evaluation(self):
        """测试画像完整性评估"""
        logger.info("\n=== 测试画像完整性评估 ===")
        
        try:
            # 测试完整度较高的场景
            complete_case = self.test_cases[0]
            complete_profile = await self.profiler.build_comprehensive_profile(
                user_input=complete_case["user_input"],
                user_contents=complete_case["user_contents"]
            )
            
            # 验证完整性评估
            is_complete, report = self.profiler.validate_profile_completeness(complete_profile)
            logger.info(f"完整画像评估结果：{is_complete}")
            logger.info(f"评估报告：{json.dumps(report, ensure_ascii=False, indent=2)}")
            
            # 测试完整度较低的场景
            incomplete_case = self.test_cases[2]
            incomplete_profile = await self.profiler.build_comprehensive_profile(
                user_input=incomplete_case["user_input"]
            )
            
            is_incomplete, incomplete_report = self.profiler.validate_profile_completeness(incomplete_profile)
            logger.info(f"不完整画像评估结果：{is_incomplete}")
            logger.info(f"不完整画像报告：{json.dumps(incomplete_report, ensure_ascii=False, indent=2)}")
            
            # 验证评估逻辑
            assert complete_profile.completeness_score >= incomplete_profile.completeness_score
            assert len(incomplete_report["suggestions"]) > 0
            
            logger.info("✅ 画像完整性评估测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 画像完整性评估测试失败：{e}")
            return False
    
    async def test_missing_dimensions_identification(self):
        """测试缺失维度识别"""
        logger.info("\n=== 测试缺失维度识别 ===")
        
        try:
            # 使用简单咨询场景（信息较少）
            test_case = self.test_cases[2]
            user_profile = await self.profiler.build_comprehensive_profile(
                user_input=test_case["user_input"]
            )
            
            # 识别缺失维度
            missing_dimensions = self.profiler.identify_missing_dimensions(user_profile)
            logger.info(f"识别到的缺失维度：{missing_dimensions}")
            
            # 验证缺失维度识别
            assert len(missing_dimensions) > 0, "应该识别到缺失维度"
            assert any("experience" in dim.lower() for dim in missing_dimensions), "应该识别到缺少经验维度"
            assert any("belief" in dim.lower() for dim in missing_dimensions), "应该识别到缺少信念维度"
            
            logger.info("✅ 缺失维度识别测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 缺失维度识别测试失败：{e}")
            return False
    
    async def test_clarifying_questions_generation(self):
        """测试追问问题生成"""
        logger.info("\n=== 测试追问问题生成 ===")
        
        try:
            # 使用信息不完整的用户画像
            test_case = self.test_cases[2]
            user_profile = await self.profiler.build_comprehensive_profile(
                user_input=test_case["user_input"]
            )
            
            # 生成追问问题
            questions = await self.profiler.generate_clarifying_questions(
                user_profile=user_profile,
                max_questions=3
            )
            
            logger.info(f"生成的追问问题：")
            for i, question in enumerate(questions, 1):
                logger.info(f"{i}. {question}")
            
            # 验证问题生成
            assert len(questions) > 0, "应该生成至少一个追问问题"
            assert len(questions) <= 3, "问题数量不应超过最大限制"
            
            # 验证问题质量
            for question in questions:
                assert len(question) > 10, "问题应该有足够的长度"
                assert "？" in question or "?" in question, "问题应该以问号结尾"
            
            logger.info("✅ 追问问题生成测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 追问问题生成测试失败：{e}")
            return False
    
    async def test_profile_update_with_answers(self):
        """测试基于回答更新画像"""
        logger.info("\n=== 测试基于回答更新画像 ===")
        
        try:
            # 创建初始画像
            test_case = self.test_cases[2]
            initial_profile = await self.profiler.build_comprehensive_profile(
                user_input=test_case["user_input"]
            )
            
            initial_score = initial_profile.completeness_score
            initial_node_count = len(initial_profile.graph.nodes)
            
            logger.info(f"初始完整性评分：{initial_score:.2f}")
            logger.info(f"初始节点数量：{initial_node_count}")
            
            # 模拟用户回答
            questions_and_answers = {
                "能否分享一个对您影响深刻的工作/学习经历？": "我在实习期间负责了一个重要项目，虽然压力很大但学到了很多",
                "什么是您最看重的人生价值？": "我认为诚信和努力最重要，做人要踏实",
                "目前您的情绪状态如何？": "除了压力大，总体还是比较积极向上的"
            }
            
            # 更新画像
            updated_profile = await self.profiler.update_profile_with_answers(
                user_profile=initial_profile,
                questions_and_answers=questions_and_answers
            )
            
            updated_score = updated_profile.completeness_score
            updated_node_count = len(updated_profile.graph.nodes)
            
            logger.info(f"更新后完整性评分：{updated_score:.2f}")
            logger.info(f"更新后节点数量：{updated_node_count}")
            
            # 验证更新效果
            assert updated_score >= initial_score, "完整性评分应该提高或保持"
            assert updated_node_count >= initial_node_count, "节点数量应该增加或保持"
            assert updated_profile.version > initial_profile.version, "版本号应该更新"
            
            logger.info("✅ 画像更新测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 画像更新测试失败：{e}")
            return False
    
    async def test_integration_with_existing_modules(self):
        """测试与现有模块的集成"""
        logger.info("\n=== 测试与现有模块的集成 ===")
        
        try:
            # 创建独立的子模块实例
            semantic_analyzer = SemanticAnalyzer()
            graph_builder = GraphBuilder()
            
            # 创建使用自定义子模块的UserProfiler
            custom_profiler = UserProfiler(
                semantic_analyzer=semantic_analyzer,
                graph_builder=graph_builder
            )
            
            # 测试集成工作流程
            test_case = self.test_cases[1]  # 使用情感关系问题
            
            # Step 1: 直接测试语义分析
            parsed_query = await semantic_analyzer.parse_user_input(test_case["user_input"])
            logger.info(f"语义分析结果：{parsed_query.search_intent}")
            
            # Step 2: 直接测试图谱构建
            user_graph = await graph_builder.build_user_graph(
                contents=test_case["user_contents"],
                parsed_query=parsed_query
            )
            logger.info(f"图谱节点数：{len(user_graph.nodes)}")
            
            # Step 3: 测试完整的画像构建流程
            complete_profile = await custom_profiler.build_comprehensive_profile(
                user_input=test_case["user_input"],
                user_contents=test_case["user_contents"]
            )
            
            # 验证集成结果
            assert complete_profile.original_query == test_case["user_input"]
            assert len(complete_profile.graph.nodes) > 0
            assert complete_profile.completeness_score > 0
            
            # 清理资源
            await semantic_analyzer.close()
            await graph_builder.close()
            await custom_profiler.close()
            
            logger.info("✅ 模块集成测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模块集成测试失败：{e}")
            return False
    
    async def test_fallback_scenarios(self):
        """测试降级场景"""
        logger.info("\n=== 测试降级场景 ===")
        
        try:
            # 测试空输入
            empty_profile = await self.profiler.build_comprehensive_profile(
                user_input="",
                user_contents=None
            )
            
            assert empty_profile is not None
            assert len(empty_profile.graph.nodes) > 0  # 应该有降级节点
            
            # 测试无意义输入
            nonsense_profile = await self.profiler.build_comprehensive_profile(
                user_input="asdlkfjaslkdfj",
                user_contents=None
            )
            
            assert nonsense_profile is not None
            assert nonsense_profile.completeness_score >= 0
            
            logger.info("✅ 降级场景测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 降级场景测试失败：{e}")
            return False
    
    async def test_performance_metrics(self):
        """测试性能指标"""
        logger.info("\n=== 测试性能指标 ===")
        
        try:
            # 测试批量画像构建
            start_time = datetime.now()
            
            profiles = []
            for test_case in self.test_cases:
                profile = await self.profiler.build_comprehensive_profile(
                    user_input=test_case["user_input"],
                    user_contents=test_case.get("user_contents")
                )
                profiles.append(profile)
            
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()
            
            logger.info(f"构建 {len(profiles)} 个画像耗时：{total_time:.2f} 秒")
            logger.info(f"平均每个画像耗时：{total_time/len(profiles):.2f} 秒")
            
            # 验证所有画像都成功创建
            for profile in profiles:
                assert profile is not None
                assert profile.completeness_score >= 0
            
            # 获取统计信息
            stats = self.profiler.get_stats()
            logger.info(f"UserProfiler统计信息：{json.dumps(stats, ensure_ascii=False, indent=2)}")
            
            logger.info("✅ 性能测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 性能测试失败：{e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("清理测试环境...")
        try:
            if self.profiler:
                await self.profiler.close()
            logger.info("测试环境清理完成")
        except Exception as e:
            logger.error(f"清理测试环境失败：{e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行UserProfiler完整测试套件")
        logger.info("=" * 60)
        
        # 设置测试环境
        if not await self.setup():
            logger.error("测试环境设置失败，退出测试")
            return False
        
        test_methods = [
            self.test_basic_profile_building,
            self.test_profile_completeness_evaluation,
            self.test_missing_dimensions_identification,
            self.test_clarifying_questions_generation,
            self.test_profile_update_with_answers,
            self.test_integration_with_existing_modules,
            self.test_fallback_scenarios,
            self.test_performance_metrics
        ]
        
        passed = 0
        failed = 0
        
        for test_method in test_methods:
            try:
                if await test_method():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                logger.error(f"测试 {test_method.__name__} 发生异常：{e}")
                failed += 1
        
        # 清理环境
        await self.cleanup()
        
        # 输出测试结果
        logger.info("=" * 60)
        logger.info(f"测试完成！通过：{passed}，失败：{failed}")
        logger.info(f"成功率：{passed/(passed+failed)*100:.1f}%" if (passed+failed) > 0 else "无测试")
        
        if failed == 0:
            logger.info("🎉 所有测试通过！UserProfiler模块实现正确！")
            return True
        else:
            logger.error(f"❌ 有 {failed} 个测试失败，需要检查实现")
            return False


async def main():
    """主函数"""
    logger.info("启动UserProfiler测试")
    
    tester = TestUserProfiler()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("UserProfiler阶段4实施测试 - 全部通过 ✅")
    else:
        logger.error("UserProfiler阶段4实施测试 - 存在问题 ❌")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())