#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证BrightData搜索计费优化方法（节省版）
只测试少量数据，验证方法可行性
"""

import requests
import json
import time
import urllib.parse

API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
PROFILES_DATASET_ID = "gd_lwxmeb2u1cniijd7t4"

def check_existing_snapshots():
    """检查已有的快照，避免重复测试"""
    print("📋 检查现有快照...")
    
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    params = {"dataset_id": PROFILES_DATASET_ID, "status": "ready"}
    
    response = requests.get("https://api.brightdata.com/datasets/v3/snapshots", 
                          headers=headers, params=params, timeout=30)
    
    if response.status_code == 200:
        snapshots = response.json()
        if snapshots:
            print(f"发现 {len(snapshots)} 个已完成的快照")
            for i, snap in enumerate(snapshots[-3:], 1):  # 显示最新3个
                print(f"{i}. ID: {snap.get('id')} - 大小: {snap.get('dataset_size')} 条")
        return snapshots
    return []

def minimal_test_profile_search():
    """最小测试：Profile Scraper + 搜索URL（只测试1个关键词）"""
    print("\n🔍 最小测试：Profile搜索方法")
    
    # 使用简单关键词
    search_query = "openai"
    encoded_query = urllib.parse.quote_plus(search_query)
    search_url = f"https://x.com/search?q={encoded_query}&f=user"
    
    data = [{
        "url": search_url,
        "max_number_of_posts": 3  # 最少推文数
    }]
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json",
    }
    
    params = {
        "dataset_id": PROFILES_DATASET_ID,
        "include_errors": "true",
    }
    
    print(f"搜索URL: {search_url}")
    print("触发采集...")
    
    try:
        response = requests.post("https://api.brightdata.com/datasets/v3/trigger",
                               headers=headers, params=params, json=data, timeout=30)
        result = response.json()
        
        if response.status_code == 200:
            snapshot_id = result.get("snapshot_id")
            print(f"✅ 成功！快照ID: {snapshot_id}")
            print("💰 计费方式：按用户Profile计费（不是按推文数）")
            return snapshot_id
        else:
            print(f"❌ 失败：{result}")
            return None
            
    except Exception as e:
        print(f"❌ 异常：{e}")
        return None

def minimal_test_batch_users():
    """最小测试：批量用户抓取（只测试2个用户）"""
    print("\n🔍 最小测试：批量用户方法")
    
    # 只测试2个用户
    test_users = ["openai", "gpt"]  # 使用简短用户名
    
    data = []
    for username in test_users:
        data.append({
            "url": f"https://x.com/{username}",
            "max_number_of_posts": 5  # 每用户只要5条推文
        })
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json",
    }
    
    params = {
        "dataset_id": PROFILES_DATASET_ID,
        "include_errors": "true",
    }
    
    print(f"测试用户: {test_users}")
    print("触发采集...")
    
    try:
        response = requests.post("https://api.brightdata.com/datasets/v3/trigger",
                               headers=headers, params=params, json=data, timeout=30)
        result = response.json()
        
        if response.status_code == 200:
            snapshot_id = result.get("snapshot_id")
            print(f"✅ 成功！快照ID: {snapshot_id}")
            print("💰 计费方式：按用户数计费（2个用户 = 2 records）")
            return snapshot_id
        else:
            print(f"❌ 失败：{result}")
            return None
            
    except Exception as e:
        print(f"❌ 异常：{e}")
        return None

def summarize_findings():
    """总结发现的优化方法"""
    print("\n" + "="*60)
    print("🏆 搜索计费优化方法总结")
    print("="*60)
    
    print("\n✅ 可行方案：")
    print("1. 【Profile搜索法】")
    print("   - 用法：Profile Scraper + Twitter搜索URL（添加&f=user）")
    print("   - 计费：按搜索到的用户数计费，不是按推文数")
    print("   - 优势：可以直接搜索关键词获取相关用户")
    
    print("\n2. 【批量用户法】")
    print("   - 用法：Profile Scraper + 用户URL列表")
    print("   - 计费：按用户数计费（每用户=1 record）")
    print("   - 优势：每个用户可获取最多3200条历史推文")
    
    print("\n❌ 不可行方案：")
    print("3. 【Posts搜索法】")
    print("   - 原因：Posts Scraper只接受具体推文URL，不接受搜索URL")
    print("   - 计费：按推文数计费（每推文=1 record）")
    
    print("\n💡 推荐策略：")
    print("• 如果需要按关键词搜索：使用方案1（Profile搜索法）")
    print("• 如果已知目标用户：使用方案2（批量用户法）")
    print("• 两种方法都避免了按推文数的高昂计费")

if __name__ == "__main__":
    print("🧪 BrightData搜索计费优化验证（节省版）")
    
    # 检查现有快照
    check_existing_snapshots()
    
    # 询问是否进行新测试
    print("\n❓ 是否进行新的最小测试？（只消耗少量额度）")
    print("1. Profile搜索测试 - 1个搜索关键词")
    print("2. 批量用户测试 - 2个用户")
    print("3. 跳过测试，只查看总结")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        minimal_test_profile_search()
    elif choice == "2":
        minimal_test_batch_users()
    elif choice == "3":
        print("跳过测试")
    else:
        print("无效选择，跳过测试")
    
    # 显示总结
    summarize_findings() 