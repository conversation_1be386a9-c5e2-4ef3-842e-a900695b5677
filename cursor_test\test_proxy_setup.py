"""
代理设置和测试脚本
帮助用户配置和测试Twitter访问代理
"""
import os
import sys
import logging
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_proxy(proxy_url: str) -> bool:
    """测试代理是否可用"""
    try:
        proxies = {'http': proxy_url, 'https': proxy_url}
        response = requests.get(
            'http://httpbin.org/ip', 
            proxies=proxies, 
            timeout=10
        )
        
        if response.status_code == 200:
            ip_info = response.json()
            logger.info(f"✅ 代理测试成功")
            logger.info(f"   代理IP: {ip_info.get('origin', 'unknown')}")
            return True
        else:
            logger.error(f"❌ 代理返回状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ProxyError:
        logger.error(f"❌ 代理连接失败: {proxy_url}")
        return False
    except requests.exceptions.Timeout:
        logger.error(f"❌ 代理超时: {proxy_url}")
        return False
    except Exception as e:
        logger.error(f"❌ 代理测试失败: {e}")
        return False

def detect_clash_proxy():
    """自动检测Clash代理"""
    logger.info("🔍 自动检测Clash代理...")
    
    clash_ports = [7890, 7891, 1080, 8080, 10809]
    
    for port in clash_ports:
        proxy_url = f"http://127.0.0.1:{port}"
        logger.info(f"测试端口 {port}...")
        
        if test_proxy(proxy_url):
            logger.info(f"🎉 发现可用代理: {proxy_url}")
            return proxy_url
    
    logger.warning("⚠️  未检测到可用的Clash代理")
    return None

def set_environment_proxy(proxy_url: str):
    """设置环境变量代理"""
    os.environ['HTTP_PROXY'] = proxy_url
    os.environ['HTTPS_PROXY'] = proxy_url
    logger.info(f"✅ 已设置环境变量代理: {proxy_url}")

def test_twitter_access(proxy_url: str = None):
    """测试Twitter网站访问"""
    logger.info("🐦 测试Twitter网站访问...")
    
    proxies = None
    if proxy_url:
        proxies = {'http': proxy_url, 'https': proxy_url}
    
    try:
        response = requests.get(
            'https://twitter.com',
            proxies=proxies,
            timeout=15,
            allow_redirects=True
        )
        
        if response.status_code in [200, 301, 302]:
            logger.info("✅ Twitter网站访问成功")
            return True
        else:
            logger.warning(f"⚠️  Twitter返回状态码: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Twitter访问失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🌐 Twitter代理设置和测试工具")
    logger.info("="*50)
    
    # 检查现有环境变量
    existing_proxy = os.getenv('HTTP_PROXY') or os.getenv('http_proxy')
    if existing_proxy:
        logger.info(f"📍 检测到现有代理环境变量: {existing_proxy}")
        if test_proxy(existing_proxy):
            logger.info("✅ 现有代理可用")
            test_twitter_access(existing_proxy)
            return
        else:
            logger.warning("⚠️  现有代理不可用，尝试自动检测")
    
    # 自动检测Clash代理
    detected_proxy = detect_clash_proxy()
    
    if detected_proxy:
        # 设置环境变量
        set_environment_proxy(detected_proxy)
        
        # 测试Twitter访问
        test_twitter_access(detected_proxy)
        
        logger.info("\n📋 代理设置完成！")
        logger.info("现在可以运行Twitter测试脚本:")
        logger.info("python cursor_test/test_twitter_real_data.py")
        
    else:
        logger.error("\n❌ 未能检测到可用代理")
        logger.error("💡 请检查以下事项:")
        logger.error("1. 确保Clash或其他代理软件正在运行")
        logger.error("2. 检查代理软件的监听端口")
        logger.error("3. 确保代理允许局域网连接")
        logger.error("4. 手动设置环境变量:")
        logger.error("   set HTTP_PROXY=http://127.0.0.1:7890")
        logger.error("   set HTTPS_PROXY=http://127.0.0.1:7890")

if __name__ == "__main__":
    main() 