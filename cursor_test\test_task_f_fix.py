#!/usr/bin/env python3
"""
子任务F修复验证测试
测试analyze_completeness方法是否能正常工作
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from resona.core.user_profiler import UserProfiler
from resona.core.semantic_analyzer import SemanticAnalyzer
from resona.core.graph_builder import GraphBuilder
from resona.services.ai_service import AIService
from resona.models.graph_models import UserGraph, GraphNode, NodeType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_task_f_fix():
    """测试子任务F的修复"""
    print("=" * 60)
    print("开始测试子任务F (analyze_completeness方法) 修复")
    print("=" * 60)
    
    try:
        # 初始化组件
        logger.info("初始化UserProfiler组件...")
        ai_service = AIService()
        semantic_analyzer = SemanticAnalyzer(ai_service)
        graph_builder = GraphBuilder(ai_service)
        user_profiler = UserProfiler(
            semantic_analyzer=semantic_analyzer,
            graph_builder=graph_builder,
            ai_service=ai_service
        )
        
        # 创建测试用的用户图谱
        logger.info("创建测试用户图谱...")
        user_graph = UserGraph(user_id="test_user")
        
        # 添加一些测试节点
        test_nodes = [
            GraphNode(
                node_id="topic_1",
                node_type=NodeType.TOPIC,
                content="职业发展困惑",
                weight=0.8
            ),
            GraphNode(
                node_id="emotion_1", 
                node_type=NodeType.EMOTION,
                content="焦虑迷茫",
                weight=0.7
            )
        ]
        
        for node in test_nodes:
            user_graph.add_node(node)
        
        logger.info(f"测试图谱创建完成：{len(user_graph.nodes)} 个节点")
        
        # 测试analyze_completeness方法
        logger.info("测试analyze_completeness方法...")
        
        start_time = datetime.now()
        completeness_result = await user_profiler.analyze_completeness(
            user_graph=user_graph,
            reference_graphs=[]  # 暂时不使用参考图谱
        )
        execution_time = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"analyze_completeness方法执行成功，耗时：{execution_time:.2f}秒")
        
        # 验证返回结果
        print("\n" + "=" * 40)
        print("完整性分析结果：")
        print("=" * 40)
        
        required_keys = [
            'completeness_score', 'is_complete', 'missing_dimensions',
            'missing_aspects', 'follow_up_questions', 'analysis_details'
        ]
        
        for key in required_keys:
            if key in completeness_result:
                value = completeness_result[key]
                print(f"✅ {key}: {value}")
            else:
                print(f"❌ 缺少必需字段: {key}")
                return False
        
        # 验证核心数据类型
        assert isinstance(completeness_result['completeness_score'], (int, float)), "completeness_score应为数值"
        assert isinstance(completeness_result['is_complete'], bool), "is_complete应为布尔值"
        assert isinstance(completeness_result['missing_dimensions'], list), "missing_dimensions应为列表"
        assert isinstance(completeness_result['follow_up_questions'], list), "follow_up_questions应为列表"
        
        print("\n🎉 子任务F修复验证测试完成！")
        print("✅ analyze_completeness方法工作正常")
        print("✅ 返回结果格式正确")
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        try:
            await user_profiler.close()
            logger.info("资源清理完成")
        except Exception as e:
            logger.warning(f"资源清理时出现警告: {e}")


async def main():
    """主函数"""
    print("子任务F修复验证测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = await test_task_f_fix()
    
    if success:
        print("\n🎉 所有测试通过！子任务F已成功修复。")
        return 0
    else:
        print("\n❌ 测试失败，需要进一步调试。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)