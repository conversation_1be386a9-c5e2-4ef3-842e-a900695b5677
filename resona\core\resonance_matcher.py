"""
共鸣匹配器 - 负责用户间的共鸣匹配和推荐生成
实现业务架构中的子任务G+H：匹配评估与推荐
"""
import json
import logging
import uuid
import math
from typing import List, Dict, Optional, Any, Tuple, Set
from datetime import datetime
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ..models.user_models import UserProfile, CandidateUser
from ..models.graph_models import (
    UserGraph, NodeType, RelationType, GraphNode, GraphEdge
)
from ..models.matching_models import (
    ResonanceScore, RankedCandidate, RecommendationSummary, 
    MatchingResult, MatchingContext, GraphMatchingResult
)
from ..services.ai_service import AIService

logger = logging.getLogger(__name__)


class ResonanceMatcher:
    """
    共鸣匹配器 - 基于图谱的用户匹配系统
    
    核心功能：
    1. 计算用户间的多维共鸣分数
    2. 对候选用户进行智能排序
    3. 生成详细的匹配理由和推荐摘要
    4. 创建个性化的互动建议
    """
    
    def __init__(self, 
                 user_profiler: Optional['UserProfiler'] = None,
                 ai_service: Optional[AIService] = None):
        """初始化共鸣匹配器"""
        self.user_profiler = user_profiler  # 延迟初始化以避免循环导入
        self.ai_service = ai_service or AIService()
        
        # 匹配参数配置
        self.default_weights = {
            'structural': 0.25,    # 图谱结构相似度
            'semantic': 0.25,      # 语义相似度
            'emotional': 0.20,     # 情绪匹配度
            'value': 0.20,         # 价值观兼容性
            'experience': 0.10     # 经验重叠度
        }
        
        # 相似度计算参数
        self.node_similarity_threshold = 0.6  # 节点相似度阈值
        self.min_resonance_score = 0.3        # 最小共鸣分数
        self.max_candidates_to_process = 50    # 最大处理候选数
        
        # 缓存机制
        self._similarity_cache: Dict[Tuple[str, str], float] = {}
        self._cache_max_size = 1000
        
        logger.info("共鸣匹配器初始化完成")
    
    async def calculate_resonance_score(self, 
                                      user_profile: UserProfile,
                                      candidate_profile: UserProfile,
                                      weights: Optional[Dict[str, float]] = None) -> ResonanceScore:
        """
        计算用户间的综合共鸣分数
        
        Args:
            user_profile: 查询用户画像
            candidate_profile: 候选用户画像
            weights: 自定义权重配置
            
        Returns:
            ResonanceScore: 详细的共鸣评分
        """
        logger.info(f"计算用户 {user_profile.user_id} 与候选用户的共鸣分数")
        
        if weights is None:
            weights = self.default_weights
        
        try:
            # 获取用户图谱
            user_graph = user_profile.graph
            candidate_graph = candidate_profile.graph
            
            # 计算各维度分数
            structural_score = await self._calculate_structural_similarity(user_graph, candidate_graph)
            semantic_score = await self._calculate_semantic_similarity(user_graph, candidate_graph)
            emotional_score = await self._calculate_emotional_alignment(user_graph, candidate_graph)
            value_score = await self._calculate_value_compatibility(user_graph, candidate_graph)
            experience_score = await self._calculate_experience_overlap(user_graph, candidate_graph)
            
            # 识别匹配元素
            matched_nodes = await self._find_matched_nodes(user_graph, candidate_graph)
            shared_themes = await self._identify_shared_themes(user_graph, candidate_graph)
            
            # 创建共鸣评分
            resonance_score = ResonanceScore(
                overall_score=0.0,  # 稍后计算
                structural_similarity=structural_score,
                semantic_similarity=semantic_score,
                emotional_alignment=emotional_score,
                value_compatibility=value_score,
                experience_overlap=experience_score,
                matched_nodes=matched_nodes,
                shared_themes=shared_themes,
                calculation_method="multi_dimensional",
                confidence=self._calculate_confidence(user_graph, candidate_graph)
            )
            
            # 计算加权总分
            overall_score = resonance_score.calculate_weighted_score(weights)
            
            logger.info(f"共鸣分数计算完成：{overall_score:.3f}")
            return resonance_score
            
        except Exception as e:
            logger.error(f"计算共鸣分数失败：{e}")
            # 返回默认低分
            return ResonanceScore(
                overall_score=0.1,
                calculation_method="fallback",
                confidence=0.1
            )
    
    async def _calculate_structural_similarity(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """计算图谱结构相似度"""
        try:
            # 节点类型分布相似度
            counts1 = graph1.get_node_count_by_type()
            counts2 = graph2.get_node_count_by_type()
            
            total1 = sum(counts1.values())
            total2 = sum(counts2.values())
            
            if total1 == 0 or total2 == 0:
                return 0.0
            
            # 计算分布相似度
            distribution_similarity = 0.0
            for node_type in NodeType:
                ratio1 = counts1.get(node_type, 0) / total1
                ratio2 = counts2.get(node_type, 0) / total2
                distribution_similarity += 1 - abs(ratio1 - ratio2)
            
            distribution_similarity /= len(NodeType)
            
            # 连接密度相似度
            density1 = len(graph1.edges) / max(len(graph1.nodes), 1)
            density2 = len(graph2.edges) / max(len(graph2.nodes), 1)
            density_similarity = 1 - abs(density1 - density2)
            
            # 加权平均
            structural_score = distribution_similarity * 0.6 + density_similarity * 0.4
            
            return min(max(structural_score, 0.0), 1.0)
            
        except Exception as e:
            logger.warning(f"结构相似度计算失败：{e}")
            return 0.0
    
    async def _calculate_semantic_similarity(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """计算语义相似度 - 使用Embedding进行深度语义匹配"""
        try:
            # 获取所有节点内容
            contents1 = [node.content for node in graph1.nodes.values()]
            contents2 = [node.content for node in graph2.nodes.values()]
            
            if not contents1 or not contents2:
                return 0.0
            
            # 检查AI服务是否可用，如果可用则使用embedding计算
            if self.ai_service:
                try:
                    # 测试AI服务连接
                    connection_ok = await self.ai_service.test_connection()
                    if connection_ok:
                        return await self._calculate_semantic_similarity_with_embedding(contents1, contents2)
                    else:
                        logger.warning("AI服务连接不可用，使用文本相似度降级策略")
                except Exception as e:
                    logger.warning(f"AI服务测试失败: {e}，使用文本相似度降级策略")
            
            # 降级策略：使用原有的文本相似度计算
            return await self._calculate_semantic_similarity_fallback(contents1, contents2)
            
        except Exception as e:
            logger.warning(f"语义相似度计算失败：{e}")
            return 0.0
    
    async def _calculate_semantic_similarity_with_embedding(self, contents1: List[str], contents2: List[str]) -> float:
        """使用Embedding计算语义相似度"""
        try:
            logger.debug("使用Embedding计算语义相似度")
            
            # 获取所有内容的embeddings
            all_contents = contents1 + contents2
            all_embeddings = await self.ai_service.batch_get_embeddings(all_contents)
            
            # 分离两个图谱的embeddings
            embeddings1 = all_embeddings[:len(contents1)]
            embeddings2 = all_embeddings[len(contents1):]
            
            # 计算两个图谱间的语义相似度矩阵
            similarity_scores = []
            
            for i, emb1 in enumerate(embeddings1):
                max_similarity = 0.0
                for j, emb2 in enumerate(embeddings2):
                    # 计算余弦相似度
                    similarity = self.ai_service._cosine_similarity(emb1, emb2)
                    max_similarity = max(max_similarity, similarity)
                similarity_scores.append(max_similarity)
            
            # 双向匹配 - 也计算从graph2到graph1的最大相似度
            reverse_similarity_scores = []
            for j, emb2 in enumerate(embeddings2):
                max_similarity = 0.0
                for i, emb1 in enumerate(embeddings1):
                    similarity = self.ai_service._cosine_similarity(emb1, emb2)
                    max_similarity = max(max_similarity, similarity)
                reverse_similarity_scores.append(max_similarity)
            
            # 计算双向平均相似度
            forward_avg = sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0.0
            reverse_avg = sum(reverse_similarity_scores) / len(reverse_similarity_scores) if reverse_similarity_scores else 0.0
            
            # 取双向相似度的平均值作为最终语义相似度
            semantic_score = (forward_avg + reverse_avg) / 2
            
            logger.debug(f"Embedding语义相似度计算完成: {semantic_score:.3f}")
            return min(max(semantic_score, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Embedding语义相似度计算失败: {e}")
            # 回退到基础文本相似度
            return await self._calculate_semantic_similarity_fallback(contents1, contents2)
    
    async def _calculate_semantic_similarity_fallback(self, contents1: List[str], contents2: List[str]) -> float:
        """降级策略：基于文本的语义相似度计算"""
        try:
            logger.debug("使用文本相似度降级策略")
            
            similarity_scores = []
            
            for content1 in contents1:
                max_similarity = 0.0
                for content2 in contents2:
                    similarity = self._calculate_text_similarity(content1, content2)
                    max_similarity = max(max_similarity, similarity)
                similarity_scores.append(max_similarity)
            
            # 平均相似度
            if similarity_scores:
                semantic_score = sum(similarity_scores) / len(similarity_scores)
                return min(max(semantic_score, 0.0), 1.0)
            
            return 0.0
            
        except Exception as e:
            logger.warning(f"降级语义相似度计算失败：{e}")
            return 0.0
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简化版本）"""
        # 缓存检查
        cache_key = (text1, text2) if text1 <= text2 else (text2, text1)
        if cache_key in self._similarity_cache:
            return self._similarity_cache[cache_key]
        
        # 简单的字符级相似度
        if not text1 or not text2:
            similarity = 0.0
        elif text1 == text2:
            similarity = 1.0
        else:
            # 计算Jaccard相似度
            set1 = set(text1.lower().split())
            set2 = set(text2.lower().split())
            
            intersection = len(set1 & set2)
            union = len(set1 | set2)
            
            similarity = intersection / union if union > 0 else 0.0
        
        # 缓存结果
        if len(self._similarity_cache) < self._cache_max_size:
            self._similarity_cache[cache_key] = similarity
        
        return similarity
    
    async def _calculate_emotional_alignment(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """计算情绪匹配度"""
        try:
            # 获取情绪节点
            emotion_nodes1 = graph1.get_nodes_by_type(NodeType.EMOTION)
            emotion_nodes2 = graph2.get_nodes_by_type(NodeType.EMOTION)
            
            # 如果没有情绪节点，尝试从其他节点提取情绪信息
            if not emotion_nodes1:
                emotion_nodes1 = self._extract_emotional_content_from_all_nodes(graph1)
            if not emotion_nodes2:
                emotion_nodes2 = self._extract_emotional_content_from_all_nodes(graph2)
            
            if not emotion_nodes1 or not emotion_nodes2:
                return 0.2  # 给一个基础分数而不是0
            
            # 提取情绪特征
            emotions1 = self._extract_emotion_features(emotion_nodes1)
            emotions2 = self._extract_emotion_features(emotion_nodes2)
            
            # 计算情绪向量相似度
            alignment_score = self._calculate_emotion_vector_similarity(emotions1, emotions2)
            
            # 确保最低分数为0.1
            alignment_score = max(alignment_score, 0.1)
            
            return min(alignment_score, 1.0)
            
        except Exception as e:
            logger.warning(f"情绪匹配度计算失败：{e}")
            return 0.1
    
    def _extract_emotional_content_from_all_nodes(self, graph: UserGraph) -> List[GraphNode]:
        """从所有节点中提取包含情绪信息的内容"""
        emotional_nodes = []
        
        # 情绪关键词
        emotion_keywords = [
            '开心', '高兴', '兴奋', '满足', '希望', '乐观', '快乐', '愉悦',
            '焦虑', '担心', '害怕', '困惑', '迷茫', '失望', '沮丧', '紧张',
            '压力', '忙碌', '平静', '冷静', '淡定', '纠结', '烦躁', '疲惫'
        ]
        
        for node in graph.nodes.values():
            content = node.content.lower()
            # 检查是否包含情绪关键词
            for keyword in emotion_keywords:
                if keyword in content:
                    # 创建虚拟情绪节点
                    emotional_nodes.append(GraphNode(
                        node_id=f"extracted_emotion_{node.node_id}",
                        node_type=NodeType.EMOTION,
                        content=node.content,
                        weight=node.weight * 0.7  # 降低权重，因为不是直接的情绪节点
                    ))
                    break
        
        return emotional_nodes
    
    def _extract_value_content_from_all_nodes(self, graph: UserGraph) -> List[GraphNode]:
        """从所有节点中提取包含价值观信息的内容"""
        value_nodes = []
        
        # 价值观关键词
        value_keywords = [
            '认为', '觉得', '相信', '坚持', '重要', '价值', '意义', '原则',
            '应该', '必须', '不应该', '反对', '支持', '赞同', '理想', '目标',
            '追求', '向往', '期望', '希望', '梦想', '信念', '观念', '想法'
        ]
        
        for node in graph.nodes.values():
            content = node.content.lower()
            # 检查是否包含价值观关键词
            for keyword in value_keywords:
                if keyword in content:
                    # 创建虚拟信念节点
                    value_nodes.append(GraphNode(
                        node_id=f"extracted_value_{node.node_id}",
                        node_type=NodeType.BELIEF,
                        content=node.content,
                        weight=node.weight * 0.8  # 降低权重，因为不是直接的信念节点
                    ))
                    break
        
        return value_nodes
    
    def _extract_emotion_features(self, emotion_nodes: List[GraphNode]) -> Dict[str, float]:
        """提取情绪特征向量"""
        emotions = {}
        
        # 情绪关键词映射
        emotion_keywords = {
            'positive': ['开心', '高兴', '兴奋', '满足', '希望', '乐观', '快乐', '愉悦'],
            'negative': ['焦虑', '担心', '害怕', '困惑', '迷茫', '失望', '沮丧'],
            'neutral': ['平静', '冷静', '淡定'],
            'stressed': ['压力', '紧张', '忙碌', '烦躁', '疲惫'],
            'confused': ['困惑', '迷茫', '纠结']
        }
        
        for node in emotion_nodes:
            content = node.content.lower()
            weight = node.weight
            
            for emotion_type, keywords in emotion_keywords.items():
                for keyword in keywords:
                    if keyword in content:
                        emotions[emotion_type] = emotions.get(emotion_type, 0.0) + weight
        
        # 如果没有找到情绪，给一个默认的中性情绪
        if not emotions:
            emotions['neutral'] = 0.5
        
        # 归一化
        total_weight = sum(emotions.values())
        if total_weight > 0:
            emotions = {k: v / total_weight for k, v in emotions.items()}
        
        return emotions
    
    def _calculate_emotion_vector_similarity(self, emotions1: Dict[str, float], 
                                           emotions2: Dict[str, float]) -> float:
        """计算情绪向量相似度"""
        if not emotions1 or not emotions2:
            return 0.0
        
        # 获取所有情绪类型
        all_emotions = set(emotions1.keys()) | set(emotions2.keys())
        
        # 构建向量
        vector1 = [emotions1.get(emotion, 0.0) for emotion in all_emotions]
        vector2 = [emotions2.get(emotion, 0.0) for emotion in all_emotions]
        
        # 计算余弦相似度
        dot_product = sum(a * b for a, b in zip(vector1, vector2))
        magnitude1 = math.sqrt(sum(a * a for a in vector1))
        magnitude2 = math.sqrt(sum(b * b for b in vector2))
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    async def _calculate_value_compatibility(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """计算价值观兼容性"""
        try:
            # 获取信念节点
            belief_nodes1 = graph1.get_nodes_by_type(NodeType.BELIEF)
            belief_nodes2 = graph2.get_nodes_by_type(NodeType.BELIEF)
            
            # 如果没有信念节点，尝试从所有节点中提取价值观相关内容
            if not belief_nodes1:
                belief_nodes1 = self._extract_value_content_from_all_nodes(graph1)
            if not belief_nodes2:
                belief_nodes2 = self._extract_value_content_from_all_nodes(graph2)
            
            if not belief_nodes1 or not belief_nodes2:
                return 0.3  # 给一个基础分数而不是0
            
            # 计算信念相似度
            compatibility_scores = []
            
            for belief1 in belief_nodes1:
                max_compatibility = 0.0
                for belief2 in belief_nodes2:
                    similarity = self._calculate_text_similarity(belief1.content, belief2.content)
                    
                    # 检查是否是冲突的价值观
                    conflict_penalty = self._detect_value_conflict(belief1.content, belief2.content)
                    adjusted_similarity = similarity * (1 - conflict_penalty)
                    
                    max_compatibility = max(max_compatibility, adjusted_similarity)
                
                compatibility_scores.append(max_compatibility)
            
            # 加权平均（按节点权重）
            total_weight = sum(node.weight for node in belief_nodes1)
            if total_weight > 0:
                weighted_score = sum(score * node.weight 
                                   for score, node in zip(compatibility_scores, belief_nodes1))
                compatibility = weighted_score / total_weight
            else:
                compatibility = sum(compatibility_scores) / len(compatibility_scores)
            
            # 确保最低分数为0.1
            compatibility = max(compatibility, 0.1)
            
            return min(compatibility, 1.0)
            
        except Exception as e:
            logger.warning(f"价值观兼容性计算失败：{e}")
            return 0.1
    
    def _detect_value_conflict(self, belief1: str, belief2: str) -> float:
        """检测价值观冲突"""
        # 简化的冲突检测
        conflict_pairs = [
            (['保守', '传统'], ['开放', '自由', '前卫']),
            (['金钱', '物质'], ['精神', '理想']),
            (['竞争', '个人'], ['合作', '集体']),
            (['工作', '事业'], ['家庭', '生活'])
        ]
        
        belief1_lower = belief1.lower()
        belief2_lower = belief2.lower()
        
        for group1, group2 in conflict_pairs:
            has_group1_in_belief1 = any(word in belief1_lower for word in group1)
            has_group2_in_belief1 = any(word in belief1_lower for word in group2)
            has_group1_in_belief2 = any(word in belief2_lower for word in group1)
            has_group2_in_belief2 = any(word in belief2_lower for word in group2)
            
            # 检查是否存在冲突
            if ((has_group1_in_belief1 and has_group2_in_belief2) or 
                (has_group2_in_belief1 and has_group1_in_belief2)):
                return 0.3  # 30%的冲突惩罚
        
        return 0.0  # 无冲突
    
    async def _calculate_experience_overlap(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """计算经验重叠度"""
        try:
            # 获取经验节点
            experience_nodes1 = graph1.get_nodes_by_type(NodeType.EXPERIENCE)
            experience_nodes2 = graph2.get_nodes_by_type(NodeType.EXPERIENCE)
            
            if not experience_nodes1 or not experience_nodes2:
                return 0.0
            
            # 计算经验相似度
            overlap_scores = []
            
            for exp1 in experience_nodes1:
                max_overlap = 0.0
                for exp2 in experience_nodes2:
                    similarity = self._calculate_text_similarity(exp1.content, exp2.content)
                    max_overlap = max(max_overlap, similarity)
                overlap_scores.append(max_overlap)
            
            # 双向检查
            reverse_scores = []
            for exp2 in experience_nodes2:
                max_overlap = 0.0
                for exp1 in experience_nodes1:
                    similarity = self._calculate_text_similarity(exp2.content, exp1.content)
                    max_overlap = max(max_overlap, similarity)
                reverse_scores.append(max_overlap)
            
            # 平均重叠度
            forward_overlap = sum(overlap_scores) / len(overlap_scores)
            reverse_overlap = sum(reverse_scores) / len(reverse_scores)
            experience_overlap = (forward_overlap + reverse_overlap) / 2
            
            return min(max(experience_overlap, 0.0), 1.0)
            
        except Exception as e:
            logger.warning(f"经验重叠度计算失败：{e}")
            return 0.0
    
    async def _find_matched_nodes(self, graph1: UserGraph, graph2: UserGraph) -> Dict[str, str]:
        """找到匹配的节点对"""
        matched_pairs = {}
        
        try:
            for node1 in graph1.nodes.values():
                best_match_id = None
                best_similarity = 0.0
                
                for node2 in graph2.nodes.values():
                    # 只匹配相同类型的节点
                    if node1.node_type == node2.node_type:
                        similarity = self._calculate_text_similarity(node1.content, node2.content)
                        
                        if similarity > best_similarity and similarity > self.node_similarity_threshold:
                            best_similarity = similarity
                            best_match_id = node2.node_id
                
                if best_match_id:
                    matched_pairs[node1.node_id] = best_match_id
            
            return matched_pairs
            
        except Exception as e:
            logger.warning(f"节点匹配失败：{e}")
            return {}
    
    async def _identify_shared_themes(self, graph1: UserGraph, graph2: UserGraph) -> List[str]:
        """识别共同主题"""
        shared_themes = []
        
        try:
            # 提取主题关键词
            themes1 = self._extract_themes_from_graph(graph1)
            themes2 = self._extract_themes_from_graph(graph2)
            
            # 找到共同主题
            for theme1 in themes1:
                for theme2 in themes2:
                    similarity = self._calculate_text_similarity(theme1, theme2)
                    if similarity > 0.7:  # 高相似度阈值
                        shared_themes.append(theme1)
                        break
            
            return list(set(shared_themes))  # 去重
            
        except Exception as e:
            logger.warning(f"共同主题识别失败：{e}")
            return []
    
    def _extract_themes_from_graph(self, graph: UserGraph) -> List[str]:
        """从图谱中提取主题"""
        themes = []
        
        # 从话题节点直接提取
        topic_nodes = graph.get_nodes_by_type(NodeType.TOPIC)
        themes.extend([node.content for node in topic_nodes])
        
        # 从其他节点提取关键词
        all_content = [node.content for node in graph.nodes.values()]
        
        # 简化的主题提取
        common_themes = [
            '工作', '学习', '生活', '感情', '家庭', '朋友', '健康', '成长',
            '梦想', '压力', '选择', '困惑', '未来', '过去', '现在'
        ]
        
        for content in all_content:
            for theme in common_themes:
                if theme in content and theme not in themes:
                    themes.append(theme)
        
        return themes
    
    def _calculate_confidence(self, graph1: UserGraph, graph2: UserGraph) -> float:
        """计算匹配置信度"""
        # 基于图谱完整性和节点数量的置信度
        total_nodes1 = len(graph1.nodes)
        total_nodes2 = len(graph2.nodes)
        
        # 节点数量充足性
        adequacy_score = min((total_nodes1 + total_nodes2) / 10, 1.0)
        
        # 图谱平衡性（各类型节点都有）
        types1 = set(node.node_type for node in graph1.nodes.values())
        types2 = set(node.node_type for node in graph2.nodes.values())
        balance_score = (len(types1) + len(types2)) / (2 * len(NodeType))
        
        # 综合置信度
        confidence = (adequacy_score * 0.6 + balance_score * 0.4)
        return min(max(confidence, 0.1), 1.0)
    
    async def rank_candidates(self, 
                            user_profile: UserProfile,
                            candidate_profiles: List[UserProfile],
                            weights: Optional[Dict[str, float]] = None,
                            max_results: int = 5) -> List[RankedCandidate]:
        """
        对候选用户进行排序
        
        Args:
            user_profile: 查询用户画像
            candidate_profiles: 候选用户画像列表
            weights: 评分权重
            max_results: 最大返回结果数
            
        Returns:
            List[RankedCandidate]: 排序后的候选用户列表
        """
        logger.info(f"开始对 {len(candidate_profiles)} 个候选用户进行排序")
        
        ranked_candidates = []
        
        # 限制处理数量
        profiles_to_process = candidate_profiles[:self.max_candidates_to_process]
        
        try:
            for candidate_profile in profiles_to_process:
                # 计算共鸣分数
                resonance_score = await self.calculate_resonance_score(
                    user_profile, candidate_profile, weights
                )
                
                # 过滤低分候选
                if resonance_score.overall_score >= self.min_resonance_score:
                    # 生成匹配理由
                    reasons = await self._generate_resonance_reasons(
                        user_profile, candidate_profile, resonance_score
                    )
                    
                    # 创建候选用户对象
                    from ..models.user_models import UserContentHistory, QualityMetrics
                    
                    # 创建默认的内容历史和质量评估
                    default_content_history = UserContentHistory(
                        username=candidate_profile.user_id,
                        posts=["示例内容"],
                        comments=["示例评论"],
                        total_content_length=100
                    )
                    
                    default_quality_metrics = QualityMetrics(
                        content_length=100,
                        engagement_score=0.5,
                        coherence_score=0.7,
                        depth_score=0.6,
                        authenticity_score=0.8,
                        overall_quality=0.65
                    )
                    
                    candidate_user = CandidateUser(
                        username=candidate_profile.user_id,
                        content_history=default_content_history,
                        quality_metrics=default_quality_metrics,
                        user_graph=candidate_profile.graph
                    )
                    
                    ranked_candidate = RankedCandidate(
                        candidate=candidate_user,
                        resonance_score=resonance_score,
                        rank=0,  # 稍后设置
                        primary_resonance_reasons=reasons
                    )
                    
                    ranked_candidates.append(ranked_candidate)
            
            # 按分数排序
            ranked_candidates.sort(
                key=lambda x: x.resonance_score.overall_score, 
                reverse=True
            )
            
            # 设置排名
            for i, candidate in enumerate(ranked_candidates[:max_results], 1):
                candidate.rank = i
            
            logger.info(f"排序完成，返回前 {min(len(ranked_candidates), max_results)} 个结果")
            return ranked_candidates[:max_results]
            
        except Exception as e:
            logger.error(f"候选用户排序失败：{e}")
            return []
    
    async def _generate_resonance_reasons(self, 
                                        user_profile: UserProfile,
                                        candidate_profile: UserProfile,
                                        resonance_score: ResonanceScore) -> List[str]:
        """生成共鸣原因"""
        reasons = []
        
        try:
            # 基于分数生成原因
            if resonance_score.structural_similarity > 0.7:
                reasons.append("思维结构相似，表达方式相近")
            
            if resonance_score.semantic_similarity > 0.6:
                reasons.append("关注话题重叠，讨论内容相关")
            
            if resonance_score.emotional_alignment > 0.6:
                reasons.append("情绪状态匹配，能够相互理解")
            
            if resonance_score.value_compatibility > 0.7:
                reasons.append("价值观念兼容，人生理念相近")
            
            if resonance_score.experience_overlap > 0.5:
                reasons.append("人生经历相似，能产生共鸣")
            
            # 基于共同主题
            if resonance_score.shared_themes:
                themes_text = "、".join(resonance_score.shared_themes[:3])
                reasons.append(f"在{themes_text}等方面有共同关注")
            
            # 确保至少有一个原因
            if not reasons:
                reasons.append("整体匹配度较好，建议进一步了解")
            
            return reasons[:5]  # 最多5个原因
            
        except Exception as e:
            logger.warning(f"生成共鸣原因失败：{e}")
            return ["匹配度良好"]
    
    async def generate_recommendation_summary(self, 
                                           user_profile: UserProfile,
                                           matched_candidate: RankedCandidate) -> RecommendationSummary:
        """
        生成推荐摘要
        
        Args:
            user_profile: 用户画像
            matched_candidate: 匹配的候选用户
            
        Returns:
            RecommendationSummary: 推荐摘要
        """
        logger.info(f"为用户 {user_profile.user_id} 生成推荐摘要")
        
        try:
            # 创建对话开场白
            conversation_starters = await self._create_conversation_starters(
                user_profile, matched_candidate
            )
            
            # 生成摘要文本
            summary_text = self._generate_summary_text(matched_candidate)
            
            # 识别关键连接点
            connection_points = self._identify_connection_points(matched_candidate.resonance_score)
            
            # 生成互动建议
            interaction_suggestions = await self._generate_interaction_suggestions(
                user_profile, matched_candidate
            )
            
            return RecommendationSummary(
                summary_text=summary_text,
                key_connection_points=connection_points,
                shared_experiences=matched_candidate.resonance_score.shared_themes,
                conversation_starters=conversation_starters,
                suggested_approach=interaction_suggestions.get('approach', ''),
                tone_recommendation=interaction_suggestions.get('tone', '')
            )
            
        except Exception as e:
            logger.error(f"生成推荐摘要失败：{e}")
            # 返回基础摘要
            return RecommendationSummary(
                summary_text="这位用户与您有一定的匹配度，建议进一步了解。",
                conversation_starters=["Hi，看到你的一些观点很有意思，想和你聊聊。"]
            )
    
    async def _create_conversation_starters(self, 
                                          user_profile: UserProfile,
                                          matched_candidate: RankedCandidate) -> List[str]:
        """创建对话开场白"""
        starters = []
        
        try:
            resonance_score = matched_candidate.resonance_score
            
            # 基于共同主题
            if resonance_score.shared_themes:
                theme = resonance_score.shared_themes[0]
                starters.append(f"看到你也关注{theme}相关的话题，我也在思考这方面的问题")
            
            # 基于价值观兼容性
            if resonance_score.value_compatibility > 0.7:
                starters.append("看到你的一些观点和想法，感觉我们在很多问题上的看法比较相近")
            
            # 基于经验重叠
            if resonance_score.experience_overlap > 0.6:
                starters.append("感觉我们可能有相似的经历，想和你交流一下想法")
            
            # 基于情绪状态
            if resonance_score.emotional_alignment > 0.6:
                starters.append("看到你分享的一些感受，很能理解你现在的状态")
            
            # 默认开场白
            if not starters:
                starters.append("Hi，看到你的一些分享很有共鸣，想和你聊聊")
            
            return starters[:3]  # 最多3个开场白
            
        except Exception as e:
            logger.warning(f"创建对话开场白失败：{e}")
            return ["Hi，想和你交流一下想法"]
    
    def _generate_summary_text(self, matched_candidate: RankedCandidate) -> str:
        """生成摘要文本"""
        score = matched_candidate.resonance_score.overall_score
        strength = matched_candidate.get_recommendation_strength()
        
        if score >= 0.8:
            return f"这位用户与您的匹配度很高（{score:.1%}），{strength}与其建立连接。你们在多个维度都表现出良好的兼容性。"
        elif score >= 0.6:
            return f"这位用户与您有不错的匹配度（{score:.1%}），{strength}进一步了解。你们有一些共同点值得探讨。"
        elif score >= 0.4:
            return f"这位用户与您有一定的匹配度（{score:.1%}），可以{strength}接触。虽然差异存在，但也许能带来新的视角。"
        else:
            return f"这位用户与您的匹配度较低（{score:.1%}），建议{strength}谨慎考虑是否建立连接。"
    
    def _identify_connection_points(self, resonance_score: ResonanceScore) -> List[str]:
        """识别关键连接点"""
        connection_points = []
        
        # 基于各维度分数
        if resonance_score.value_compatibility > 0.6:
            connection_points.append("价值观念相近")
        
        if resonance_score.experience_overlap > 0.5:
            connection_points.append("人生经历相似")
        
        if resonance_score.emotional_alignment > 0.6:
            connection_points.append("情绪状态理解")
        
        if resonance_score.semantic_similarity > 0.6:
            connection_points.append("话题兴趣重叠")
        
        # 基于匹配节点
        if len(resonance_score.matched_nodes) > 2:
            connection_points.append("思维模式相近")
        
        return connection_points
    
    async def _generate_interaction_suggestions(self, 
                                              user_profile: UserProfile,
                                              matched_candidate: RankedCandidate) -> Dict[str, str]:
        """生成互动建议"""
        suggestions = {}
        
        score = matched_candidate.resonance_score.overall_score
        
        # 建议的交流方式
        if score >= 0.8:
            suggestions['approach'] = "可以直接表达共鸣，分享相似经历"
            suggestions['tone'] = "真诚友好，可以稍微深入一些"
        elif score >= 0.6:
            suggestions['approach'] = "从共同话题开始，逐步建立信任"
            suggestions['tone'] = "友好而谨慎，先建立基础连接"
        else:
            suggestions['approach'] = "保持礼貌距离，观察对方反应"
            suggestions['tone'] = "礼貌客观，避免过于个人化"
        
        return suggestions
    
    async def close(self) -> None:
        """关闭共鸣匹配器"""
        if self.user_profiler is not None:
            await self.user_profiler.close()
        if self.ai_service:
            await self.ai_service.close()
        logger.info("共鸣匹配器已关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "similarity_cache_size": len(self._similarity_cache),
            "max_cache_size": self._cache_max_size,
            "node_similarity_threshold": self.node_similarity_threshold,
            "min_resonance_score": self.min_resonance_score,
            "default_weights": self.default_weights
        }