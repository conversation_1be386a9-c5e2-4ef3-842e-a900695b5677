#!/usr/bin/env python3
"""
only_profile 项目的综合测试运行脚本
运行所有单元测试和集成测试，生成详细的测试报告
"""

import unittest
import sys
import os
import time
import json
import subprocess
from datetime import datetime
from typing import Dict, List, Any
import traceback

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def setup_test_environment():
    """设置测试环境"""
    print("=" * 80)
    print("设置测试环境...")
    print("=" * 80)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查项目路径
    project_path = os.path.join(os.path.dirname(__file__), '..', 'only_profile')
    print(f"项目路径: {project_path}")
    print(f"项目路径存在: {os.path.exists(project_path)}")
    
    # 检查虚拟环境
    virtual_env = os.environ.get('VIRTUAL_ENV')
    if virtual_env:
        print(f"虚拟环境: {virtual_env}")
    else:
        print("警告: 未检测到虚拟环境")
    
    # 检查依赖包
    required_packages = ['unittest', 'asyncio', 'json', 'tempfile']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print(f"警告: 缺少依赖包: {', '.join(missing_packages)}")
    
    print("=" * 80)
    return len(missing_packages) == 0

def run_single_test_file(test_file: str) -> Dict[str, Any]:
    """运行单个测试文件"""
    print(f"\n运行测试文件: {test_file}")
    print("-" * 60)
    
    start_time = time.time()
    
    try:
        # 动态导入测试模块
        module_name = os.path.splitext(test_file)[0]
        
        if module_name.endswith('_url_parser'):
            from test_only_profile_url_parser import run_url_parser_tests
            success = run_url_parser_tests()
        elif module_name.endswith('_data_crawler'):
            from test_only_profile_data_crawler import run_data_crawler_tests
            success = run_data_crawler_tests()
        elif module_name.endswith('_config'):
            from test_only_profile_config import run_config_tests
            success = run_config_tests()
        elif module_name.endswith('_integration'):
            from test_only_profile_integration import run_integration_tests
            success = run_integration_tests()
        else:
            print(f"未知的测试文件: {test_file}")
            success = False
        
        end_time = time.time()
        duration = end_time - start_time
        
        result = {
            'test_file': test_file,
            'success': success,
            'duration': duration,
            'start_time': start_time,
            'end_time': end_time,
            'error': None
        }
        
        print(f"测试 {test_file} {'成功' if success else '失败'} (耗时: {duration:.2f}秒)")
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        result = {
            'test_file': test_file,
            'success': False,
            'duration': duration,
            'start_time': start_time,
            'end_time': end_time,
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        
        print(f"测试 {test_file} 出错: {e}")
        print(f"错误详情: {traceback.format_exc()}")
    
    return result

def run_all_tests() -> Dict[str, Any]:
    """运行所有测试"""
    print("=" * 80)
    print("开始运行 only_profile 项目的所有测试")
    print("=" * 80)
    
    # 测试文件列表
    test_files = [
        'test_only_profile_url_parser.py',
        'test_only_profile_data_crawler.py',
        'test_only_profile_config.py',
        'test_only_profile_integration.py'
    ]
    
    # 检查测试文件是否存在
    existing_test_files = []
    for test_file in test_files:
        test_path = os.path.join(os.path.dirname(__file__), test_file)
        if os.path.exists(test_path):
            existing_test_files.append(test_file)
            print(f"✓ 找到测试文件: {test_file}")
        else:
            print(f"✗ 缺少测试文件: {test_file}")
    
    if not existing_test_files:
        print("错误: 没有找到任何测试文件")
        return {
            'success': False,
            'error': 'No test files found',
            'test_results': []
        }
    
    # 运行测试
    test_results = []
    total_start_time = time.time()
    
    for test_file in existing_test_files:
        try:
            result = run_single_test_file(test_file)
            test_results.append(result)
        except Exception as e:
            print(f"运行测试文件 {test_file} 时出现意外错误: {e}")
            test_results.append({
                'test_file': test_file,
                'success': False,
                'duration': 0,
                'error': str(e),
                'traceback': traceback.format_exc()
            })
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 统计结果
    total_tests = len(test_results)
    successful_tests = sum(1 for r in test_results if r['success'])
    failed_tests = total_tests - successful_tests
    
    overall_result = {
        'success': failed_tests == 0,
        'total_tests': total_tests,
        'successful_tests': successful_tests,
        'failed_tests': failed_tests,
        'total_duration': total_duration,
        'start_time': total_start_time,
        'end_time': total_end_time,
        'test_results': test_results
    }
    
    return overall_result

def generate_test_report(results: Dict[str, Any]) -> str:
    """生成测试报告"""
    print("\n" + "=" * 80)
    print("生成测试报告...")
    print("=" * 80)
    
    # 基本信息
    report = []
    report.append("only_profile 项目测试报告")
    report.append("=" * 80)
    report.append(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"Python版本: {sys.version}")
    report.append(f"测试环境: {os.environ.get('VIRTUAL_ENV', '未使用虚拟环境')}")
    report.append("")
    
    # 总体结果
    report.append("总体结果:")
    report.append(f"  总测试数: {results['total_tests']}")
    report.append(f"  成功测试: {results['successful_tests']}")
    report.append(f"  失败测试: {results['failed_tests']}")
    report.append(f"  成功率: {(results['successful_tests'] / results['total_tests'] * 100):.1f}%")
    report.append(f"  总耗时: {results['total_duration']:.2f} 秒")
    report.append("")
    
    # 详细结果
    report.append("详细结果:")
    report.append("-" * 60)
    
    for test_result in results['test_results']:
        status = "✓ 成功" if test_result['success'] else "✗ 失败"
        report.append(f"{test_result['test_file']}: {status} ({test_result['duration']:.2f}秒)")
        
        if not test_result['success'] and test_result.get('error'):
            report.append(f"  错误: {test_result['error']}")
            if test_result.get('traceback'):
                report.append(f"  详细错误信息:")
                for line in test_result['traceback'].split('\n'):
                    if line.strip():
                        report.append(f"    {line}")
        report.append("")
    
    # 建议
    report.append("建议:")
    report.append("-" * 60)
    
    if results['failed_tests'] == 0:
        report.append("✓ 所有测试都通过了！项目质量良好。")
    else:
        report.append("✗ 存在失败的测试，需要检查和修复。")
        report.append("  建议:")
        report.append("  1. 检查失败测试的错误信息")
        report.append("  2. 确认项目依赖已正确安装")
        report.append("  3. 检查项目配置是否正确")
        report.append("  4. 确认测试环境设置正确")
    
    report.append("")
    report.append("=" * 80)
    
    report_content = "\n".join(report)
    
    # 保存报告到文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"only_profile_test_report_{timestamp}.txt"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        print(f"测试报告已保存到: {report_file}")
    except Exception as e:
        print(f"保存测试报告时出错: {e}")
    
    return report_content

def save_test_results(results: Dict[str, Any]) -> str:
    """保存测试结果到JSON文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"only_profile_test_results_{timestamp}.json"
    
    try:
        # 处理不能序列化的对象
        serializable_results = {
            'success': results['success'],
            'total_tests': results['total_tests'],
            'successful_tests': results['successful_tests'],
            'failed_tests': results['failed_tests'],
            'total_duration': results['total_duration'],
            'start_time': datetime.fromtimestamp(results['start_time']).isoformat(),
            'end_time': datetime.fromtimestamp(results['end_time']).isoformat(),
            'test_results': []
        }
        
        for test_result in results['test_results']:
            serializable_test = {
                'test_file': test_result['test_file'],
                'success': test_result['success'],
                'duration': test_result['duration'],
                'start_time': datetime.fromtimestamp(test_result['start_time']).isoformat(),
                'end_time': datetime.fromtimestamp(test_result['end_time']).isoformat(),
                'error': test_result.get('error'),
                'traceback': test_result.get('traceback')
            }
            serializable_results['test_results'].append(serializable_test)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"测试结果已保存到: {results_file}")
        return results_file
        
    except Exception as e:
        print(f"保存测试结果时出错: {e}")
        return None

def main():
    """主函数"""
    print("only_profile 项目单元测试启动器")
    print("=" * 80)
    
    # 设置测试环境
    env_ready = setup_test_environment()
    if not env_ready:
        print("测试环境设置不完整，但继续运行测试...")
    
    # 运行所有测试
    results = run_all_tests()
    
    # 生成测试报告
    report = generate_test_report(results)
    print(report)
    
    # 保存测试结果
    save_test_results(results)
    
    # 返回退出码
    if results['success']:
        print("\n🎉 所有测试通过！")
        return 0
    else:
        print(f"\n❌ 有 {results['failed_tests']} 个测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 