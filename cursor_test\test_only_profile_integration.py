#!/usr/bin/env python3
"""
only_profile 项目的集成测试
测试完整的工作流程：URL解析 -> 数据爬取 -> 语义分析 -> 图谱构建 -> 前端展示
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock, AsyncMock
import asyncio
import json
import tempfile
import shutil
from datetime import datetime
from typing import Dict, Any, List

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 模拟数据结构
class MockRedditLinkInfo:
    def __init__(self, link_type, username=None, subreddit=None, post_id=None, comment_id=None):
        self.link_type = link_type
        self.username = username
        self.subreddit = subreddit
        self.post_id = post_id
        self.comment_id = comment_id

class MockPost:
    def __init__(self, id, title, body, author, subreddit, created_utc, score=10):
        self.id = id
        self.title = title
        self.body = body
        self.author = author
        self.subreddit = subreddit
        self.created_utc = created_utc
        self.score = score

class MockComment:
    def __init__(self, id, body, author, subreddit, created_utc, score=5):
        self.id = id
        self.body = body
        self.author = author
        self.subreddit = subreddit
        self.created_utc = created_utc
        self.score = score

# 模拟项目模块
class MockRedditUrlParser:
    def parse_url(self, url):
        if '/u/' in url or '/user/' in url:
            username = url.split('/')[-1]
            return MockRedditLinkInfo('user', username=username)
        elif '/comments/' in url:
            parts = url.split('/')
            subreddit = parts[parts.index('r') + 1]
            post_id = parts[parts.index('comments') + 1]
            return MockRedditLinkInfo('post', subreddit=subreddit, post_id=post_id)
        return MockRedditLinkInfo('unknown')

class MockDataCrawler:
    def __init__(self, config=None):
        self.config = config or {}
    
    async def crawl_from_link(self, link_info):
        if link_info.link_type == 'user':
            return {
                'username': link_info.username,
                'posts': [MockPost(
                    id=f"post_{i}",
                    title=f"Post {i}",
                    body=f"Content of post {i}",
                    author=link_info.username,
                    subreddit=f"sub_{i}",
                    created_utc=datetime.now().timestamp()
                ) for i in range(3)],
                'comments': [MockComment(
                    id=f"comment_{i}",
                    body=f"Comment {i}",
                    author=link_info.username,
                    subreddit=f"sub_{i}",
                    created_utc=datetime.now().timestamp()
                ) for i in range(5)],
                'crawl_time': datetime.now().isoformat()
            }
        return {}

class MockSemanticAnalyzer:
    def __init__(self, config=None):
        self.config = config or {}
    
    async def analyze_user_profile(self, crawl_data):
        return {
            'personality_traits': {
                'openness': 0.8,
                'conscientiousness': 0.7,
                'extraversion': 0.6,
                'agreeableness': 0.9,
                'neuroticism': 0.3
            },
            'interests': ['technology', 'science', 'programming'],
            'communication_style': 'analytical',
            'emotional_patterns': {
                'positive_ratio': 0.7,
                'negative_ratio': 0.2,
                'neutral_ratio': 0.1
            },
            'analysis_time': datetime.now().isoformat()
        }

class MockGraphBuilder:
    def __init__(self, config=None):
        self.config = config or {}
    
    async def build_personality_graph(self, analysis_data):
        return {
            'nodes': [
                {'id': 'openness', 'label': 'Openness', 'value': 0.8, 'category': 'personality'},
                {'id': 'conscientiousness', 'label': 'Conscientiousness', 'value': 0.7, 'category': 'personality'},
                {'id': 'technology', 'label': 'Technology', 'value': 0.9, 'category': 'interest'},
                {'id': 'analytical', 'label': 'Analytical', 'value': 0.8, 'category': 'communication'}
            ],
            'edges': [
                {'from': 'openness', 'to': 'technology', 'weight': 0.7},
                {'from': 'conscientiousness', 'to': 'analytical', 'weight': 0.8},
                {'from': 'technology', 'to': 'analytical', 'weight': 0.6}
            ],
            'metadata': {
                'total_nodes': 4,
                'total_edges': 3,
                'categories': ['personality', 'interest', 'communication']
            },
            'build_time': datetime.now().isoformat()
        }

class MockRedditProfileApp:
    def __init__(self, config=None):
        self.config = config or {}
        self.url_parser = MockRedditUrlParser()
        self.data_crawler = MockDataCrawler(config)
        self.semantic_analyzer = MockSemanticAnalyzer(config)
        self.graph_builder = MockGraphBuilder(config)
    
    async def analyze_reddit_profile(self, url):
        # 完整的分析流程
        # 1. 解析URL
        link_info = self.url_parser.parse_url(url)
        if link_info.link_type == 'unknown':
            raise ValueError(f"不支持的URL格式: {url}")
        
        # 2. 爬取数据
        crawl_data = await self.data_crawler.crawl_from_link(link_info)
        if not crawl_data:
            raise ValueError("无法爬取数据")
        
        # 3. 语义分析
        analysis_data = await self.semantic_analyzer.analyze_user_profile(crawl_data)
        if not analysis_data:
            raise ValueError("语义分析失败")
        
        # 4. 构建图谱
        graph_data = await self.graph_builder.build_personality_graph(analysis_data)
        if not graph_data:
            raise ValueError("图谱构建失败")
        
        # 5. 返回完整结果
        return {
            'url': url,
            'link_info': link_info,
            'crawl_data': crawl_data,
            'analysis_data': analysis_data,
            'graph_data': graph_data,
            'processing_time': datetime.now().isoformat()
        }


class TestOnlyProfileIntegration(unittest.TestCase):
    """only_profile 项目集成测试"""
    
    def setUp(self):
        """测试设置"""
        self.config = {
            'reddit': {
                'client_id': 'test_client_id',
                'client_secret': 'test_client_secret',
                'user_agent': 'test_user_agent'
            },
            'ai': {
                'api_key': 'test_api_key',
                'model_name': 'test_model',
                'max_tokens': 4000
            },
            'app': {
                'debug': True,
                'port': 8001,
                'timeout': 300
            }
        }
        
        self.app = MockRedditProfileApp(self.config)
        
        # 测试URL
        self.test_urls = [
            'https://reddit.com/u/testuser',
            'https://www.reddit.com/user/testuser',
            'https://reddit.com/r/Python/comments/abc123',
            'https://old.reddit.com/u/spez'
        ]
    
    def test_complete_workflow_user_profile(self):
        """测试完整的用户画像工作流程"""
        print("测试完整的用户画像工作流程...")
        
        async def run_test():
            url = 'https://reddit.com/u/testuser'
            result = await self.app.analyze_reddit_profile(url)
            
            # 验证结果结构
            self.assertIsInstance(result, dict)
            self.assertIn('url', result)
            self.assertIn('link_info', result)
            self.assertIn('crawl_data', result)
            self.assertIn('analysis_data', result)
            self.assertIn('graph_data', result)
            self.assertIn('processing_time', result)
            
            # 验证URL信息
            self.assertEqual(result['url'], url)
            self.assertEqual(result['link_info'].link_type, 'user')
            self.assertEqual(result['link_info'].username, 'testuser')
            
            # 验证爬取数据
            crawl_data = result['crawl_data']
            self.assertEqual(crawl_data['username'], 'testuser')
            self.assertIn('posts', crawl_data)
            self.assertIn('comments', crawl_data)
            self.assertIsInstance(crawl_data['posts'], list)
            self.assertIsInstance(crawl_data['comments'], list)
            
            # 验证分析数据
            analysis_data = result['analysis_data']
            self.assertIn('personality_traits', analysis_data)
            self.assertIn('interests', analysis_data)
            self.assertIn('communication_style', analysis_data)
            self.assertIn('emotional_patterns', analysis_data)
            
            # 验证图谱数据
            graph_data = result['graph_data']
            self.assertIn('nodes', graph_data)
            self.assertIn('edges', graph_data)
            self.assertIn('metadata', graph_data)
            self.assertIsInstance(graph_data['nodes'], list)
            self.assertIsInstance(graph_data['edges'], list)
            
            # 验证节点结构
            for node in graph_data['nodes']:
                self.assertIn('id', node)
                self.assertIn('label', node)
                self.assertIn('value', node)
                self.assertIn('category', node)
            
            # 验证边结构
            for edge in graph_data['edges']:
                self.assertIn('from', edge)
                self.assertIn('to', edge)
                self.assertIn('weight', edge)
        
        asyncio.run(run_test())
    
    def test_workflow_with_invalid_url(self):
        """测试无效URL的工作流程"""
        print("测试无效URL的工作流程...")
        
        async def run_test():
            invalid_urls = [
                'https://twitter.com/user',
                'https://facebook.com/user',
                'invalid_url',
                ''
            ]
            
            for url in invalid_urls:
                with self.subTest(url=url):
                    with self.assertRaises(ValueError):
                        await self.app.analyze_reddit_profile(url)
        
        asyncio.run(run_test())
    
    def test_workflow_error_handling(self):
        """测试工作流程的错误处理"""
        print("测试工作流程的错误处理...")
        
        async def run_test():
            # 模拟数据爬取失败
            original_crawl_from_link = self.app.data_crawler.crawl_from_link
            
            async def mock_crawl_failure(link_info):
                return {}  # 空数据模拟失败
            
            self.app.data_crawler.crawl_from_link = mock_crawl_failure
            
            with self.assertRaises(ValueError):
                await self.app.analyze_reddit_profile('https://reddit.com/u/testuser')
            
            # 恢复原始方法
            self.app.data_crawler.crawl_from_link = original_crawl_from_link
        
        asyncio.run(run_test())
    
    def test_workflow_performance(self):
        """测试工作流程的性能"""
        print("测试工作流程的性能...")
        
        async def run_test():
            start_time = datetime.now()
            
            result = await self.app.analyze_reddit_profile('https://reddit.com/u/testuser')
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 验证处理时间合理
            self.assertLess(duration, 60)  # 应该在60秒内完成
            
            # 验证结果完整性
            self.assertIsInstance(result, dict)
            self.assertIn('processing_time', result)
        
        asyncio.run(run_test())
    
    def test_workflow_with_multiple_urls(self):
        """测试多个URL的工作流程"""
        print("测试多个URL的工作流程...")
        
        async def run_test():
            results = []
            
            for url in self.test_urls:
                try:
                    result = await self.app.analyze_reddit_profile(url)
                    results.append(result)
                except ValueError as e:
                    # 某些URL可能不支持，这是正常的
                    print(f"URL {url} 不支持: {e}")
                    continue
            
            # 验证至少有一些结果
            self.assertGreater(len(results), 0)
            
            # 验证每个结果的结构
            for result in results:
                self.assertIn('url', result)
                self.assertIn('link_info', result)
                self.assertIn('crawl_data', result)
                self.assertIn('analysis_data', result)
                self.assertIn('graph_data', result)
        
        asyncio.run(run_test())
    
    def test_data_consistency(self):
        """测试数据一致性"""
        print("测试数据一致性...")
        
        async def run_test():
            url = 'https://reddit.com/u/testuser'
            
            # 多次运行相同的分析
            results = []
            for i in range(3):
                result = await self.app.analyze_reddit_profile(url)
                results.append(result)
            
            # 验证用户名的一致性
            usernames = [r['link_info'].username for r in results]
            self.assertEqual(len(set(usernames)), 1)  # 所有用户名应该相同
            
            # 验证URL的一致性
            urls = [r['url'] for r in results]
            self.assertEqual(len(set(urls)), 1)  # 所有URL应该相同
        
        asyncio.run(run_test())
    
    def test_module_integration(self):
        """测试模块集成"""
        print("测试模块集成...")
        
        # 测试各个模块的初始化
        self.assertIsNotNone(self.app.url_parser)
        self.assertIsNotNone(self.app.data_crawler)
        self.assertIsNotNone(self.app.semantic_analyzer)
        self.assertIsNotNone(self.app.graph_builder)
        
        # 测试配置传递
        self.assertEqual(self.app.config, self.config)
    
    def test_result_serialization(self):
        """测试结果序列化"""
        print("测试结果序列化...")
        
        async def run_test():
            result = await self.app.analyze_reddit_profile('https://reddit.com/u/testuser')
            
            # 尝试序列化结果
            try:
                # 模拟序列化过程
                serialized = json.dumps(result, default=str)
                self.assertIsInstance(serialized, str)
                
                # 尝试反序列化
                deserialized = json.loads(serialized)
                self.assertIsInstance(deserialized, dict)
                
                # 验证关键字段
                self.assertIn('url', deserialized)
                self.assertIn('processing_time', deserialized)
                
            except (TypeError, ValueError) as e:
                self.fail(f"序列化失败: {e}")
        
        asyncio.run(run_test())
    
    def test_config_validation(self):
        """测试配置验证"""
        print("测试配置验证...")
        
        # 测试无效配置
        invalid_configs = [
            {},  # 空配置
            {'reddit': {}},  # 不完整配置
            {'ai': {'api_key': ''}},  # 空API密钥
            None  # None配置
        ]
        
        for config in invalid_configs:
            with self.subTest(config=config):
                # 这里应该测试配置验证逻辑
                # 由于我们的模拟类比较简单，这里只是验证初始化不会出错
                try:
                    app = MockRedditProfileApp(config)
                    self.assertIsNotNone(app)
                except Exception as e:
                    # 某些配置可能会导致初始化失败，这是正常的
                    print(f"配置 {config} 导致初始化失败: {e}")
    
    def test_edge_cases(self):
        """测试边界情况"""
        print("测试边界情况...")
        
        async def run_test():
            # 测试空用户名
            empty_user_url = 'https://reddit.com/u/'
            try:
                result = await self.app.analyze_reddit_profile(empty_user_url)
                # 如果没有抛出异常，检查结果
                self.assertIsInstance(result, dict)
            except ValueError:
                # 预期的异常
                pass
            
            # 测试非常长的URL
            long_url = 'https://reddit.com/u/' + 'a' * 1000
            try:
                result = await self.app.analyze_reddit_profile(long_url)
                self.assertIsInstance(result, dict)
            except ValueError:
                # 预期的异常
                pass
        
        asyncio.run(run_test())


class TestOnlyProfileEndToEnd(unittest.TestCase):
    """端到端测试"""
    
    def setUp(self):
        """测试设置"""
        self.config = {
            'reddit': {
                'client_id': 'test_client_id',
                'client_secret': 'test_client_secret',
                'user_agent': 'test_user_agent'
            },
            'ai': {
                'api_key': 'test_api_key',
                'model_name': 'test_model'
            }
        }
        
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
        self.output_file = os.path.join(self.temp_dir, 'test_output.json')
    
    def tearDown(self):
        """清理测试"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        print("测试端到端工作流程...")
        
        async def run_test():
            app = MockRedditProfileApp(self.config)
            
            # 执行完整的分析流程
            result = await app.analyze_reddit_profile('https://reddit.com/u/testuser')
            
            # 保存结果到文件
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            # 验证文件生成
            self.assertTrue(os.path.exists(self.output_file))
            
            # 验证文件内容
            with open(self.output_file, 'r', encoding='utf-8') as f:
                loaded_result = json.load(f)
            
            self.assertEqual(loaded_result['url'], 'https://reddit.com/u/testuser')
            self.assertIn('graph_data', loaded_result)
            self.assertIn('analysis_data', loaded_result)
        
        asyncio.run(run_test())
    
    def test_batch_processing(self):
        """测试批量处理"""
        print("测试批量处理...")
        
        async def run_test():
            app = MockRedditProfileApp(self.config)
            
            urls = [
                'https://reddit.com/u/user1',
                'https://reddit.com/u/user2',
                'https://reddit.com/u/user3'
            ]
            
            results = []
            for url in urls:
                try:
                    result = await app.analyze_reddit_profile(url)
                    results.append(result)
                except ValueError as e:
                    print(f"处理 {url} 时出错: {e}")
            
            # 验证批量处理结果
            self.assertEqual(len(results), len(urls))
            
            # 验证每个结果的唯一性
            usernames = [r['link_info'].username for r in results]
            self.assertEqual(len(set(usernames)), len(usernames))
        
        asyncio.run(run_test())


def run_integration_tests():
    """运行集成测试"""
    print("=" * 80)
    print("开始运行 only_profile 集成测试")
    print("=" * 80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_suite.addTest(unittest.makeSuite(TestOnlyProfileIntegration))
    test_suite.addTest(unittest.makeSuite(TestOnlyProfileEndToEnd))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("集成测试结果:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("=" * 80)
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1) 