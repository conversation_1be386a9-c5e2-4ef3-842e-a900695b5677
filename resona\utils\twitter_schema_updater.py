"""
Twitter Schema ID 手动更新工具
使用已知的有效schema ID列表进行替换
"""
import os
import re
import logging
from pathlib import Path
from typing import Optional, List

logger = logging.getLogger(__name__)

class TwitterSchemaUpdater:
    """Twitter Schema ID 更新器 - 使用预定义的ID列表"""
    
    # 已知的有效schema ID列表（按时间从新到旧）
    KNOWN_SCHEMA_IDS = [
        "tD8zKvQzwY3kmNMGB-T5Uw",  # 2024-Q4 latest
        "lZ0GCEojmtQfiUQa5oJSEw",  # 2024-Q4 
        "UN1i3zUiCWa-6r-Uaho4fw",  # 2024-Q3
        "nK1dw4oV3k4w5TdtcAdSww",  # 2024-Q3
        "gkjsKepM6gl_HmFWoWKfgg",  # 2024-Q2
        "7jT5GT59P8IFjgxwqnEdQw",  # 当前使用的（已过期）
    ]
    
    def __init__(self):
        self.snscrape_twitter_path = self._find_snscrape_twitter_file()
        
    def _find_snscrape_twitter_file(self) -> Optional[Path]:
        """查找snscrape的twitter.py文件"""
        try:
            import snscrape
            snscrape_dir = Path(snscrape.__file__).parent
            twitter_file = snscrape_dir / 'modules' / 'twitter.py'
            
            if twitter_file.exists():
                return twitter_file
            else:
                logger.error(f"未找到twitter.py文件: {twitter_file}")
                return None
                
        except ImportError:
            logger.error("snscrape未安装")
            return None
    
    def get_current_schema_id(self) -> Optional[str]:
        """获取当前使用的schema ID"""
        if not self.snscrape_twitter_path:
            return None
            
        try:
            with open(self.snscrape_twitter_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 搜索SearchTimeline URL中的schema ID
            pattern = r'https://twitter\.com/i/api/graphql/([A-Za-z0-9_-]+)/SearchTimeline'
            match = re.search(pattern, content)
            
            if match:
                current_id = match.group(1)
                logger.info(f"当前schema ID: {current_id}")
                return current_id
            else:
                logger.error("未找到SearchTimeline schema ID")
                return None
                
        except Exception as e:
            logger.error(f"读取twitter.py文件失败: {e}")
            return None
    
    def update_to_latest_schema(self) -> bool:
        """更新到最新的schema ID"""
        current_id = self.get_current_schema_id()
        if not current_id:
            return False
        
        # 找到当前ID在列表中的位置
        try:
            current_index = self.KNOWN_SCHEMA_IDS.index(current_id)
            logger.info(f"当前ID在已知列表中的位置: {current_index}")
        except ValueError:
            logger.warning(f"当前ID {current_id} 不在已知列表中，使用最新ID")
            current_index = len(self.KNOWN_SCHEMA_IDS) - 1
        
        # 尝试使用更新的schema ID
        for i in range(min(current_index, len(self.KNOWN_SCHEMA_IDS) - 1)):
            new_id = self.KNOWN_SCHEMA_IDS[i]
            logger.info(f"尝试更新到schema ID: {new_id}")
            
            if self.replace_schema_id(current_id, new_id):
                logger.info(f"成功更新schema ID: {current_id} -> {new_id}")
                return True
            else:
                logger.error(f"替换schema ID失败: {new_id}")
        
        logger.error("所有候选schema ID都尝试失败")
        return False
    
    def replace_schema_id(self, old_id: str, new_id: str) -> bool:
        """替换twitter.py文件中的schema ID"""
        if not self.snscrape_twitter_path:
            return False
            
        try:
            # 读取原文件
            with open(self.snscrape_twitter_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 备份原文件
            backup_path = self.snscrape_twitter_path.with_suffix('.py.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            logger.info(f"已备份原文件到: {backup_path}")
            
            # 替换schema ID
            new_content = original_content.replace(old_id, new_id)
            
            # 验证替换
            if old_id in new_content:
                logger.error("Schema ID替换失败 - 旧ID仍存在")
                return False
            
            if new_id not in new_content:
                logger.error("Schema ID替换失败 - 新ID未插入")
                return False
            
            # 写入新文件
            with open(self.snscrape_twitter_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.info(f"✓ Schema ID替换成功: {old_id} -> {new_id}")
            return True
            
        except Exception as e:
            logger.error(f"替换schema ID失败: {e}")
            return False
    
    def force_update_to_specific_id(self, target_id: str) -> bool:
        """强制更新到指定的schema ID"""
        current_id = self.get_current_schema_id()
        if not current_id:
            return False
            
        return self.replace_schema_id(current_id, target_id)

def quick_update_twitter_schema() -> bool:
    """快速更新Twitter schema ID"""
    updater = TwitterSchemaUpdater()
    return updater.update_to_latest_schema()

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger.info("开始Twitter Schema ID更新...")
    
    # 执行更新
    success = quick_update_twitter_schema()
    
    if success:
        logger.info("✓ Twitter Schema ID更新完成！")
        logger.info("请重新运行Twitter测试以验证修复效果")
    else:
        logger.error("✗ Twitter Schema ID更新失败")
    
    exit(0 if success else 1) 