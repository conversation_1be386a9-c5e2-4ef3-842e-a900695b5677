"""
Twitter真实数据抓取测试脚本
验证TwitterService的真实数据抓取功能，不使用模拟数据
"""
import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from resona.services.twitter_service import TwitterService
from resona.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cursor_test/twitter_real_test.log')
    ]
)
logger = logging.getLogger(__name__)

async def test_twitter_connection():
    """测试Twitter连接"""
    logger.info("🔗 测试Twitter连接...")
    
    try:
        twitter_service = TwitterService()
        connection_result = await twitter_service.test_connection()
        
        if connection_result:
            logger.info("✅ Twitter连接测试成功！")
            return True
        else:
            logger.error("❌ Twitter连接测试失败！")
            return False
            
    except Exception as e:
        logger.error(f"❌ Twitter连接测试异常: {e}")
        return False

async def test_twitter_search():
    """测试Twitter搜索功能"""
    logger.info("🔍 测试Twitter搜索功能...")
    
    try:
        twitter_service = TwitterService()
        
        # 测试搜索关键词
        test_keywords = ["程序员", "工作压力"]
        
        logger.info(f"搜索关键词: {test_keywords}")
        
        # 执行搜索
        search_results = await twitter_service.search_posts_by_keywords_enhanced(
            keywords=test_keywords,
            limit=10,
            time_filter="week"
        )
        
        logger.info(f"✅ 搜索完成，获得 {len(search_results)} 条推文")
        
        # 分析结果
        if search_results:
            for i, post in enumerate(search_results[:3]):
                logger.info(f"推文 {i+1}:")
                logger.info(f"  作者: @{post.get('author', 'unknown')}")
                logger.info(f"  内容: {post.get('text', '')[:100]}...")
                logger.info(f"  互动: 👍{post.get('like_count', 0)} 🔁{post.get('retweet_count', 0)} 💬{post.get('reply_count', 0)}")
                logger.info(f"  质量分数: {post.get('quality_score', 0):.2f}")
                logger.info("---")
            
            return True
        else:
            logger.warning("⚠️  搜索结果为空")
            return False
            
    except Exception as e:
        logger.error(f"❌ Twitter搜索测试失败: {e}")
        return False

async def test_extract_commenters():
    """测试提取推主功能"""
    logger.info("👥 测试提取推主功能...")
    
    try:
        twitter_service = TwitterService()
        
        # 先搜索一些推文
        search_results = await twitter_service.search_posts_by_keywords_enhanced(
            keywords=["工作", "生活"],
            limit=15,
            time_filter="week"
        )
        
        if not search_results:
            logger.warning("⚠️  没有搜索结果，跳过推主提取测试")
            return False
        
        logger.info(f"基于 {len(search_results)} 条推文提取推主...")
        
        # 提取推主
        commenters_result = await twitter_service.extract_quality_commenters_detailed(
            posts=search_results,
            min_score=0,
            min_length=50,
            max_commenters=10
        )
        
        selected_commenters = commenters_result.get('selected_commenters', [])
        extraction_stats = commenters_result.get('extraction_stats', {})
        
        logger.info(f"✅ 推主提取完成:")
        logger.info(f"  总推文数: {extraction_stats.get('total_posts_processed', 0)}")
        logger.info(f"  发现推主数: {extraction_stats.get('total_authors_found', 0)}")
        logger.info(f"  优质推主数: {len(selected_commenters)}")
        
        # 显示前几个推主
        for i, commenter in enumerate(selected_commenters[:3]):
            logger.info(f"推主 {i+1}: @{commenter.get('username', 'unknown')}")
            logger.info(f"  推文数: {commenter.get('post_count', 0)}")
            logger.info(f"  平均质量: {commenter.get('avg_quality', 0):.2f}")
            logger.info(f"  选择理由: {', '.join(commenter.get('selection_reasons', []))}")
            logger.info("---")
        
        return len(selected_commenters) > 0
        
    except Exception as e:
        logger.error(f"❌ 推主提取测试失败: {e}")
        return False

async def test_user_timeline():
    """测试用户时间线获取功能"""
    logger.info("📊 测试用户时间线获取功能...")
    
    try:
        twitter_service = TwitterService()
        
        # 首先搜索一些推文获取用户名
        search_results = await twitter_service.search_posts_by_keywords_enhanced(
            keywords=["技术"],
            limit=5,
            time_filter="week"
        )
        
        if not search_results:
            logger.warning("⚠️  没有搜索结果，跳过时间线测试")
            return False
        
        # 选择第一个用户进行测试
        test_username = search_results[0].get('author', '')
        if not test_username:
            logger.warning("⚠️  无法获取测试用户名")
            return False
        
        logger.info(f"获取用户 @{test_username} 的时间线...")
        
        # 获取用户历史数据
        user_history = await twitter_service.get_user_comprehensive_history(
            username=test_username,
            limit=50
        )
        
        if user_history:
            logger.info(f"✅ 用户时间线获取成功:")
            logger.info(f"  用户: @{user_history.get('username', 'unknown')}")
            logger.info(f"  总推文数: {user_history.get('total_content_fetched', 0)}")
            logger.info(f"  采样推文数: {user_history.get('sampled_content_count', 0)}")
            logger.info(f"  采样方法: {user_history.get('sampling_method', 'unknown')}")
            
            # 显示统计信息
            content_stats = user_history.get('content_stats', {})
            if content_stats:
                logger.info(f"  平均长度: {content_stats.get('avg_length', 0):.1f}字符")
                logger.info(f"  平均点赞: {content_stats.get('avg_likes', 0):.1f}")
                logger.info(f"  平均转发: {content_stats.get('avg_retweets', 0):.1f}")
            
            return True
        else:
            logger.warning(f"⚠️  无法获取用户 @{test_username} 的时间线")
            return False
            
    except Exception as e:
        logger.error(f"❌ 用户时间线测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🐦 开始Twitter真实数据抓取测试")
    logger.info("="*60)
    
    test_results = {}
    
    try:
        # 测试1: 连接测试
        logger.info("测试1: Twitter连接")
        test_results['connection'] = await test_twitter_connection()
        
        if not test_results['connection']:
            logger.error("❌ Twitter连接失败，跳过后续测试")
            return
        
        logger.info("")
        
        # 测试2: 搜索功能
        logger.info("测试2: Twitter搜索")
        test_results['search'] = await test_twitter_search()
        logger.info("")
        
        # 测试3: 推主提取
        logger.info("测试3: 推主提取")
        test_results['commenters'] = await test_extract_commenters()
        logger.info("")
        
        # 测试4: 用户时间线
        logger.info("测试4: 用户时间线")
        test_results['timeline'] = await test_user_timeline()
        logger.info("")
        
        # 汇总结果
        logger.info("🏁 测试结果汇总:")
        logger.info("="*60)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  {test_name}: {status}")
        
        logger.info(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！Twitter真实数据抓取功能正常")
        elif passed_tests > 0:
            logger.info("⚠️  部分测试通过，Twitter服务部分可用")
        else:
            logger.error("❌ 所有测试失败，Twitter服务不可用")
            
    except Exception as e:
        logger.error(f"❌ 测试执行异常: {e}")
    
    logger.info("\n🔚 测试完成")

if __name__ == "__main__":
    asyncio.run(main()) 