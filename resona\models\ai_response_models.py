"""
AI 响应模型 - 用于 Lang<PERSON>hain PydanticOutputParser
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class EmotionType(str, Enum):
    """情绪类型枚举"""
    ANXIETY = "anxiety"
    CONFUSION = "confusion"
    SADNESS = "sadness"
    HOPE = "hope"
    FRUSTRATION = "frustration"
    LONELINESS = "loneliness"
    GUILT = "guilt"
    FEAR = "fear"
    ANGER = "anger"
    RELIEF = "relief"


class ExpressionStyle(str, Enum):
    """表达风格枚举"""
    ANALYTICAL = "analytical"
    EMOTIONAL = "emotional"
    AVOIDANT = "avoidant"
    SEEKING = "seeking"
    REFLECTIVE = "reflective"


class QueryAnalysis(BaseModel):
    """查询分析结果 - 用于LangChain结构化输出"""
    main_topics: List[str] = Field(
        description="主要话题，如'职业选择'、'人际关系'等",
        min_length=1
    )
    emotions: List[EmotionType] = Field(
        description="情绪标签，只能从EmotionType枚举中选择",
        min_length=1
    )
    expression_style: ExpressionStyle = Field(
        description="表达风格，只能从ExpressionStyle枚举中选择"
    )
    intent_summary: str = Field(
        description="一句话总结用户的核心困扰和需求",
        min_length=5
    )


class ResonanceSummary(BaseModel):
    """共鸣摘要 - 用于LangChain结构化输出"""
    summary: str = Field(
        description="1-2句话的共鸣摘要，说明为什么推荐这个用户",
        min_length=10,
        max_length=200
    )


class PrivateMessage(BaseModel):
    """私信内容 - 用于LangChain结构化输出"""
    message: str = Field(
        description="真诚的私信开场白，50-100字",
        min_length=20,
        max_length=150
    )


class TranslationResult(BaseModel):
    """翻译结果 - 用于LangChain结构化输出"""
    translated_text: str = Field(
        description="翻译后的英文文本",
        min_length=1
    )


class RankingResult(BaseModel):
    """用户排序结果 - 用于LangChain结构化输出"""
    reasoning: str = Field(
        description="排序理由，解释为什么这样排序",
        min_length=20
    )
    final_score: float = Field(
        description="最终综合得分，0.0-1.0之间",
        ge=0.0,
        le=1.0
    )


# 新增：语义分析结果模型
class SemanticAnalysisResult(BaseModel):
    """语义分析结果 - 对应 semantic_analyzer 的输出"""
    search_keywords: List[str] = Field(
        description="List of English keywords for Reddit search",
        min_length=5,
        max_length=8
    )
    topics: List[str] = Field(description="High-level topics identified from the user query")
    emotional_state: Dict[str, float] = Field(
        description="Analysis of the user's emotional state",
        examples=[{"anxiety": 0.7, "confusion": 0.5}]
    )
    values_info: Dict[str, str] = Field(
        description="User's values and decision-making style",
        examples=[{"decision_style": "analytical", "priority_focus": "career"}]
    )
    core_concerns: List[str] = Field(description="Core concerns extracted from the query")
    decision_points: List[str] = Field(description="Key decision points the user is facing")
    life_domains: List[str] = Field(description="Life domains affected by the user's query")
    support_needs: List[str] = Field(description="Types of support the user is seeking")
    confidence: float = Field(
        description="Confidence score of the analysis",
        ge=0.0,
        le=1.0
    )


# 新增：图谱节点模型
class GraphNodeModel(BaseModel):
    """图谱节点模型"""
    id: str = Field(description="Unique identifier for the node")
    type: str = Field(
        description="Node type: experience, belief, emotion, or topic"
    )
    content: str = Field(description="Content description of the node")
    weight: float = Field(
        description="Weight/importance of the node",
        ge=0.0,
        le=1.0
    )
    metadata: Dict[str, Any] = Field(
        description="Additional metadata for the node",
        default_factory=dict
    )


# 新增：图谱边模型
class GraphEdgeModel(BaseModel):
    """图谱边模型"""
    source: str = Field(description="Source node ID")
    target: str = Field(description="Target node ID")
    relation: str = Field(
        description="Type of relationship: causes, influences, conflicts, supports, triggers, or correlates"
    )
    weight: float = Field(
        description="Strength of the relationship",
        ge=0.0,
        le=1.0
    )
    evidence: str = Field(description="Text evidence supporting this relationship")


# 新增：图谱分析结果模型
class GraphAnalysisResult(BaseModel):
    """图谱分析结果 - 对应 graph_builder 的输出"""
    nodes: List[GraphNodeModel] = Field(
        description="List of graph nodes",
        max_length=10
    )
    edges: List[GraphEdgeModel] = Field(description="List of graph edges")


# 新增：追问问题生成结果模型
class ClarifyingQuestionsResult(BaseModel):
    """追问问题生成结果"""
    questions: List[str] = Field(
        description="List of clarifying questions",
        min_length=1,
        max_length=5
    ) 