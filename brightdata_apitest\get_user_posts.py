#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BrightData API - Twitter用户帖子获取工具

交互式导航工具：启动后按提示填写参数
"""

import requests
import json
import time
import sys
from datetime import datetime as _dt
import os
import pathlib

class TwitterDataCollector:
    def __init__(self, api_token, dataset_id):
        self.api_token = api_token
        self.dataset_id = dataset_id
        self.base_headers = {
            "Authorization": f"Bearer {api_token}",
        }
    
    def trigger_collection(self, usernames_and_counts):
        """
        触发数据收集
        
        Args:
            usernames_and_counts: 用户名和帖子数量的列表
            格式: [{"username": "elonmusk", "max_posts": 100}]
        
        Returns:
            包含 snapshot_id 的响应
        """
        url = "https://api.brightdata.com/datasets/v3/trigger"
        headers = {
            **self.base_headers,
            "Content-Type": "application/json",
        }
        params = {
            "dataset_id": self.dataset_id,
            "include_errors": "true",
        }
        
        # 转换为API所需格式
        data = []
        for item in usernames_and_counts:
            username = item["username"].replace("@", "")  # 移除@符号
            payload = {
                "url": f"https://x.com/{username}",
            }
            # 只有指定了帖子数量才添加限制
            if item.get("max_posts") is not None:
                payload["max_number_of_posts"] = item["max_posts"]
            data.append(payload)
        
        response = requests.post(url, headers=headers, params=params, json=data, timeout=30)
        return response.json()
    
    def check_progress(self, snapshot_id):
        """检查数据收集进度"""
        url = f"https://api.brightdata.com/datasets/v3/progress/{snapshot_id}"
        
        response = requests.get(url, headers=self.base_headers, timeout=30)
        return response.json()
    
    def get_snapshots(self, status="ready"):
        """获取快照列表"""
        url = "https://api.brightdata.com/datasets/v3/snapshots"
        params = {
            "dataset_id": self.dataset_id,
            "status": status,
        }
        
        response = requests.get(url, headers=self.base_headers, params=params, timeout=30)
        return response.json()
    
    def download_snapshot_data(self, snapshot_id, save_dir="brightdata_apitest/data", max_retries=5):
        """下载快照数据并保存到本地JSON文件
        
        Args:
            snapshot_id: 快照ID
            save_dir: 保存目录，相对项目根目录
            max_retries: 最大重试次数
        
        Returns:
            本地文件路径或None
        """
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # API端点（目前BrightData提供snapshot直接下载接口）
        url = f"https://api.brightdata.com/datasets/v3/snapshot/{snapshot_id}"
        params = {"format": "json"}
        
        # 重试机制处理building状态
        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=self.base_headers, params=params, timeout=60)
                
                if response.status_code == 200:
                    data = response.json()
                    # 构建文件名
                    timestamp = _dt.now().strftime("%Y%m%d_%H%M%S")
                    file_path = os.path.join(save_dir, f"snapshot_{snapshot_id}_{timestamp}.json")
                    
                    # 保存
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 数据已保存到 {file_path}")
                    return file_path
                
                elif response.status_code == 202:
                    # 快照仍在构建中
                    try:
                        error_data = response.json()
                        message = error_data.get("message", "快照构建中")
                        print(f"⏳ {message}，等待30秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    except:
                        print(f"⏳ 快照构建中，等待30秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    
                    if attempt < max_retries - 1:  # 不是最后一次尝试
                        time.sleep(30)
                        continue
                    else:
                        print(f"❌ 重试{max_retries}次后仍无法下载，请稍后手动尝试")
                        return None
                
                else:
                    print(f"❌ 下载失败，状态码: {response.status_code}, 响应: {response.text[:200]}")
                    return None
                    
            except Exception as e:
                print(f"❌ 下载快照数据时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    print("⏳ 等待10秒后重试...")
                    time.sleep(10)
                else:
                    return None
        
        return None
    
    def get_account_usage(self):
        """获取账户使用情况和record消耗"""
        # 尝试多个可能的API端点
        endpoints = [
            "https://api.brightdata.com/datasets/v3/usage",
            "https://api.brightdata.com/v1/usage",
            f"https://api.brightdata.com/datasets/v3/datasets/{self.dataset_id}/usage"
        ]
        
        for i, url in enumerate(endpoints):
            try:
                params = {}
                if i == 0:  # 第一个端点需要dataset_id参数
                    params = {"dataset_id": self.dataset_id}
                
                response = requests.get(url, headers=self.base_headers, params=params, timeout=30)
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 404 and i < len(endpoints) - 1:
                    continue  # 尝试下一个端点
                else:
                    print(f"⚠️ 端点 {i+1} 获取使用情况失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"⚠️ 端点 {i+1} 请求时出错: {e}")
        
        # 如果所有端点都失败，提供快照汇总信息作为替代
        print("📊 使用快照信息提供替代使用统计...")
        try:
            snapshots = self.get_snapshots()
            snapshots_list = snapshots if isinstance(snapshots, list) else snapshots.get("snapshots", [])
            
            if snapshots_list:
                total_records = sum(snapshot.get("dataset_size", 0) for snapshot in snapshots_list)
                recent_snapshots = len([s for s in snapshots_list if s.get("created")])
                
                return {
                    "alternative_stats": True,
                    "total_snapshots": recent_snapshots,
                    "total_records_from_snapshots": total_records,
                    "latest_snapshot": snapshots_list[0] if snapshots_list else None,
                    "note": "这是基于快照数据的估算，非官方API统计"
                }
            else:
                return {"error": "无法获取任何使用统计信息"}
        except Exception as e:
            print(f"⚠️ 获取替代统计信息时出错: {e}")
            return None
    
    def get_snapshot_usage(self, snapshot_id):
        """获取特定快照的record消耗"""
        url = f"https://api.brightdata.com/datasets/v3/snapshot/{snapshot_id}/usage"
        
        try:
            response = requests.get(url, headers=self.base_headers, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                # 如果没有专门的快照使用API，从快照详情中获取
                snapshots = self.get_snapshots()
                snapshots_list = snapshots if isinstance(snapshots, list) else snapshots.get("snapshots", [])
                
                for snapshot in snapshots_list:
                    if snapshot.get("id") == snapshot_id:
                        return {
                            "records_consumed": snapshot.get("dataset_size", 0),
                            "snapshot_id": snapshot_id
                        }
                return None
        except Exception as e:
            print(f"⚠️ 获取快照使用情况时出错: {e}")
            return None
    
    def collect_user_posts(self, username, max_posts=None, wait_for_completion=True):
        """
        完整的用户帖子收集流程
        
        Args:
            username: Twitter用户名（可带@符号）
            max_posts: 最大帖子数量，None表示不限制
            wait_for_completion: 是否等待完成
        
        Returns:
            收集结果
        """
        username = username.replace("@", "")
        
        if max_posts is None:
            print(f"🚀 开始收集 @{username} 的帖子（不限制数量）...")
        else:
            print(f"🚀 开始收集 @{username} 的 {max_posts} 条帖子...")
        
        # 1. 触发收集
        try:
            result = self.trigger_collection([{"username": username, "max_posts": max_posts}])
            
            if "snapshot_id" not in result:
                print(f"❌ 触发失败: {result}")
                return None
            
            snapshot_id = result["snapshot_id"]
            print(f"✅ 收集任务已触发，快照ID: {snapshot_id}")
            
            if not wait_for_completion:
                print("⏭️ 不等待完成，返回快照ID")
                return {"snapshot_id": snapshot_id, "status": "triggered"}
            
            # 2. 等待完成
            print("⏳ 等待数据收集完成...")
            start_time = time.time()
            
            while True:
                progress = self.check_progress(snapshot_id)
                status = progress.get("status", "unknown")
                
                elapsed = int(time.time() - start_time)
                print(f"📊 状态: {status} (已等待 {elapsed}s)")
                
                if status == "ready":
                    print("🎉 数据收集完成！")
                    break
                elif status == "failed":
                    print(f"❌ 数据收集失败: {progress}")
                    return None
                elif elapsed > 600:  # 超过10分钟
                    print("⏰ 等待超时，请稍后手动检查")
                    return {"snapshot_id": snapshot_id, "status": "timeout"}
                
                time.sleep(15)  # 每15秒检查一次
            
            # 3. 获取结果信息并自动下载
            snapshots_result = self.get_snapshots()
            snapshots = snapshots_result if isinstance(snapshots_result, list) else snapshots_result.get("snapshots", [])
            
            for snapshot in snapshots:
                if snapshot.get("id") == snapshot_id:
                    print(f"📦 数据大小: {snapshot.get('dataset_size', 'N/A')} 条记录")
                    print(f"🕐 创建时间: {snapshot.get('created', 'N/A')}")
                    
                    # 显示record消耗信息
                    usage_info = self.get_snapshot_usage(snapshot_id)
                    if usage_info:
                        consumed = usage_info.get('records_consumed', snapshot.get('dataset_size', 0))
                        print(f"💰 本次消耗: {consumed} records")
                    
                    # 自动下载并保存数据
                    saved_path = self.download_snapshot_data(snapshot_id)
                    if saved_path:
                        snapshot["local_file"] = saved_path
                    return snapshot
            
            print("⚠️ 未找到对应的快照数据")
            return None
            
        except Exception as e:
            print(f"❌ 收集过程中出错: {e}")
            return None

def main():
    # API配置
    API_TOKEN = "1967f140fb33809c108c2a11e811362313fd4a6819e4eaa49ab63881b4982c71"
    DATASET_ID = "gd_lwxmeb2u1cniijd7t4"
    
    print("=" * 50)
    print("🐦 Twitter用户帖子获取工具")
    print("=" * 50)
    print()
    
    # 创建收集器
    collector = TwitterDataCollector(API_TOKEN, DATASET_ID)
    
    while True:
        print("请选择操作：")
        print("1. 获取用户帖子")
        print("2. 查看已完成的快照列表")
        print("3. 查看账户使用情况和record消耗")
        print("4. 查询特定快照的record消耗")
        print("5. 下载历史快照数据到本地")
        print("6. 退出")
        print()
        
        choice = input("请输入选项 (1-6): ").strip()
        
        if choice == "1":
            print("\n" + "="*40)
            print("📥 获取用户帖子")
            print("="*40)
            
            # 获取用户名
            while True:
                username = input("请输入Twitter用户名（可带@符号）: ").strip()
                if username:
                    break
                print("❌ 用户名不能为空，请重新输入")
            
            # 获取帖子数量
            while True:
                max_posts_input = input("请输入最大帖子数量（直接回车表示不限制）: ").strip()
                if not max_posts_input:
                    max_posts = None
                    break
                try:
                    max_posts = int(max_posts_input)
                    if max_posts > 0:
                        break
                    else:
                        print("❌ 帖子数量必须大于0，请重新输入")
                except ValueError:
                    print("❌ 请输入有效的数字")
            
            # 是否等待完成
            while True:
                wait_input = input("是否等待收集完成并自动下载？(y/n，默认y): ").strip().lower()
                if wait_input in ['', 'y', 'yes']:
                    wait_for_completion = True
                    break
                elif wait_input in ['n', 'no']:
                    wait_for_completion = False
                    break
                else:
                    print("❌ 请输入 y 或 n")
            
            print("\n🚀 开始收集...")
            
            # 执行收集
            result = collector.collect_user_posts(
                username=username,
                max_posts=max_posts,
                wait_for_completion=wait_for_completion
            )
            
            if result:
                print("\n📄 收集结果:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                if "id" in result:
                    print(f"\n💾 快照已保存，ID: {result['id']}")
                    if "local_file" in result:
                        print(f"📁 本地文件: {result['local_file']}")
                    else:
                        print("您可以通过BrightData控制台查看和下载数据")
            else:
                print("❌ 收集失败")
        
        elif choice == "2":
            print("\n" + "="*40)
            print("📋 已完成的快照列表")
            print("="*40)
            
            snapshots = collector.get_snapshots()
            
            # 处理响应格式
            snapshots_list = snapshots if isinstance(snapshots, list) else snapshots.get("snapshots", [])
            
            if snapshots_list and len(snapshots_list) > 0:
                print(f"找到 {len(snapshots_list)} 个已完成的快照:\n")
                for i, snapshot in enumerate(snapshots_list, 1):
                    print(f"{i}. ID: {snapshot.get('id')}")
                    print(f"   大小: {snapshot.get('dataset_size')} 条记录")
                    print(f"   时间: {snapshot.get('created')}")
                    print()
                
                # 询问是否下载某个快照
                download_choice = input("是否要下载某个快照？请输入序号（直接回车跳过）: ").strip()
                if download_choice.isdigit():
                    idx = int(download_choice) - 1
                    if 0 <= idx < len(snapshots_list):
                        snapshot_id = snapshots_list[idx]['id']
                        print(f"正在下载快照 {snapshot_id}...")
                        saved_path = collector.download_snapshot_data(snapshot_id)
                        if saved_path:
                            print(f"✅ 已下载到: {saved_path}")
                    else:
                        print("❌ 无效的序号")
            else:
                print("没有找到已完成的快照")
        
        elif choice == "3":
            print("\n" + "="*40)
            print("📊 账户使用情况和record消耗")
            print("="*40)
            
            usage = collector.get_account_usage()
            
            if usage:
                print("✅ 账户使用情况:")
                print(json.dumps(usage, indent=2, ensure_ascii=False))
            else:
                print("❌ 获取账户使用情况失败")
        
        elif choice == "4":
            print("\n" + "="*40)
            print("🔍 特定快照的record消耗")
            print("="*40)
            
            # 先显示可用快照
            snapshots = collector.get_snapshots()
            snapshots_list = snapshots if isinstance(snapshots, list) else snapshots.get("snapshots", [])
            
            if snapshots_list and len(snapshots_list) > 0:
                print("可用快照列表:")
                for i, snapshot in enumerate(snapshots_list[:5], 1):  # 只显示前5个
                    print(f"{i}. ID: {snapshot.get('id')} (大小: {snapshot.get('dataset_size')} 条记录)")
                print()
            
            # 获取快照ID
            while True:
                snapshot_id = input("请输入快照ID: ").strip()
                if snapshot_id:
                    break
                print("❌ 快照ID不能为空，请重新输入")
            
            usage = collector.get_snapshot_usage(snapshot_id)
            
            if usage:
                print("✅ 快照使用情况:")
                print(json.dumps(usage, indent=2, ensure_ascii=False))
            else:
                print("❌ 获取快照使用情况失败")
        
        elif choice == "5":
            print("\n" + "="*40)
            print("📥 下载历史快照数据到本地")
            print("="*40)
            
            # 获取所有快照
            snapshots = collector.get_snapshots()
            snapshots_list = snapshots if isinstance(snapshots, list) else snapshots.get("snapshots", [])
            
            if snapshots_list and len(snapshots_list) > 0:
                print(f"找到 {len(snapshots_list)} 个快照:\n")
                for i, snapshot in enumerate(snapshots_list, 1):
                    print(f"{i}. ID: {snapshot.get('id')}")
                    print(f"   大小: {snapshot.get('dataset_size')} 条记录")
                    print(f"   时间: {snapshot.get('created')}")
                    print()
                
                # 询问是否下载某个快照
                download_choice = input("是否要下载某个快照？请输入序号（直接回车跳过）: ").strip()
                if download_choice.isdigit():
                    idx = int(download_choice) - 1
                    if 0 <= idx < len(snapshots_list):
                        snapshot_id = snapshots_list[idx]['id']
                        print(f"正在下载快照 {snapshot_id}...")
                        saved_path = collector.download_snapshot_data(snapshot_id)
                        if saved_path:
                            print(f"✅ 已下载到: {saved_path}")
                    else:
                        print("❌ 无效的序号")
            else:
                print("没有找到快照")
        
        elif choice == "6":
            print("\n👋 感谢使用，再见！")
            break
        
        else:
            print("❌ 无效选项，请重新选择")
        
        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    main() 