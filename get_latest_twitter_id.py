#!/usr/bin/env python3
"""
自动获取最新的 Twitter SearchTimeline ID
"""
import requests
import re
import time
from urllib.parse import quote

def get_latest_search_timeline_id():
    """获取最新的SearchTimeline ID"""
    
    # 模拟浏览器请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        print("正在获取 Twitter 主页...")
        
        # 访问 Twitter 主页
        response = requests.get('https://twitter.com', headers=headers, timeout=10)
        if response.status_code != 200:
            print(f"访问 Twitter 失败: {response.status_code}")
            return None
            
        # 在页面源码中查找可能的 SearchTimeline ID
        patterns = [
            r'"SearchTimeline"[^}]*?"queryId":"([A-Za-z0-9_-]+)"',
            r'SearchTimeline[^}]*?queryId[^}]*?([A-Za-z0-9_-]{22})',
            r'graphql/([A-Za-z0-9_-]{22})/SearchTimeline',
            r'"([A-Za-z0-9_-]{22})"\s*:\s*\{\s*"queryId"[^}]*SearchTimeline',
        ]
        
        content = response.text
        found_ids = set()
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if len(match) >= 20:  # 过滤太短的匹配
                    found_ids.add(match)
                    print(f"找到候选 ID: {match}")
        
        if found_ids:
            # 返回最长的ID（通常是最完整的）
            best_id = max(found_ids, key=len)
            print(f"选择最佳 ID: {best_id}")
            return best_id
        else:
            print("未在页面中找到 SearchTimeline ID")
            return None
            
    except Exception as e:
        print(f"获取ID时出错: {e}")
        return None

def test_search_timeline_id(schema_id):
    """测试给定的SearchTimeline ID是否有效"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
    }
    
    # 构建测试URL
    test_url = f"https://twitter.com/i/api/graphql/{schema_id}/SearchTimeline"
    
    # 简单的测试参数
    params = {
        'variables': '{"rawQuery":"test","count":1,"product":"Latest"}',
        'features': '{"responsive_web_graphql_exclude_directive_enabled":true}'
    }
    
    try:
        print(f"测试 ID: {schema_id}")
        response = requests.get(test_url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ ID {schema_id} 有效！")
            return True
        elif response.status_code == 404:
            print(f"❌ ID {schema_id} 返回 404")
            return False
        else:
            print(f"⚠️ ID {schema_id} 返回状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"测试ID时出错: {e}")
        return False

def update_snscrape_id(new_id):
    """更新snscrape中的SearchTimeline ID"""
    
    import os
    twitter_py_path = ".venv/Lib/site-packages/snscrape/modules/twitter.py"
    
    if not os.path.exists(twitter_py_path):
        print(f"找不到文件: {twitter_py_path}")
        return False
    
    try:
        # 读取文件
        with open(twitter_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换SearchTimeline URL
        pattern = r'(https://twitter\.com/i/api/graphql/)([A-Za-z0-9_-]+)(/SearchTimeline)'
        
        def replace_func(match):
            return f"{match.group(1)}{new_id}{match.group(3)}"
        
        new_content = re.sub(pattern, replace_func, content)
        
        # 写回文件
        with open(twitter_py_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 已更新 snscrape 中的 SearchTimeline ID 为: {new_id}")
        return True
        
    except Exception as e:
        print(f"更新文件时出错: {e}")
        return False

if __name__ == "__main__":
    print("🔍 正在获取最新的 Twitter SearchTimeline ID...")
    
    # 获取最新ID
    latest_id = get_latest_search_timeline_id()
    
    if latest_id:
        print(f"\n找到候选 ID: {latest_id}")
        
        # 测试ID是否有效
        if test_search_timeline_id(latest_id):
            # 更新snscrape
            if update_snscrape_id(latest_id):
                print(f"\n🎉 成功更新！新的 SearchTimeline ID: {latest_id}")
            else:
                print(f"\n❌ 更新失败，请手动替换 ID: {latest_id}")
        else:
            print(f"\n❌ 获取的 ID 无效，可能需要手动获取")
    else:
        print("\n❌ 未能自动获取 ID，请手动操作：")
        print("1. 打开浏览器访问 twitter.com")
        print("2. 搜索任意关键词") 
        print("3. F12 → Network → 过滤 'SearchTimeline'")
        print("4. 复制URL中的ID并手动替换") 