# 将此文件复制为 .env 并填入您的配置

# Reddit API Credentials
# 访问 https://www.reddit.com/prefs/apps 创建应用获取
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=Resona/1.0 by YourUsername

# DeepInfra API (已填入您的密钥)
DEEPINFRA_API_KEY=OCDjfGAZ0AZDutH2kMU931bbnS5XrLK9

# OpenAI API (备用)
OPENAI_API_KEY=your_openai_api_key

# Database
DATABASE_URL=sqlite:///./resona.db

# App Config
DEBUG=True
PORT=8000
HOST=0.0.0.0

# Vector DB Config
FAISS_INDEX_PATH=./data/faiss_index
EMBEDDING_CACHE_PATH=./data/embeddings_cache 