{"session_info": {"session_id": "detailed_results_20250630_162352_db57c14a", "start_time": "2025-06-30T16:23:52.741894", "end_time": "2025-06-30T16:23:52.741894", "status": "completed", "user_prompt": "测试 datetime 序列化修复", "total_execution_time": 0.0, "version": "2.0"}, "subtask_results": {"测试任务_2025-06-30T16:23:52.741894": {"task_name": "测试任务", "timestamp": "2025-06-30T16:23:52.741894", "execution_time_seconds": 1.234, "status": "success", "data": {"execution_start": "2025-06-30T16:23:52.741894", "test_results": [{"timestamp": "2025-06-30T16:23:52.741894", "result": "success"}, {"timestamp": "2025-06-30T16:23:52.741894", "result": "warning"}], "metadata": {"created_at": "2025-06-30T16:23:52.741894", "version": "test_1.0"}}, "metrics": {"data_size": 368, "data_type": "dict", "timestamp": "2025-06-30T16:23:52.741894"}, "error_info": null}}, "performance_metrics": {"subtask_timings": {"测试任务": 1.234}}, "error_logs": [], "final_results": {"success": true, "completion_time": "2025-06-30T16:23:52.741894", "summary": "datetime 序列化测试成功完成"}}